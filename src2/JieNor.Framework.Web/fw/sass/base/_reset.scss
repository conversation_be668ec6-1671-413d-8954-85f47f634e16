@import "../util/variables";
@import "../component/button";
@import "../util/mixins";
@import "../util/functions";
@import "../util/helpers";
a{
    color: $def-color;
}
.p{
    font-size:$font-size;
}
.red-font{
    color: $red-color !important;
}
.green-font{
    color: $greed-color !important;
}
.body{
    font-size:$button-font-size;
}
.font-active{
    color: $def-color !important;
}
html, body{
    background: $bg-color;
    .page-content {
        @include background($bg-color);
        >.row{
            > .col-md-12{
                padding: {
                    right: 0;
                    left: 0px;
                }
            }
        }
        .tab-content {
            .tab-pane{
                @include background($bg-white-color);
                padding: 10px;
                .text-right{
                    text-align: right !important;
                }
                .text-left{
                    text-align: left !important;
                }
                .text-center{
                    text-align: center !important;
                }
                > .row{
                    margin: 0;
                    > .col-md-12{
                        padding: 0;
                    }
                }
            }
            .tab-pane.first-content{
                padding: 0px;
                @include background($bg-color);
                >.row{
                    .col-md-12{
                        padding-left: 0;
                    }
                }
            }
        }
    }
    .page-container {
        > .page-content-wrapper {
            > .page-content {
                > .row {
                    #tab-content{
                        border: 0;
                        padding:10px;
                        @include background($bg-color);
                    }
                }
            }
        }
    }
}
.changeStatus{
    background: $def-color !important;
}
.yellow-casablanca{
    >.portlet-title{
        >.tools{
            > a.collapse,a.expand{
                background-image: url($allicon-bgimg) !important;
                -webkit-transition: none !important;
                -moz-transition: none !important;
                -o-transition: none !important;
                -ms-transition: none !important;
                transition: none !important;
                float: right;
                margin-top: 5px;
                &.collapse{
                    background-position: -5px -10px;
                }
                &.expand{
                    background-position: -22px -10px;
                }
            }
            .portlet-title-btn{
                border-radius: 3px !important;
                color: $def-color;
                font-size: 13px;
                background-color: #fff;
                border: 1px solid $def-color;
                padding: 5px 14px;
                margin-right: 5px;
                float: right;
                &:hover{
                    background: $defhover-color;
                    border-color: $defhover-color;
                    color: #fff;
                }
            }
            > span{
                line-height: 28px;
            }
        }
    }
}
.save-btn{
    font-size: 14px;
    background: $def-color;
    &:hover{
        background: $defhover-color;
    }
}
.save-btn2{
    font-size: 14px;
    color: $def-color;
    border: 1px $def-color solid !important;
    padding: 0px 15px;
    line-height: 28px;
    outline: none;
    &:hover{
        background: $defhover-color !important;
        border-color: $defhover-color !important;
    }
}
.btn-primary{
    background-color: $def-color;
    border-color: $def-color;
    &:hover{
        background-color: $defhover-color;
        border-color: $defhover-color;
    }
}
.icon-upload{
    background-color: $def-color;
    &:hover{
        background-color: $defhover-color;
    }
}
.portlet{
    .checkbox-list{
        padding-top: 0;
    }
}
