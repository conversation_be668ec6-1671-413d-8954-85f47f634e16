//首页系统菜单
; var Index_SysMenu = {

    //临时存储所有菜单数据
    allMenus: [],

    //临时存储当前被点击的顶部快捷菜单元素
    $menuItem: null,

    //初始化
    init: function () {
        var that = this;

        //加载菜单数据
        yiAjax.p('/dynamic/sys_mainfw?operationno=listmenu&format=json', '', function (r) {

            //渲染菜单
            that.renderMenu(r);

        }, null, null, $('body'));

        //绑定事件
        that.bindEvent();
    },

    //绑定事件
    bindEvent: function () {

        //快捷搜索菜单面板显示隐藏
        var $navBar = $('.page-header.navbar .top-menu .navbar-nav');
        var searchInput = $navBar.find('.dropdown-search-input');
        var searchBox = $navBar.find('.dropdown-search-box');

        //点击快捷搜索图标显示搜索框
        $(document).on('click', function (e) {
            var $target = $(e.target);
            var $button = $('.y-nav-button-search');
            var $input = $button.find('.dropdown-search-input');
            if ($target.hasClass('icon-magnifier') || $target.hasClass('dropdown-search-input')) {
                $button.css({ 'border': '1px #bdc5c9 solid' });
                $input.fadeIn();
            } else {
                $button.css({ 'border': 'none' });
                $input.fadeOut();
            }

            //点击下拉菜单容器之外的地方时自动隐藏下拉菜单容器
            if ($target.closest('.dropdown-search').length < 1 && $target.closest('.dropdown-search-box').length < 1) {
                searchBox.hide();
            }
        });

        //获得焦点
        searchInput.focus(function () {
            searchBox.show();
            searchBox.scrollTop(0);
            searchBox.find(".topmenu").removeClass("active");
            searchBox.find(".topmenu:visible").eq(0).addClass("active");
        });

        //搜索输入框值变化事件
        searchInput.bind("input propertychange", function (event) {
            var keyword = $.trim(searchInput.val()).toUpperCase();
            var $menu = searchBox.find('.topmenu');
            searchBox.scrollTop(0);
            //如果没输入信息则显示所有
            if (!keyword) {
                $menu.show();
                return;
            }
            //过滤标签
            for (var i = 0; i < $menu.length; i++) {
                var $menuItem = $menu.eq(i),
                    pinyin = $menuItem.attr('pinyin'),
                    chinese = $menuItem.attr('chinese'),
                    tpinyin = $menuItem.attr('tpinyin'),
                    tchinese = $menuItem.attr('tchinese');
                if (pinyin.indexOf(keyword) !== -1 || chinese.indexOf(keyword) !== -1 || tpinyin.indexOf(keyword) !== -1 || tchinese.indexOf(keyword) !== -1) {
                    $menuItem.show();
                    searchBox.find(".topmenu").removeClass("active");
                    searchBox.find(".topmenu:visible").eq(0).addClass("active");
                } else {
                    $menuItem.hide();
                }
            }
            if (searchBox.find(".topmenu:visible").length == 0) {
                searchBox.find(".topmenu").removeClass("active");
            }
        });

        //快捷搜索菜单项鼠标悬浮事件
        searchBox.on('hover', '.topmenu', function () {
            searchBox.find(".topmenu").removeClass("active");
            $(this).addClass("active");
        });

        //快捷搜索菜单面板按↑↓键切换焦点，回车即进入所选表单
        searchInput.on('keydown', function (event) {
            //按↓键
            if (event.keyCode == 40) {
                if (searchBox.find(".topmenu:visible").length > 1) {
                    var activeItem = searchBox.find(".topmenu.active:visible"),
                        visibleItem = searchBox.find(".topmenu:visible");
                    for (var i = 0, j = visibleItem.length; i < j; i++) {
                        if (activeItem.attr("menuid") == $(visibleItem[i]).attr("menuid")) {
                            //如果是最后一项则没有改变
                            if (i + 1 == visibleItem.length) {
                                return;
                            }
                            activeItem.removeClass("active");
                            $(visibleItem[i + 1]).addClass("active");
                            searchBox.scrollTop(30 * i);
                            return;
                        }
                    }
                }
            }
            //按↑键
            if (event.keyCode == 38) {
                if (searchBox.find(".topmenu:visible").length > 1) {
                    var activeItem = searchBox.find(".topmenu.active:visible"),
                        visibleItem = searchBox.find(".topmenu:visible");
                    for (var i = 0, j = visibleItem.length; i < j; i++) {
                        if (activeItem.attr("menuid") == $(visibleItem[i]).attr("menuid")) {
                            //如果是第一项则没有改变
                            if (i == 0) {
                                return;
                            }
                            activeItem.removeClass("active");
                            $(visibleItem[i - 1]).addClass("active");
                            searchBox.scrollTop(30 * i);
                            return;
                        }
                    }
                }
            }
            //按回车键
            if (event.keyCode == 13) {
                searchBox.find(".topmenu.active").click();
                searchInput.blur();
            }
        });

        //点击仪表盘菜单切换到仪表盘页签
        $('#dashboard_menu').on('click', function () {
            var dashboardPageId = $.trim($('#tabs .first-tab').attr('id')).replace('tab_', '');
            if (dashboardPageId) {
                TabMgr.getInstance().activate(dashboardPageId);
            }
            $(this).addClass('active');
        });

        //我的企业点击事件
        $('#mycompany').on('click', function () {
            var url = '/bill/sys_company?operationno=view&pageid=' + Index.consts.pageId;
            var param = {
                billData: [],
                simpleData: {
                    getMyCompany: true
                }
            };
            yiAjax.p(url, param, null, null, null, $('body'));
        });
    },

    //渲染菜单
    renderMenu: function (r) {

        //菜单数据
        var data = r.operationResult.srvData;

        //渲染一级菜单
        this.renderClassOne(data);

        //渲染二级菜单
        for (var i = 0; i < data.length; i++) {
            this.renderClassTwo(data[i], i);
        }

        //渲染顶部快捷菜单
        this.renderTopMenu();

        //渲染菜单面板
        this.renderMenuPanel(data);

        //绑定侧边栏菜单事件
        this.bindSidebarMenuEvent();

        //绑定菜单面板事件
        this.bindMenuPanelEvent();
    },

    //绑定侧边栏菜单事件
    bindSidebarMenuEvent: function () {

        //监控每个菜单，在点击每个菜单的时候，打开对于的 tab 页签
        $('.page-sidebar-menu').on('click', '[formId][domainType]', function () {
            var $this = $(this);

            //隐藏下拉导航菜单
            $this.closest('.menu-dropdown').removeClass('open');

            var menuPara = eval('({' + (unescape($this.attr('data-param') || '') + '})'));

            //功能入口标识
            menuPara.__entry = $this.attr('menuid');

            //特殊参数标识：全局参数
            var globalPara = { __gp__: JSON.stringify(menuPara) };

            //打开页签
            Index.openForm({
                formId: $this.attr('formId'),
                domainType: $this.attr('domainType'),
                param: globalPara
            });
        });
    },

    //渲染一级菜单
    renderClassOne: function (data) {
        data = data.sort(yiCommon.sortby('order')); //排序
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var menu = data[i];
            var param = '';
            if (!menu.menuGroups) {
                param = ' url="{0}" domainType="{1}" formId="{2}"'.format(menu.url, menu.domainType, menu.billFormId);
            }
            html += '<li class="menu y-nav-menu-module"><a href="###"{2}><i class="{0}"></i><span class="title">{1}</span></a></li>'.format(menu.icon, menu.name, param);
        }
        $('.page-sidebar-wrapper .page-sidebar ul.page-sidebar-menu').append(html);
    },

    //渲染二级菜单
    renderClassTwo: function (parent, index) {
        var data = parent.menuGroups;
        if (!data || data.length < 1) return;
        data = data.sort(yiCommon.sortby('order'));

        var html = '';
        for (var i = 0; i < data.length; i++) {
            var menu = data[i];
            if (menu.menuItems && menu.menuItems.length > 0) {

                var three = this.renderClassThree(menu);
                html += '<li class="menu-panel-item y-nav-menu-group"><a>{0}</a>{1}</li>'.format(menu.name, three);

                //收集顶部快捷菜单数据
                this.collectTopMenuData(menu, parent.name);

            } else {
                html += '<li  class="menu-panel-item y-nav-menu-group"><a class="iconify" url="{0}" domainType="{1}" formId="{2}">{3}</a></li>'
                    .format(menu, menu.domainType, menu.billFormId, menu.name);
            }
        }
        $('.page-sidebar-wrapper .page-sidebar ul.page-sidebar-menu>li').eq(index + 1).append('<ul class="sub-menu">' + html + '</ul>');
    },

    //渲染三级菜单
    renderClassThree: function (parent) {
        var data = parent.menuItems;
        if (!data || data.length < 1) return '';
        data = data.sort(yiCommon.sortby('order'));
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var menu = data[i];
            html += '<li class="three-menu y-nav-menu-item"><a href="###" class="iconify" url="{0}" domainType="{1}" formId="{2}" data-param="{4}" menuid="{5}">{3}</a></li>'
                .format(menu.url, menu.domainType, menu.billFormId, menu.name, escape(menu.parameter), menu.id);
        }
        return '<ul class="sub-menu">' + html + '</ul>';
    },

    //渲染顶部快捷菜单
    renderTopMenu: function () {

        //去除重复菜单
        var menuData = [];
        var all = this.allMenus;
        for (var i = 0; i < all.length; i++) {
            var exists = false;
            for (var j = 0; j < menuData.length; j++) {
                if (all[i].name == menuData[j].name) {
                    exists = true;
                    break;
                }
            };
            if (!exists) {
                menuData.push(all[i]);
            }
        }

        //渲染顶部快捷搜索菜单
        var searchHtml = '';
        for (var i = 0; i < menuData.length; i++) {
            var menu = menuData[i],
                pinYin = pinyin.getCamelChars(menu.name),
                tpinYin = pinyin.getCamelChars(menu.tname);
            searchHtml +=
                '<div class="topmenu" chinese="{3}" pinyin="{6}" tchinese="{7}" tpinyin="{8}" url="{0}" domainType="{1}" formId="{2}" data-param="{4}" menuid="{5}">\
                    <div class="col-xs-3"><span>{6}</span></div>\
                    <div class="col-xs-4"><b>{3}</b></div>\
                    <div class="col-xs-5">{8}-{7}</div>\
                </div>'.format(menu.url, menu.domainType, menu.billFormId, menu.name, escape(menu.parameter), menu.id, pinYin, menu.tname, tpinYin);
        }
        $('.page-header.navbar .top-menu .navbar-nav .dropdown-search-box').append(searchHtml);
    },

    //收集顶部快捷菜单数据
    collectTopMenuData: function (parent) {
        var data = parent.menuItems;
        if (!data || data.length < 1) return '';
        data = data.sort(yiCommon.sortby('order'));
        var menus = [];
        for (var i = 0; i < data.length; i++) {
            data[i].tname = parent.name;
            menus.push(data[i]);
        }
        this.allMenus = this.allMenus.concat(menus);
    },

    //渲染菜单面板
    renderMenuPanel: function (data) {

        //处理旧版菜单样式
        $('.page-sidebar-wrapper .page-sidebar .page-sidebar-menu li>ul.sub-menu')
            .removeClass()
            .addClass('menu-panel')
            .addClass('y-nav-menu-slide');
        $('.page-sidebar-wrapper .page-sidebar .page-sidebar-menu li ul li>ul').removeClass();

        //定宽
        for (var i = 0; i < data.length; i++) {
            var arr = [];
            var menuGroups = data[i].menuGroups;
            if (menuGroups) {
                for (var j = 0; j < menuGroups.length; j++) {
                    arr.push(menuGroups[j].menuItems.length);
                }
            }
            arr.sort(function sortby(a, b) {
                return a - b;
            });
            var sum = arr[arr.length - 1];
            if (sum > 4) sum = 4;

            var menus = $('.page-sidebar-wrapper .page-sidebar .page-sidebar-menu .menu'),
              width = sum > 1 ? sum * 96 + 'px' : 'auto';
            $(menus[i]).children('ul').children('li').children('ul').css('width', width);
        }
    },

    //绑定菜单面板事件
    bindMenuPanelEvent: function () {
        var that = this;

        $(document).on('click', function (e) {

            var $target = $(e.target);
            var $menu = $target.parents('.menu');
            var $menuPanel = $target.parents('.menu-panel');
            var tagName = $target.get(0).tagName;

            //点击菜单中有子菜单的二级菜单或者其他部分->return不隐藏菜单
            var toClass = $target.attr('class');
            if (!toClass) toClass = $target.parent('.menu-panel-item').attr('class');
            if (toClass && toClass.indexOf('menu-panel-item') != -1 && toClass.indexOf('no-sub') == -1) return;

            //点击非菜单部分->隐藏菜单
            if ($menu.length == 0 && that.$menuItem && that.$menuItem.length > 0 && $menuPanel.length == 0) {
                that.hidePanelMenu();
            }

            //点击菜单中二级、三级菜单->隐藏菜单
            if ($menu.length == 1 && $menuPanel.length == 1 && tagName == 'A') {
                that.hidePanelMenu();
            }
        });

        $('.page-sidebar-menu.page-sidebar-menu-hover-submenu .menu>a').on('click', function () {
            var $this = $(this);
            if ($this.siblings('.menu-panel').css('display') == 'none') {
                that.$menuItem = $this;
                that.showPanelMenu();
            } else {
                that.hidePanelMenu();
                that.$menuItem = null;
            }
        });
    },

    //隐藏菜单面板
    hidePanelMenu: function () {

        //一级菜单非点击状态样式
        this.$menuItem.removeClass('click');

        //菜单面板隐藏
        this.$menuItem.siblings('.menu-panel')
            .css({ 'opacity': '0.5', 'margin-left': '0px', 'z-index': '-1', 'display': 'none' });
    },

    //显示菜单面板 
    showPanelMenu: function () {

        //一级菜单点击状态样式
        $('.page-sidebar-menu.page-sidebar-menu-hover-submenu .menu>a').removeClass('click');
        this.$menuItem.addClass('click');
        $('#dashboard_menu').removeClass('active');

        //菜单面板显示	
        $('.page-sidebar-menu.page-sidebar-menu-hover-submenu .menu .menu-panel')
            .css({ 'display': 'none', 'opacity': '0.5', 'margin-left': '-200px', 'z-index': '-1' });

        var $menuPanel = this.$menuItem.siblings('.menu-panel');
        $menuPanel.css('display', 'block');
        $menuPanel.animate({ 'opacity': '1', 'margin-left': '0px' }, 400);

        setTimeout(function () {
            $menuPanel.animate({ 'z-index': '210' }, 400);
        }, 200);

        //美化列表滚动条
        Index.beautyScrollBar($menuPanel);
    }
};

$(document).ready(function () {

    //首页初始化时先释放所有服务端页面实例
    yiAjax.p('/dynamic/sys_mainfw?operationno=closeall');

    //初始化菜单
    Index_SysMenu.init();
});