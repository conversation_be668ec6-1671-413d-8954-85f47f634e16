//首页顶部用户信息
; var Index_UserBar = {

    //常量
    consts: {
        //存放登录后的用户名
        userName: '',
        passwordId: '#txtPasswordBox',
        pwdDialogId: 'modifypwd-dialog'
    },

    //初始化
    init: function () {
        this.openDashboard('sys_dashboard');
        this.getSysInfo();
        this.loadUserCtxInfo();
        this.getExtAppInfoList();
        this.bindOperate();
    },

    //绑定操作
    bindOperate: function () {

        var that = this;

        //修改密码
        $('#aModifyPwd').click(function () {
            that.showPwdDialog();
        });

        //注销
        $('#aLogout').click(function () {
            $.cookie("login_way", '', { expires: 0, path: '/' });
            Metronic.blockUI({ target: $('body'), animate: true });
            Index.closeAll();
            window.location.href = "/auth/logout";
        });

        //我的令牌
        $('#mytoken').click(function () {
            that.showMyToken();
        });

        //版本介绍
        $('#sysVersionLog').click(function () {
            that.showVersionLog();
        });

        window.addEventListener('click', handlerCloseCompany)
        function handlerCloseCompany(e) {
            let elParent = e.target.parentElement,$dropDownMenu = $('.dropdown-menu-company');
            if (elParent && elParent.className != 'dropdown-menu-company') {
                $('.dropdown-menu-company').hide();
            }
        }
    },

    //显示企业列表
    showCompanyList: function (companys) {
        if (companys && companys.length > 0) {
            var html = '';
            for (var i = 0, l = companys.length; i < l; i++) {
                html += '<li role="presentation"><a role="menuitem" tabindex="-1" href="#" cid="{0}">{1}</a></li>'
                    .format(companys[i].id, companys[i].name);
            }
            $('#ul_company_list').html(html).on('click', function (e) {
                //保存当前切换的组织ID
                var cid = $(e.target).attr('cid'),
                    curr_cid = $('#btn_company_list').attr('cid');
                if (cid !== curr_cid) {
                    //直接通过请求转换会话上下文的企业信息，然后刷新当前页面
                    Metronic.blockUI({ target: $('body'), animate: true });
                    Index.closeAll();
                    window.location.href = '/company/switch/{0}'.format(cid);
                }
            });
        }
    },

    //显示企业列表
    showCompanys: function (curCompany, companys) {

        var $switch = $('#switchEnterprise');
        $switch.attr('cid', curCompany.id);

        var $dropDown = $('.dropdown-company');
        var $dropDownMenu = $('.dropdown-menu-company');

        $dropDown.on('click', function (e) {
            var tagName = $(e.target)[0].tagName.toLowerCase();
            if (tagName === 'input') return;
            if ($dropDownMenu.is(':visible')) {
                $dropDownMenu.hide();
            } else {
                $dropDownMenu.show();
            }
        });

        $dropDown.hover(function () {
            $dropDownMenu.show();
        });

        if (!companys || companys.length < 1) {
            $dropDownMenu.remove();
            return;
        }

        var html = '';
        if (companys.length == 1) {
            $dropDownMenu.remove();
        }

        //当企业大于十个，则显示快捷输入框。
        if (companys.length > 10) {
            $dropDownMenu.find('input').removeClass('hide');
        }

        for (var i = 0; i < companys.length; i++) {
            var company = companys[i];
            if (company.id === curCompany.id) {
                $('#spanCompanyName').text(company.name);
                $dropDown.find('.dropdown-toggle').append('<i class="fa fa-caret-down pull-right"></i>');
            } else {
                html += '<li><a cid="{0}" title="{2}">{1}</a></li>'.format(company.id, company.name, company.name);
            }
        }

        //点击切换企业
        $switch.html(html).on('click', function (e) {

            var cid = $(e.target).attr('cid');
            var curr_cid = $switch.attr('cid');
            if (cid !== curr_cid) {

                //切换企业前先检查用户是否已被禁用
                yiAjax.g('/company/checkuser/{0}'.format(cid), {}, function (r) {

                    //直接通过请求转换会话上下文的企业信息，然后刷新当前页面
                    Metronic.blockUI({ target: $('body'), animate: true });    
                    Index.closeAll();
                    window.location.href = '/company/switch/{0}'.format(cid);

                }, function (m) {
                    yiDialog.error(yiCommon.extract(m));
                }, true, $('body'));
            }
        });

        // 创建防抖函数
        function debounce(fn, delay) {
            let timer = null;
            return function () {
                const context = this;
                const args = arguments;
                clearTimeout(timer);
                timer = setTimeout(() => {
                    fn.apply(context, args);
                }, delay);
            }
        }
        
        const $searchInput = $dropDown.find('input.screen');
        const $listItems = $('.dropdown-company ul.dropdown-menu li');
        // 输入关键字快捷搜索企业
        $searchInput.on('keyup', debounce(function () {
            const val = this.value.trim();

            if (!val) {
                $listItems.show();
                return;
            }

            $listItems.each(function () {
                const $item = $(this);
                $item[($item.text().indexOf(val) > -1 ? 'show' : 'hide')]();
            });
        }, 200)); // 200ms 的防抖延迟

        //输入关键字快捷搜索企业
        /*$dropDown.find('input.screen').on('keyup', function () {
            var val = $(this).val();
            $('.dropdown-company ul.dropdown-menu li').each(function () {
                var $this = $(this);
                if ($.trim(val).length > 0) {
                    var text = $this.text();
                    if (text.indexOf(val) > -1) {
                        $this.show();
                    } else {
                        $this.hide();
                    }
                } else {
                    $this.show();
                }
            });
        });*/
    },

    //获取外部应用信息列表
    getExtAppInfoList: function () {
        yiAjax.p('/dynamic/sys_externalapp?operationno=getallexternalapp', {}, function (r) {
            Consts.extApps = r.operationResult.srvData || [];
        });
    },

    //获取系统信息
    getSysInfo: function () {
        var that = this;

        yiAjax.p('/dynamic/sys_mainfw?operationno=getsysinfo', {}, function (r) {
            var srvd = r && r.operationResult && r.operationResult.srvData;
            if (!srvd) return;

            //临时存储当前登录企业信息
            Consts.loginCompany = srvd.lastActiveCompany || {};

            //公司名称
            if (srvd.lastActiveCompany) {
                $('#btn_company_list')
                    .attr('cid', srvd.lastActiveCompany.id)
                    .html(srvd.lastActiveCompany.name + '&nbsp; <span class="caret"></span>');

                $('#spanCompanyName').text(srvd.lastActiveCompany.name);
            }
            debugger;
            //临时存储当前登录企业信息到yiajax里面;
            var loginCompanyId = $.trim(Consts.loginCompany && Consts.loginCompany.id);
            if (loginCompanyId) {
                window.__companyId = loginCompanyId;
            }
            window.__xuid = $.trim(srvd.userId);

            Consts.topcompanyid = srvd.topCompanyId;
            Consts.istoporg = srvd.isTopOrg;
            Consts.parentCompanyId = srvd.parentCompanyId;
            Consts.isSecondOrg = srvd.isSecondOrg;
            //BI对应的ssoToken
            Consts.ssoToken = srvd.ssoToken;
            //是否直营
            Consts.isdirectsale = srvd.isDirectSale;

            //加载文件服务器配置
            Consts.upApi = srvd.fileServerInfo;
            //自动补全 fsApiUrl 最后的斜杠
            if (Consts.upApi && Consts.upApi.fsApiUrl) {
                if (Consts.upApi.fsApiUrl[Consts.upApi.fsApiUrl.length - 1] !== '/') {
                    Consts.upApi.fsApiUrl += '/';
                }
            }

            //微信网站应用：微信登录
            Consts.wxPcAppId = srvd.wxPcAppId;

            //客服信息
            Consts.chatServers = srvd.chatServerInfo;

            //帮助中心
            var helpCenter = srvd.helpCenter;
            var updateLog = srvd.updateLog;
            var versionNo = srvd.versionNo;
            if (helpCenter) {
                $('#sysHelpCenter').attr('href', helpCenter);
            }
            if (updateLog) {
                $('#sysUpdateLog').attr('href', updateLog);
            }
            if (versionNo) {
                $('#sysVersionLog').text(versionNo + '版本介绍');
            }

            //更新仪表盘页面标题
            var dsbdTitle = $.trim(srvd.dashboardTitle) || '仪表盘';
            $('.page-sidebar-wrapper .start span').text(dsbdTitle);
            $('#tabs .first-tab a').text(dsbdTitle);

            //如果上一步操作是绑定微信账号，则自动打开“我的资料”页面
            var wxIsBind = $.cookie("wx_isbind");
            if (wxIsBind === 'true') {
                $.cookie("wx_isbind", '', { expires: 0, path: '/' });
                var timeId = setTimeout(function () {
                    Index.openForm({
                        formId: 'bd_personal',
                        domainType: Consts.domainType.dynamic
                    });
                    clearTimeout(timeId);
                }, 5000);
            }

            //如果用户是首次登录系统，则强制用户修改密码
            that.firstLoginForcedModifyPwd();

            //显示系统信息
            that.showSysInfo(srvd);
            //异步改同步，目的是为了 window.__companyId赋值，后续请求头包含当前组织id。
        }, null, null, null, { async: false });
    },

    //加载用户上下文信息
    loadUserCtxInfo: function () {
        var that = this;

        yiAjax.p('/dynamic/sys_mainfw?operationno=loaduserctxinfo', {}, function (r) {
            var srvd = r && r.operationResult && r.operationResult.srvData;
            if (!srvd) return;

            Consts.mystaff = srvd.myStaff;
            Consts.mydept = srvd.myDept;
            Consts.mydepts = srvd.myDepts;

            //显示企业列表
            //this.showCompanyList(srvd.companys);

            //设置logo
            //that.setLogo(srvd.companyLogoMax, srvd.companyLogoMix);

            if (srvd.companys.length == 0) {

                $('#switchEnterprise').parent().hide();

            } else {

                //重新组装企业数据
                var srvData = { columnKeys: ['id', 'name'], data: srvd.companys };
                yiCommon.assemblyData(srvData);
                srvd.companys = srvData.data;

                //显示企业列表
                that.showCompanys(srvd.lastActiveCompany, srvd.companys);
                //判断当前用户所属组织在不在列表中，不存在则提示网络波动
                if (!srvd.companys.some(o => o.id === Consts.loginCompany.id)) {
                    yiDialog.warn("系统检测到网络存在波动，请退出后重新登录，谢谢！！！");
                    setTimeout(function () {
                        $.cookie("login_way", '', { expires: 0, path: '/' });
                        Metronic.blockUI({ target: $('body'), animate: true });
                        $(window).unbind('beforeunload');
                        window.onbeforeunload = null;
                        window.location.href = '/';
                    }, 3000);
                }
            }
        });
    },

    //打开仪表盘
    openDashboard: function (dashboardPage) {
        if ($.trim(dashboardPage)) {
            Index.openForm({
                formId: dashboardPage,
                domainType: Consts.domainType.dynamic,
                cp: { dashboardPage: true }
            });
        } else {
            var dsbdPageId = 'e7a49d2412ea4e05a052f7798a5cc623';
            $('#tabs .first-tab')
                .attr({ id: 'tab_' + dsbdPageId })
                .find('a')
                .attr({ 'href': '#' + dsbdPageId, 'aria-controls': dsbdPageId });
            $('.first-content').attr('id', dsbdPageId);
        }
    },

    //如果用户是首次登录系统，则强制用户修改密码
    firstLoginForcedModifyPwd: function () {
        var that = this;
        //微信登录方式不做控制
        var loginWay = $.cookie("login_way");
        if (loginWay === 'wechat') {
            return;
        }
        yiAjax.p('/dynamic/sec_user?operationno=getpwdpolicy', {}, function (r) {
            var result = r && r.operationResult;
            var isSuccess = result && result.isSuccess;
            var srvd = result && result.srvData;
            var needModifyPwd = srvd && srvd.needModifyPwd;
            if (!isSuccess || !needModifyPwd) return;
            var title = "";
            switch (srvd.type) {
                case 1:
                    title = "新用户或被管理员重置密码后，需要修改密码才能使用系统";
                    break;
                case 2:
                    title = "密码已过期，需要修改密码才能使用系统"
                    break;
            }
            that.showPwdDialog(true, title);
        });
    },

    //显示系统信息
    showSysInfo: function (srvd) {
        var that = this;

        //将用户名暂存起来，其他地方有用到
        that.consts.userName = $.trim(srvd.userName);
        Consts.loginUserName = that.consts.userName;

        //将用户Id暂存起来，其他地方有用到
        that.consts.userId = $.trim(srvd.userId);
        Consts.loginUserId = that.consts.userId;

        //最终显示的名称
        var displayName = $.trim(srvd.displayName);
        if (!displayName) {
            displayName = that.consts.userName;
        }

        //获取用户个人资料
        yiAjax.p('/dynamic/sec_user?operationno=getmyprofile', { simpledata: {} }, function (m) {
            var srvd = m.operationResult.srvData;
            var data = srvd && srvd.data;
            debugger;
            if (data)
            { 
                if (Consts.loginCompany.id != data.fmainorgid.id)
                { 
                    yiDialog.warn("系统检测到网络存在波动，请退出后重新登录，谢谢！");
                    setTimeout(function () {
                        $.cookie("login_way", '', { expires: 0, path: '/' });
                        Metronic.blockUI({ target: $('body'), animate: true });
                        $(window).unbind('beforeunload');
                        window.onbeforeunload = null;
                        window.location.href = '/';
                    }, 3000);
                }
                //用户姓名
                var uName = $.trim(data.fname);
                if (uName) {
                    displayName = uName;
                }
                //用户头像
                var uImageId = '';
                if (data.fimage) {
                    uImageId = $.trim(data.fimage.id);
                }
                if (uImageId) {
                    yiAjax.p('/fileinfo', { fileId: uImageId, thumbnail: false }, function (r) {
                        var srvd = r.operationResult.srvData;
                        if (srvd && srvd.length > 0) {
                            var uImgUrl = $.trim(srvd[0].url);
                            if (uImgUrl) {
                                $('#imgUserIcon').attr('src', uImgUrl);
                            }
                        }
                    });
                }
            }
            Consts.loginUserDisplayName = displayName;
            $('#spanUserName').text(displayName);
        });

        //修改title
        if (srvd.productName) {
            document.title = srvd.productName;
        }

        //显示在线客服
        var chats = Consts.chatServers;
        if (chats && chats.length > 0) {
            var chatHtml = '';
            for (var i = 0; i < chats.length; i++) {
                if (chats[i].chatType == '1') {
                    chatHtml += '\
                        <li class="dropdown dropdown-extended">\
                            <a class="qq-chat" href="http://wpa.qq.com/msgrd?v=3&uin=' + chats[i].number + '&site=qq&menu=yes" target="_blank">\
                            <img alt="QQ在线客服" src="/fw/images/qq.png" title="QQ在线客服">&nbsp;' + chats[i].title + '</a>\
                        </li>';
                }
            }
            $(".top-menu ul:first").prepend(chatHtml);
        }
    },

    //根据logoId设置logo图标
    setLogo: function (logoMaxId, logoMixId) {
        logoMaxId = $.trim(logoMaxId);
        logoMixId = $.trim(logoMixId);
        var logoMaxUrl = '/fw/include/admin/layout3/images/logo.png?r=' + Math.random(); //先增加随机数强制去除用户浏览器缓存，后续再去掉该随机数逻辑
        var logoMixUrl = '/fw/include/admin/layout3/images/logo1.png?r=' + Math.random();
        var $logo = $('.page-logo .logo-default');
        var $sidebar = $('.menu-toggler.sidebar-toggler');
        if (logoMaxId || logoMixId) {
            var fileIds = [];
            if (logoMaxId) fileIds.push(logoMaxId);
            if (logoMixId) fileIds.push(logoMixId);
            yiAjax.p('/fileinfo', { fileId: fileIds.toString(), thumbnail: false }, function (r) {
                var srvd = r.operationResult.srvData;
                if (srvd && srvd.length > 0) {
                    for (var i = 0; i < srvd.length; i++) {
                        if (logoMaxId && srvd[i].fileId === logoMaxId) {
                            var _maxUrl = $.trim(srvd[i].url);
                            if (_maxUrl) logoMaxUrl = _maxUrl;
                        }
                        if (logoMixId && srvd[i].fileId === logoMixId) {
                            var _mixUrl = $.trim(srvd[i].url);
                            if (_mixUrl) logoMixUrl = _mixUrl;
                        }
                    }
                    if (logoMaxUrl) {
                        $logo.attr('src', logoMaxUrl);
                    }
                    if (logoMixUrl) {
                        _setSideBarBg();
                    }
                }
            });
        }
        if (!logoMaxId) {
            $logo.attr('src', logoMaxUrl);
        }
        if (!logoMixId) {
            _setSideBarBg();
        }
        $logo.css('visibility', 'visible');

        function _setSideBarBg() {
            if ($logo.css('display') == 'none') {
                $sidebar.css('background-image', 'url(' + logoMixUrl + ')');
            }
            $sidebar.attr('bgurl', logoMixUrl);
        }
    },

    //显示修改密码对话框
    showPwdDialog: function (forced, title) {
        var that = this;
        var dialogOpts = {
            id: that.consts.pwdDialogId,
            type: 1,
            resize: false,
            success: function (layero, index) {
                ModifyPwd.init(index);
            }
        };
        if (forced) {
            $.extend(dialogOpts, {
                title: title,
                area: '550px',
                closeBtn: 0, //不显示关闭按钮
                btn: ['修改'],
                btncls: ['0'],
                yes: function (index, layero) {
                    ModifyPwd.submit(index);
                    return false;
                },
            });
        } else {
            $.extend(dialogOpts, {
                title: '修改密码',
                area: '320px',
                btn: ['取消', '修改'],
                btncls: ['', '0'],
                yes: function (index, layero) {
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    ModifyPwd.submit(index);
                    return false;
                },
            });
        }
        yiAjax.gf('/views/account/modifypwd.html', {}, function (html) {
            dialogOpts.content = html;
            yiDialog.d(dialogOpts);
        });
    },

    //显示我的令牌
    showMyToken: function () {
        yiAjax.g('/dynamic/sys_mainfw?operationno=getmytoken&format=json', '', function (r) {
            var token = r && r.operationResult && r.operationResult.srvData;
            if (token) {
                var conent = '<div style=\'word-wrap:break-word;font-size:14px\'>' + token + '</div>';
                yiDialog.d({
                    id: 'getmytoken_id',
                    type: 1,
                    closeBtn: 1,
                    resize: true,
                    content: conent,
                    title: '我的令牌',
                    area: ['800px', '500px'],
                    btn: ['确定'],
                    btncls: ['0'],
                    yes: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
        });
    },

    //显示版本日志
    showVersionLog: function () {
        yiAjax.p('/dynamic/sys_mainfw?operationno=GetVersionInfo&format=json', '', function (r) {
            var srvData = r && r.operationResult && r.operationResult.srvData;
            var versionNo = $.trim(srvData.versionNo);
            var versionLog = srvData.versionLog || '';
            var conent = '<div style=\'word-wrap:break-word;font-size:14px\'>' + versionLog + '</div>';
            yiDialog.d({
                id: 'getversioninfo_id',
                type: 1,
                closeBtn: 1,
                maxmin: true,
                resize: true,
                content: conent,
                title: versionNo + '版本介绍',
                area: ['900px', '600px'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
        }, function (m) {
            yiDialog.error(yiCommon.extract(m));
        }, null, $('body'));
    }
};

$(document).ready(function () {
    Index_UserBar.init();
});