/// <reference path="/fw/js/basepage.js" />
; (function () {
    var Index_ColorChage = (function () {

        return function () {

            var that = this;

            that.init = function () {
                //替换页面宽度
                that.chageWidth();
            };

            //页面布局宽度替换
            that.chageWidth = function () {
                //Tab页签的宽度
                var Width = $('.tabbable.tabbable-custom.tabbable-noborder.tabbable-reversed').width();
                $('.page-header.page-tabs-fixed .nav.nav-tabs').css('width', Width + 'px');

                $('#chageWidth').toggle(

                    function () {
                        $(".page-container").parent().addClass('container');
                        $('.page-header-inner').addClass('container');
                        $('.page-footer').addClass('container');
                        //设置tab页签宽度
                        var marginWidth = document.querySelectorAll(".page-header.navbar.navbar-fixed-top>.page-header-inner")[0].offsetLeft;
                        $('.page-sidebar-wrapper').css('margin-left', marginWidth);

                        $(window).resize(function () {
                            var marginWidth = document.querySelectorAll(".page-header.navbar.navbar-fixed-top>.page-header-inner")[0].offsetLeft;
                            $('.page-sidebar-wrapper').css({ 'margin-left': marginWidth }, 10);
                        });
                        //Tab页签的宽度
                        var Width = $('.tabbable.tabbable-custom.tabbable-noborder.tabbable-reversed').width();
                        $('.page-header.page-tabs-fixed').css('width', Width + 'px');

                        

                    },
                    function () {
                        $(".page-container").parent().removeClass('container');
                        $('.page-header-inner').removeClass('container');
                        $('.page-footer').removeClass('container');
                        //Tab页签的宽度
                        var Width = $('.tabbable.tabbable-custom.tabbable-noborder.tabbable-reversed').width();
                        $('.page-header.page-tabs-fixed').css('width', Width + 'px');

                        //设置菜单与左侧菜单的距离
                        var containerWidth = $('.page-header.navbar.navbar-fixed-top>.page-header-inner').css('margin-left');
                        $('.page-sidebar-wrapper').css('margin-left', containerWidth);

                       
                    }
                 );
            };
			
			$(".page-sidebar-menu-hover-submenu").slimscroll({
				position: 'right',
				size: '5px',
				radius: '4px',
				alwaysVisible: true,
				height: '92%'
			});
			
			
            //菜单变化时，logo的替换和左边距设置
			if ($('body').hasClass("page-sidebar-closed")) {
			    $('.sidebar-toggler').addClass('logochange');
			    $(".sidebar-toggler").css('cssText', 'float: right;background-image:url(/fw/include/admin/layout3/images/logo1.png) !important;background-size: contain');
			    $('.start.active ').addClass('activeborder');
			    $('.page-container').css('margin-left', '0');
			    $('.y-nav-button-chgskin').css({ 'width': '100%' });
			    $('.y-nav-button-setting').css({ 'width': '100%' });
			} else {
			    $(".sidebar-toggler").css('background-image', '/fw/include/admin/layout3/images/sidebar-toggler1.png');
			    $('.y-nav-button-chgskin').css({ 'width': '50%' });
			    $('.y-nav-button-setting').css({ 'width': '50%' });
			}
            //点击按钮显示隐藏菜单
			$(".dropdown-showdrop").on('click', function () {
			    $(".page-sidebar-wrapper").animate({ 'left': '0px', 'opacity': '1' }, 300);
			});
			$(document).on("click", function (e) {
			    var target = $(e.target);
			    if (target.closest(".page-sidebar-wrapper").length == 0 && target.closest(".dropdown-showdrop").length == 0 && window.innerWidth <= 991) {
			        //点击元素之外的地方触发
			        $(".page-sidebar-wrapper").animate({ 'left': '-250px', 'opacity': '0' }, 300);
			    }
			});
            //点击其他区域隐藏菜单
			$(window).resize(function () {
			    $("page-sidebar-menu").css('margin-right', '0');
			    //				

			    //				
			    if (window.innerWidth > 991) {

			        $(".page-sidebar-wrapper").css({ 'left': '0', 'opacity': '1' });
			    }
			    if (window.innerWidth <= 991) {

			        $(".page-sidebar-wrapper").css({ 'left': '-250px', 'opacity': '0' });
			    }
			});
			$(window).resize();

			$('body').on('click', '.sidebar-toggler', function (e) {
			    var $sidebarToggler = $(this);
			    setTimeout(function () {
			        $(window).resize();
			    }, 1);

			    if ($('body').hasClass("page-sidebar-closed")) {

			        $(".page-logo a").css('display', 'none');
			        $(".page-logo a img").css('display', 'none');

			        //读取小logo图标切换
			        var bgurl = $sidebarToggler.attr('bgurl');
			        $sidebarToggler.addClass('logochange').css('cssText', 'float: right;background-image:url(' + bgurl + ') !important;background-size: contain');

			        $('.page-container').css({ 'margin-left': '0' });
			        $('.navbar-nav').css({ 'margin-right': '85px' });
			        $('.start.active ').addClass('activeborder');
			        $('.y-nav-button-chgskin').css({ 'width': '100%' });
			        $('.y-nav-button-setting').css({ 'width': '100%' });
			        //根据当前页面是否为宽屏状态执行相应调整数值
			    } else {

			        $(".page-logo a").css('display', 'inline-block');
			        $(".page-logo a img").css('display', 'inline-block');

			        //切换logo图标
			        $sidebarToggler.removeClass('logochange').css('cssText', 'float:right;width:10px !important;margin-right:2px !important;background-image:url(/fw/images/all-icon.png) !important;');

			        $('.navbar-nav').css({ 'margin-right': '186px' });
			        $('.page-container').css({ 'margin-left': '170px' });
			        $('.start.active ').removeClass('activeborder');
			        $('.y-nav-button-chgskin').css({ 'width': '50%' });
			        $('.y-nav-button-setting').css({ 'width': '50%' });
			    }
			});

            //白色
            $('#white').on('click', function () {
                //头部背景色和字体样式变换（去掉）
                $('.start a').removeClass('bgblack');
                //调用去掉黑色样式
                that.removeBlack();

                //左边菜单栏样式变换（去掉）
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').removeClass('bgblack')
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').removeClass('bgblue');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu i').removeClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu span').removeClass('fontblack');
                //快捷菜单栏样式变换（去掉）
                $('.page-quick-sidebar-wrapper .page-quick-sidebar').removeClass('bgblack');

                //头部菜单鼠标经过样式变换
                $('.dropdown.dropdown-dark a').hover(
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                        $(this).next().find('.external').removeClass('hoverblack');
                        $(this).find('span').removeClass('fontblack');
                        $(this).next().find('a').removeClass('fontblack');

                        $(this).next().find('li').hover(
                            function () {
                                $(this).siblings('.external').removeClass('hoverblack');
                                $(this).parents('ul').prev().removeClass('hoverblack');
                                $(this).siblings().removeClass('bgblack');
                            }, function () {

                            })
                    },
                    function () {

                    })

                //左边鼠标经过样式变换
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu > li a').hover(
                    function () {
                        $(this).addClass('bgblue');
                        $(this).next().find('a').removeClass('bgblack');
                        $(this).next().find('a').removeClass('fontblack');
                        $(this).next().find('a').hover(
                            function () {
                                $(this).addClass('bgblue');
                                $(this).parents('ul').prev().addClass('fontblack');
                                $(this).parents('ul').prev().addClass('bgblue');
                                $(this).parents('ul').prev().removeClass('hoverblack');
                                $(this).removeClass('hoverblack');
                            },
                            function () {
                                $(this).removeClass('bgblue');
                                $(this).parents('ul').prev().removeClass('fontblack');
                                $(this).parents('ul').prev().removeClass('bgblue')
                            })
                    },
                    function () {
                        $(this).removeClass('bgblue');
                    })
            })
            //黑色
            $('#black').on('click', function () {
                //头部背景色和字体样式变换（添加）
                $('.page-logo').addClass('bgblack');
                $('.page-top').addClass('bgblack');
                $('.top-menu').addClass('bgblack');
                $('.e-home').addClass('bgblack');
                $('.start a').addClass('bgblack');
                $('.company-list button').addClass('bgblack');
                $('.company-list button').addClass('fontblack');
                $('.page-header-inner').addClass('bgblack');
                $('.e-home').children().addClass('fontblack');
                $('.page-logo a img').attr('src', '/fw/include/admin/layout3/images/logo2.png');
                //左边菜单栏样式变换（添加）
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu span').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu i').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').addClass('bgblack');
                //快捷菜单
                $('.page-quick-sidebar-wrapper .page-quick-sidebar').addClass('bgblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post.in .message .arrow').addClass('arrorrightblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post.out .message .arrow').addClass('arrorleftblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post .message').addClass('arrorbgblack');

                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').removeClass('bgblue');

                //头部菜单鼠标经过样式变换
                $('.dropdown.dropdown-dark a').hover(
                    function () {
                        $(this).addClass('hoverblack');
                        $(this).next().find('li').addClass('bgblack');
                        $(this).next().find('.external').removeClass('bgblue');
                        $(this).next().find('.external').addClass('hoverblack');
                        $(this).find('span').addClass('fontblack');
                        $(this).next().find('a').addClass('fontblack');
                        $('.label-icon').removeClass('fontblack')

                        $(this).next().find('li').hover(function () {
                            $(this).find('span#spanUserName').addClass('fontblack');
                            $(this).parents('ul').prev().addClass('hoverblack');
                            $(this).siblings().addClass('bgblack');
                            $(this).siblings('.external').addClass('hoverblack');
                            $(this).siblings('.external').removeClass('bgblack');
                        }, function () {
                            $(this).removeClass('hoverblack');
                            $(this).find('span#spanUserName').removeClass('fontblack');
                            $(this).next().find('li').removeClass('bgblack');
                            $(this).next().find('.external').removeClass('hoverblack');
                        })
                    },
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                        $(this).find('span#spanUserName').removeClass('fontblack');
                    })

                $('.dropdown-menu.pull-right').addClass('bgblack');
                $('.dropdown-menu.pull-right').addClass('blackborder');
                $('.dropdown-menu.pull-right').addClass('fontblack');

                $('.feeds.list-items li').hover(
                    function () {
                        $(this).addClass('hoverblack');
                    },
                    function () {
                        $(this).removeClass('hoverblack')
                    })
                //左边菜单鼠标经过样式变换
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu > li a').hover(
                    function () {
                        $(this).removeClass('bgblue');
                        $(this).addClass('hoverblack');
                        $(this).next().find('a').addClass('fontblack');
                        $(this).next().find('a').addClass('bgblack');
                        $(this).next().find('a').hover(
                            function () {
                                $(this).removeClass('bgblue');
                                $(this).parents('ul').prev().removeClass('bgblue')
                                $(this).parents('ul').prev().addClass('fontblack');
                                $(this).addClass('hoverblack');
                                $(this).parents('ul').prev().addClass('hoverblack');
                            },
                            function () {
                                $(this).removeClass('hoverblack');
                                $(this).parents('ul').prev().removeClass('hoverblack')
                            })
                    },
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                    })
            })
            //灰色
            $('#grey').on('click', function () {
                $('.start a').addClass('bgblack');
                //调用去掉黑色样式
                that.removeBlack();

                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu span').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu i').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').addClass('bgblack')
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').removeClass('bgblue')

                $('.page-quick-sidebar-wrapper .page-quick-sidebar').removeClass('bgblack');

                //头部菜单鼠标经过样式变换
                $('.dropdown.dropdown-dark a').hover(
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                        $(this).next().find('.external').removeClass('hoverblack');
                        $(this).find('span').removeClass('fontblack');
                        $(this).next().find('a').removeClass('fontblack');

                        $(this).next().find('li').hover(
                            function () {
                                $(this).siblings('.external').removeClass('hoverblack');
                                $(this).parents('ul').prev().removeClass('hoverblack');
                                $(this).siblings().removeClass('bgblack');
                            }, function () {

                            })
                    },
                    function () {

                    })

                //左边菜单鼠标经过样式变换
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu > li a').hover(
                    function () {
                        $(this).removeClass('bgblue');
                        $(this).addClass('hoverblack');
                        $(this).next().find('a').addClass('fontblack');
                        $(this).next().find('a').addClass('bgblack');
                        $(this).next().find('a').hover(
                            function () {
                                $(this).removeClass('bgblue');
                                $(this).parents('ul').prev().removeClass('bgblue')
                                $(this).parents('ul').prev().addClass('fontblack');
                                $(this).addClass('hoverblack');
                                $(this).parents('ul').prev().addClass('hoverblack');
                            },
                            function () {
                                $(this).removeClass('hoverblack');
                                $(this).parents('ul').prev().removeClass('hoverblack')
                            })
                    },
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                    })
            })
            //蓝色
            $('#blue').on('click', function () {
                $('.start a').addClass('bgblack');
                //调用去掉黑色样式
                that.removeBlack();

                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu span').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu i').addClass('fontblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').removeClass('bgblack');
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu').addClass('bgblue');

                $('.page-quick-sidebar-wrapper .page-quick-sidebar').removeClass('bgblack');

                //头部菜单鼠标经过样式变换
                $('.dropdown.dropdown-dark a').hover(
                    function () {
                        $(this).removeClass('hoverblack');
                        $(this).next().find('li').removeClass('bgblack');
                        $(this).next().find('.external').removeClass('hoverblack');
                        $(this).find('span').removeClass('fontblack');
                        $(this).next().find('a').removeClass('fontblack');

                        $(this).next().find('li').hover(function () {

                            $(this).parents('ul').prev().removeClass('hoverblack');
                            $(this).siblings('.external').removeClass('hoverblack');
                            $(this).siblings().removeClass('bgblack');
                        }, function () {

                        })
                    },
                    function () {

                    })
                //左边菜单鼠标经过样式变换
                $('.page-sidebar-menu.page-sidebar-menu-hover-submenu > li a').hover(
                    function () {
                        $(this).addClass('bgblue');
                        $(this).addClass('hoverblack');
                        $(this).next().find('a').removeClass('bgblack');
                        $(this).next().find('a').removeClass('fontblack');
                        $(this).next().find('a').hover(
                            function () {
                                $(this).parents('ul').prev().addClass('fontblack');
                                $(this).removeClass('hoverblack');
                                $(this).parents('ul').prev().removeClass('hoverblack');
                            },
                            function () {
                                $(this).parents('ul').prev().removeClass('fontblack');
                            })
                    },
                    function () {
                        $(this).removeClass('bgblue');
                        $(this).removeClass('hoverblack')
                        $(this).next().find('li').removeClass('bgblack');
                    })
            })

            //显示帮助界面
            $('#help_icon').on('click', function () {
                if ($('.helpdropdown-menu').css('display') === "none") {
                    $('.helpdropdown-menu').stop().fadeIn();
                    $('.sys-dashboard').children().eq(0).css({ 'z-index': '0' });
                } else {
                    $('.helpdropdown-menu').stop().fadeOut();
                    setTimeout(function () {
                        $('.sys-dashboard').children().eq(0).css({ 'z-index': '110' });
                    }, 100)
                }
            });

            //点击关闭帮助界面
            $('.icon-close').on('click', function () {
                $('.helpdropdown-menu').stop().fadeOut();
                setTimeout(function () {
                    $('.sys-dashboard').children().eq(0).css({ 'z-index': '110' });
                }, 100)
            });

            //验证邮箱输入的格式是否正确以及正确点击后的效果
            $('.help-email').on('input', function () {
                var $email = $(".help-email"),
					$subBtn = $("#submit_que"),
					reg = /^.{1,}@\w{1,}(\.[a-z]{2,3}){1,2}$/;
                if ($email.val == "") {
                    $subBtn.addClass('subBtn_disable');
                    $subBtn.removeClass('subBtn_correct');

                } else if (reg.test($email.val()) == false) {
                    $subBtn.addClass('subBtn_disable');
                    $subBtn.removeClass('subBtn_correct');

                } else {
                    $subBtn.removeClass('subBtn_disable');
                    $subBtn.addClass('subBtn_correct');
                    $(".subBtn_correct").live('click', function () {
                        $("#submit_que").css("background-color", "#ccc !important").html('发送中');
                        setTimeout(function () {
                            $email.val("");
                            $("#help_text").val("");
                            $subBtn.removeClass('subBtn_correct');
                            $("#submit_que").html('发送');
                            $('.icon-close').click();
                        }, 2000);
                    });
                }

            });

            that.removeBlack = function () {
                //去掉头部黑色样式
                $('.page-logo').removeClass('bgblack');
                $('.page-top').removeClass('bgblack');
                $('.top-menu').removeClass('bgblack');
                $('.e-home').removeClass('bgblack');
                $('.company-list button').removeClass('bgblack');
                $('.company-list button').removeClass('fontblack');
                $('.page-header-inner').removeClass('bgblack');
                $('.e-home').children().removeClass('fontblack');
                //去掉快捷菜单下拉部分黑色样式
                $('.dropdown-menu.pull-right').removeClass('bgblack');
                $('.dropdown-menu.pull-right').removeClass('blackborder');
                $('.dropdown-menu.pull-right').removeClass('fontblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post.in .message .arrow').removeClass('arrorrightblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post.out .message .arrow').removeClass('arrorleftblack');
                //$('.page-quick-sidebar-wrapper .page-quick-sidebar-chat .page-quick-sidebar-chat-user .page-quick-sidebar-chat-user-messages .post .message').removeClass('arrorbgblack');

                $('.page-logo a img').attr('src', '/fw/include/admin/layout3/images/logo.png');

                //去掉快捷菜单下拉部分鼠标经过黑色样式
                $('.feeds.list-items li').hover(
                    function () {
                        $(this).removeClass('hoverblack');
                    },
                    function () {

                    })
            };
        };

    })();

    $(document).ready(function () {

        new Index_ColorChage().init();
    });

})();