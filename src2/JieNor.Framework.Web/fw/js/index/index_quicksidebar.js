var QuickSidebar = function () {

    // 处理任务提示
    var handleQuickSidebarAlerts = function () {
        var wrapper = $('.page-quick-sidebar-wrapper');
        var wrapperAlerts = wrapper.find('.page-quick-sidebar-alerts');

        var initAlertsSlimScroll = function () {
            var alertList = wrapper.find('.page-quick-sidebar-alerts-list');
            var alertListHeight;

            alertListHeight = wrapper.height() - wrapper.find('.nav-justified > .nav-tabs').outerHeight();

            // 提示菜单
            Metronic.destroySlimScroll(alertList);
            alertList.attr("data-height", alertListHeight);
            Metronic.initSlimScroll(alertList);
        };

        initAlertsSlimScroll();
        Metronic.addResizeHandler(initAlertsSlimScroll); // reinitialize on window resize
    };

    // 处理设值
    var handleQuickSidebarSettings = function () {
        var wrapper = $('.page-quick-sidebar-wrapper');
        var wrapperAlerts = wrapper.find('.page-quick-sidebar-settings');

        var initSettingsSlimScroll = function () {
            var settingsList = wrapper.find('.page-quick-sidebar-settings-list');
            var settingsListHeight;

            settingsListHeight = wrapper.height() - wrapper.find('.nav-justified > .nav-tabs').outerHeight();

            // alerts list 
            Metronic.destroySlimScroll(settingsList);
            settingsList.attr("data-height", settingsListHeight);
            Metronic.initSlimScroll(settingsList);
        };

        initSettingsSlimScroll();
        Metronic.addResizeHandler(initSettingsSlimScroll); // reinitialize on window resize
    };


   // //当超级管理员广播消息的时候，弹出侧边栏，激活通知列签，并且显示广播的信息
   // var broastCast = function () {
   //     //侧边栏的对象
   //     var $navJustified = $('.nav-justified');
   //     var loghub = $.connection.log;
   //     var imhub = $.connection.jieNorIMServer;

   //     if (imhub == undefined) {
   //         //如果检测到没有该对象，证明文件没有被导入，测试代码，后期删除。
   //         return false;
   //     }

   //     //后台设计的im通道,是为了广播消息而设计的,通过微软的signalr功能,imhub.client.resmes里面是广播的信息
   //     imhub.client.onRecvSystemMessage = function (code, messages) {
   //         if (!messages) {
   //             return;
   //         }

   //         //功能实现要求: 当超级管理员广播消息的时候，弹出侧边栏，激活通知列签，并且显示广播的信息
   //         //弹出侧边栏
   //         $('body').addClass('page-quick-sidebar-open');

   //         for (var i = 0; i < messages.length; i++) {
   //             var msg = messages[i];
   //             if (!msg) continue;

   //             //广播的具体信息
   //             var $sysMessage = $(
   //             '<li>\
			//    <div class="col1">\
		 //           <div class="cont">\
		 //               <div class="cont-col1">\
		 //                   <div class="label label-sm label-warning">\
		 //                       <i class="fa fa-bell-o"></i>\
		 //                   </div>\
		 //               </div>\
		 //               <div class="cont-col2">\
		 //                   <div class="desc"></div>\
		 //               </div>\
		 //           </div>\
		 //       </div>\
		 //       <div class="col2">\
		 //       	<div class="date"></div>\
		 //       </div>\
			//</li>');
   //             //填充消息
   //             if (msg) {
   //                 $sysMessage.find('div.desc').text(msg.message);
   //                 $sysMessage.find('div.date').text(msg.cDate);
   //             }

   //             //因为不管有没有广播消息,广播的提示始终是存在的,所以为广播设置一个标识.即attr-showSys=sysMessage
   //             //标识信息的后面就是广播的具体信息的位置.
   //             $navJustified.find('ul[attr-showSys=sysMessage]')
   //             //添加消息
   //             .append($sysMessage);
   //         }
   //     };

   //     imhub.client.onRecvBusinessMessage = function (callId, messages) {
   //         if (!messages) {
   //             return;
   //         }

   //         //功能实现要求: 当超级管理员广播消息的时候，弹出侧边栏，激活通知列签，并且显示广播的信息
   //         //弹出侧边栏
   //         $('body').addClass('page-quick-sidebar-open');

   //         for (var i = 0; i < messages.length; i++) {
   //             var msg = messages[i];
   //             if (!msg) continue;

   //             //广播的具体信息
   //             var $sysMessage = $(
   //             '<li>\
			//    <div class="col1">\
		 //           <div class="cont">\
		 //               <div class="cont-col1">\
		 //                   <div class="label label-sm label-warning">\
		 //                       <i class="fa fa-bell-o"></i>\
		 //                   </div>\
		 //               </div>\
		 //               <div class="cont-col2">\
		 //                   <div class="desc"></div>\
		 //               </div>\
		 //           </div>\
		 //       </div>\
		 //       <div class="col2">\
		 //       	<div class="date"></div>\
		 //       </div>\
			//</li>');
   //             //填充消息
   //             if (msg) {
   //                 $sysMessage.find('div.desc').text(msg.subject);
   //                 $sysMessage.find('div.date').text(msg.cDate);
   //             }

   //             //因为不管有没有广播消息,广播的提示始终是存在的,所以为广播设置一个标识.即attr-showSys=sysMessage
   //             //标识信息的后面就是广播的具体信息的位置.
   //             $navJustified.find('ul[attr-showSys=sysMessage]')
   //             //添加消息
   //             .append($sysMessage);
   //         }
   //     };
   // };

    return {
        init: function () {
            //layout handlers
            //控制弹出和弹入
            handleQuickSidebarAlerts(); // 系统消息
            handleQuickSidebarSettings(); // 设置
            //broastCast();//侧边栏效果
        }
    };
}();
$(document).ready(function () {

    QuickSidebar.init();
});