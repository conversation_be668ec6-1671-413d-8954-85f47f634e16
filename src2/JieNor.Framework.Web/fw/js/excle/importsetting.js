/// <reference path="/fw/js/basepage.js" />
//@ sourceURL=/fw/js/excle/importsetting.js
; (function () {
    var importsetting = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

        };
        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.GetXlsFiled = function (that, xlsfile) {
            that.Model.blockUI({ id: '#page#' });
            var packet = {
                billdata: JSON.stringify(that.Model.clone()),
                simpledata: {
                    url: xlsfile
                }
            };

            yiAjax.p('/dynamic/importsetting?operationno=ImportSettingXlsFld', packet,
               function (r) {
                   
                   if (r.operationResult && r.operationResult.isSuccess) {
                       var data = r.operationResult.srvData;
                       that.Model.setComboData({ id: "fxlsfldcaption", data: "", clearData: true });

                       var cData = [];
                       for (var i = 0; i < data.length; i++) {
                           var sd = { "id": data[i], "name": data[i] };
                           cData.push(sd);
                       }

                       that.Model.setComboData({ id: "fxlsfldcaption", data: cData });

                   }
                   else {
                       if (r.operationResult.simpleMessage != null && r.operationResult.simpleMessage != '') {
                           yiDialog.a(r.operationResult.simpleMessage);
                       }
                   }
                   that.Model.refreshEntry({ id: "ffldmapentity" });
                   that.Model.unblockUI({ id: '#page#' });
               },
               function (er) {
                   that.Model.unblockUI({ id: '#page#' });
               });
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "ftmplfile":
                    
                    var xlsfile = that.Model.getSimpleValue({ id: "ftmplfile" });
                    if (!xlsfile) {
                        return;
                    }

                    that.GetXlsFiled(that, xlsfile);
                    break;
            }
        };



        _child.prototype.onElementClick = function (args) {
            var that = this;
            switch (args.id) {
                case "automap":
                    var xlsfile = that.Model.getSimpleValue({ id: "ftmplfile" });
                    if (!xlsfile) {
                        yiDialog.a("未选择模板文件!");
                        return;
                    }
                    that.Model.blockUI({ id: '#page#' });
                    var formid = that.Model.getSimpleValue({ id: "fbizformid" });
                    var packet = {
                        billdata: JSON.stringify(that.Model.clone()),
                        simpledata: {
                            formid: formid,
                            url: xlsfile
                        }
                    };

                    yiAjax.p('/dynamic/importsetting?operationno=ImportSettingAutoMap', packet,
                       function (r) {
                           
                           if (r.operationResult && r.operationResult.isSuccess) {
                               var redata = r.operationResult.srvData;
                               var ffindentity = redata.ffindentity;
                               that.Model.deleteEntryData({ id: "ffindentity" });
                               for (var i = 0; i < ffindentity.length; i++) {
                                   that.Model.addRow({ id: "ffindentity", data: ffindentity[i] });
                               }

                               //that.Model.refreshEntry({ id: "ffindentity" });

                               var ffldmapentity = redata.ffldmapentity;
                               var oldffldmapentity = that.Model.getEntryData({ id: "ffldmapentity" });
                               for (var i = 0; i < ffldmapentity.length; i++) {
                                   for (var j = 0; j < oldffldmapentity.length; j++) {
                                       
                                       if (ffldmapentity[i].ffldkey == oldffldmapentity[j].ffldkey) {
                                           
                                           that.Model.setValue({ id: "fxlsfldcaption", row: oldffldmapentity[j].id, value: { id: ffldmapentity[i].fxlsfldCaption, fname: ffldmapentity[i].fxlsfldCaption } });
                                       }
                                   }

                               }

                               that.Model.refreshEntry({ id: "ffldmapentity" });
								//如果有匹配的excel字段，后面自动勾选
                               var _oldffldmapentity = that.Model.getEntryData({ id: "ffldmapentity" });
                               
                               for(var i=0;i<_oldffldmapentity.length; i++){
                                    var lm =_oldffldmapentity[i];
                                    if(lm.fxlsfldcaption && $.trim(lm.fxlsfldcaption.id)){
                                        
                                        that.Model.setValue({ id: "fisimport", row: lm.id, value:true });
                                    }
                                    
                               }
                               yiDialog.mt({ msg: "匹配成功!", skinseq: 4 });
                           }
                           else {
                               if (r.operationResult.simpleMessage != null && r.operationResult.simpleMessage != '') {
                                   yiDialog.a(r.operationResult.simpleMessage);
                               }
                           }

                           that.Model.unblockUI({ id: '#page#' });
                       },
                       function (er) {
                           that.Model.unblockUI({ id: '#page#' });
                       });
                    args.result = true;
                    break;
            }
        }

        _child.prototype.onInitialized = function (args) {
            var that = this;
            var xlsfile = that.Model.getSimpleValue({ id: "ftmplfile" });
            if (!xlsfile) {
                return;
            }

            that.GetXlsFiled(that, xlsfile);
            var oldffldmapentity = that.Model.getEntryData({ id: "ffldmapentity" });
            for (var j = 0; j < oldffldmapentity.length; j++) {

                that.Model.setValue({ id: "fxlsfldcaption", row: oldffldmapentity[j].id, value: { id: oldffldmapentity[j].fxlsfldcaption.id, fname: oldffldmapentity[j].fxlsfldcaption.id } });

            }

        }

        _child.prototype.onLoadDropData = function (e) {
            var that = this;
            if (!e.id) { return; }
            var _field = that.uiForm.getField(e.id);
            if (_field && _field.controlFieldKey) {
                var bizFormId = that.Model.getSimpleValue({ id: _field.controlFieldKey, row: e.row, prow: e.prow });
                var ffindkey = that.Model.getValue({ id: 'ffindkey', row: e.row, prow: e.prow });
                var headBizFormId = '';
                var headFieldKey = '';
                switch (e.id) {
                    case 'fsrcformid':
                        if (e.row) {

                        }
                        break;
                }
                getBizFormField({
                    pageId: that.formContext.pageId,
                    domainType: that.formContext.domainType,
                    formId: that.formContext.formId,
                    headBizFormId: headBizFormId,
                    headFieldKey: headFieldKey,
                    bizFormId: bizFormId,
                    fieldKey: e.id,
                    entryKey: ffindkey || '',
                    ignoreRefPro:true,
                    success: e.callback
                });
            }
        };

        return _child;
    })(BasePlugIn);
    window.importsetting = window.importsetting || importsetting;
})();