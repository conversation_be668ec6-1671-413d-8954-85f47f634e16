/// <reference path="/fw/js/BasePlugIn.js" />
/*
    @ sourceURL=/fw/js/excle/excleimporttemp.js
*/
; (function () {
    var excleimporttemp = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //值改变
        _child.prototype.onFieldValueChanged = function (args) {
            var that = this;
            switch (args.id) {
                case "ffile":
                case "fbeginrow":
                case "fbegincol":
                    that.Model.deleteEntryData({ id: "fentity" });
                    that.Model.setComboData({ id: "fexclefields", data: "", clearData: true });
                    var fworkobject = that.Model.getSimpleValue({ id: "fworkobject" });
                    var fbeginrow = that.Model.getValue({ id: "fbeginrow" });
                    var fbegincol = that.Model.getValue({ id: "fbegincol" });
                    var ffile = that.Model.getSimpleValue({ id: "ffile" });
                    if (args.id == "ffile") {
                        ffile = args.value.id;
                    } else if (args.id == "fbeginrow") {
                        fbeginrow = args.value;
                    } else {
                        fbegincol = args.value;
                    }
                    if (fworkobject && ffile) {
                        that.getFilePath(ffile, fworkobject, that.getCombos, that, fbeginrow, fbegincol);
                    }
                    //that.Model.addRow({ id: "fentity" });
                    break;
            }
        };

        _child.prototype.getFilePath = function (fileId, fworkobject, callBack, that, beginRow, beginCol) {
            var postUrl = Consts.upApi.fsApiUrl + "FileInfo/FetchFiles";
            var postFileId = {
                FileIds: fileId
            };
            $.ajax({
                url: postUrl,
                type: "POST",
                dataType: "json",
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify(postFileId),
                success: function (data) {
                    var fileUrlPath = Consts.upApi.localFsApiUrl || Consts.upApi.fsApiUrl;
                    fileUrlPath = fileUrlPath + '/FileInfo/GetFile?fileId=' + fileId+'&fileName=""';
                    callBack(fileId, fworkobject, that, beginRow, beginCol);
                }
            });
        };

        _child.prototype.getCombos = function (fileId, formId, that, beginRow, beginCol) {
            that.Model.blockUI({ id: '#page#' });
            var packet = {
                simpledata: {
                    formId: formId,
                    fileId: fileId,
                    beginRow: beginRow,
                    beginCol: beginCol
                }
            };
            yiAjax.p('/dynamic/excleimporttemp?operationno=getcombos', packet, function (r) {
                var data = r.operationResult.srvData;
                if (data) {
                    that.Model.setComboData({ id: "fbillfields", data: data.fieldName });
                    that.Model.setComboData({ id: "fexclefields", data: data.colName });
                    var dataSource = that.Model.getEntryData({ id: "fentity" });
                    if (!dataSource || dataSource.length == 0) {
                        if (data.fieldName) {
                            for (var i = 0; i < data.fieldName.length; i++) {
                                var enData = { "fbillfields": { id: data.fieldName[i].id, fname: data.fieldName[i].name, fnumber: data.fieldName[i].name }, "fexclefields": { id: "", fname: "", fnumber: "" } };
                                that.Model.addRow({ id: "fentity", data: enData });
                            }
                            that.Model.refreshEntry({ id: "fentity" });
                        }
                        that.Model.unblockUI({ id: '#page#' });
                        return;
                    }
                    for (var i = 0; i < dataSource.length; i++) {
                        for (var j = 0; j < data.fieldName.length; j++) {
                            if (dataSource[i].fbillfields.id == data.fieldName[j].id) {
                                dataSource[i].fbillfields.fname = data.fieldName[j].name;
                                dataSource[i].fbillfields.fnumber = data.fieldName[j].name;
                                break;
                            }
                        }
                        for (var k = 0; k < data.colName.length; k++) {
                            if (dataSource[i].fexclefields.id == data.colName[k].id) {
                                dataSource[i].fexclefields.fname = data.colName[k].name;
                                dataSource[i].fexclefields.fnumber = data.colName[k].name;
                                break;
                            }
                        }
                    }
                    that.Model.refreshEntry({ id: "fentity" });
                    that.Model.unblockUI({ id: '#page#' });
                }
            });
        }

        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var fworkobject = that.Model.getSimpleValue({ id: "fworkobject" });
            var fbeginrow = that.Model.getValue({ id: "fbeginrow" });
            var fbegincol = that.Model.getValue({ id: "fbegincol" });
            var ffile = that.Model.getSimpleValue({ id: "ffile" });
            if (!ffile) {
                return;
            }
            if (fworkobject) {
                that.getFilePath(ffile, fworkobject, that.getCombos, that, fbeginrow, fbegincol);
            }
        }


        //按钮点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (args.opcode == "tempsave") {
                var fworkobject = that.Model.getSimpleValue({ id: "fworkobject" });
                var ffile = that.Model.getSimpleValue({ id: "ffile" });
                var fbeginrow = that.Model.getValue({ id: "fbeginrow" });
                var fbegincol = that.Model.getValue({ id: "fbegincol" });
                var dataSource = that.Model.getEntryData({ id: "fentity" });
                var obj = new Object();
                obj.formid = fworkobject;
                obj.ffile = ffile;
                obj.fbeginrow = fbeginrow;
                obj.fbegincol = fbegincol;
                obj.fentity = [];
                for (var i = 0; i < dataSource.length; i++) {
                    var entity = new Object();
                    entity.rowid = dataSource[i].frowid;
                    entity.Billfields = dataSource[i].fbillfields;
                    entity.Exclefields = dataSource[i].fexclefields;
                    obj.fentity.push(entity);
                }
                args.param.datas = JSON.stringify(obj);
            }

            if (args.opcode == "excelimporttmpl") {
                args.param.formId = that.Model.getSimpleValue({ id: "fworkobject" });
            }
        }

        return _child;
    })(BasePlugIn);
    window.excleimporttemp = window.excleimporttemp || excleimporttemp;
})();