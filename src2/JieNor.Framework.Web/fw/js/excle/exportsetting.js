/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
/*
    @ sourceURL=/fw/js/excle/exportsetting.js
*/
;
(function () {
    var exportsetting = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.onFieldValueChanged = function (args) {
            var that = this;
            var fbyselectrow = that.Model.getValue({ id: "fbyselectrow" });
            var fbycurrentpage = that.Model.getValue({ id: "fbycurrentpage" });
            var fbyallpage = that.Model.getValue({ id: "fbyallpage" });

            var fbylist = that.Model.getValue({ id: "fbylist" });
            var fbytemplete = that.Model.getValue({ id: "fbytemplete" });
            var fbyimport = that.Model.getValue({ id: "fbyimport" });
            switch (args.id) {
                case "fbyselectrow":
                    if (fbyselectrow == true) {
                        if (fbycurrentpage == true || fbyallpage == true) {
                            that.Model.setValue({ id: "fbycurrentpage", value: false });
                            that.Model.setValue({ id: "fbyallpage", value: false });
                        }
                    }
                    else {
                        if (fbycurrentpage == false && fbyallpage == false) {
                            that.Model.setValue({ id: "fbycurrentpage", value: true });
                        }
                    }
                    break;
                case "fbycurrentpage":
                    if (fbycurrentpage == true) {
                        if (fbyselectrow == true || fbyallpage == true) {
                            that.Model.setValue({ id: "fbyselectrow", value: false });
                            that.Model.setValue({ id: "fbyallpage", value: false });
                        }
                    }
                    else {
                        if (fbyselectrow == false && fbyallpage == false) {
                            that.Model.setValue({ id: "fbyselectrow", value: true });
                        }
                    }
                    break;
                case "fbyallpage":                    
                    if (fbyallpage == true) {
                        if (fbyselectrow == true || fbycurrentpage == true) {
                            that.Model.setValue({ id: "fbyselectrow", value: false });
                            that.Model.setValue({ id: "fbycurrentpage", value: false });
                        }
                    }
                    else {
                        if (fbyselectrow == false && fbycurrentpage == false) {
                            that.Model.setValue({ id: "fbycurrentpage", value: true });
                        }
                    }
                    break; 


                case "fbylist":                    
                    if (fbylist == true ) {
                        if(fbytemplete == true || fbyimport == true)
                        { 
                            that.Model.setValue({ id: "fbytemplete", value: false });
                            that.Model.setValue({ id: "fbyimport", value: false });
                        }
                    }
                    else {
                        if (fbytemplete == false && fbyimport == false) {
                            that.Model.setValue({ id: "fbyimport", value: true });
                        }
                    }

                    break;
                case "fbytemplete": 
                    if (fbytemplete == true) {
                        that.Model.setEnable({ id: 'fbytempleteid', way: 2, value: true });
                        if (fbylist == true || fbyimport == true) {
                            that.Model.setValue({ id: "fbylist", value: false });
                            that.Model.setValue({ id: "fbyimport", value: false });
                        }
                    }
                    else {
                        that.Model.setEnable({ id: 'fbytempleteid', way: 2, value: false });
                        if (fbylist == false && fbyimport == false) {
                            that.Model.setValue({ id: "fbylist", value: true });
                        }
                    }
                    break;
                case "fbyimport": 
                    if (fbyimport == true) {
                        if (fbylist == true || fbytemplete == true) {
                            that.Model.setValue({ id: "fbylist", value: false });
                            that.Model.setValue({ id: "fbytemplete", value: false });
                        }
                    }
                    else {
                        if (fbylist == false && fbytemplete == false) {
                            that.Model.setValue({ id: "fbylist", value: true });
                        }
                    }
                    break;

                case "fbyexcel":
                    var fbyexcel = that.Model.getValue({ id: "fbyexcel" });
                    var fbypdf = that.Model.getValue({ id: "fbypdf" });
                    if (fbyexcel == true && fbypdf == true) {
                        that.Model.setValue({ id: "fbypdf", value: false });
                    }
                    else if (fbyexcel == false && fbypdf == false) {
                        that.Model.setValue({ id: "fbypdf", value: true });
                    }
                    break;
                case "fbypdf": 
                    var fbyexcel = that.Model.getValue({ id: "fbyexcel" });
                    var fbypdf = that.Model.getValue({ id: "fbypdf" });
                    if (fbyexcel == true && fbypdf == true)
                    {
                        that.Model.setValue({ id: "fbyexcel", value: false });
                    }
                    if (fbyexcel == false && fbypdf == false) {
                        that.Model.setValue({ id: "fbyexcel", value: true });
                    }
                    break;
            }
        }


        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            switch (args.opcode) {
                case 'exportdata':
                    var fbytemplete = that.Model.getValue({ id: "fbytemplete" });
                    var fbytempleteid = that.Model.getSimpleValue({ id: "fbytempleteid" });
                    if (fbytemplete == true && !fbytempleteid) {
                        args.result = true;
                        yiDialog.a('按套打模板导出时，必须选择对应的套打模板！');
                        return;
                    }
                    that.Model.blockUI({ id: '#page#' });
                    var packet = {
                        billdata: JSON.stringify(that.Model.clone()),
                        simpledata: {
                            fbytempleteid: fbytempleteid
                        }
                    };
                    yiAjax.p('/dynamic/exportsetting?operationno=exportdata', packet,
                        function (r) {
                            //成功后回调函数
                            if (args.extra && $.isFunction(args.extra.success)) {
                                args.extra.success(r);
                            }
                            //后端返回的文件名
                            var exportUrl = $.trim(r.operationResult.srvData);
                            if (exportUrl) {
                                yiCommon.downloadFile({
                                    url: '/download/bas_filedetail',
                                    local: true,
                                    data: {
                                        exportUrl: exportUrl
                                    },
                                    error: function () {
                                        yiDialog.error('导出失败！');
                                    }
                                });
                            }
                            that.Model.unblockUI({ id: '#page#' });
                        }, function (er) {
                            that.Model.unblockUI({ id: '#page#' });
                        }, null, $(args.pageSelector));
                    args.result = true;                    
                    break;
            }
        }


         

        return _child;
    })(BasePlugIn);
    window.exportsetting = window.exportsetting || exportsetting;
})();