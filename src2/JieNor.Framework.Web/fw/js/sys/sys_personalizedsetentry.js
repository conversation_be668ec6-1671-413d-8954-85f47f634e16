/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/sys/sys_personalizedsetentry.js
*/
; (function () {
    var sys_personalizedsetentry = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
            that.isChanged = false;
            that.styleChange = false;
            that.styleLoad = false;
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.basicSetId = 'fbasicset';
        _child.prototype.fieldsortId = 'ffieldsort';

        //初始化动态表单插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            //样式设置容器
            that.$styleset = that.Model.getEleMent({ id: '.y-styleset' });

            var cp = that.formContext.cp;
            if (cp) {
                that.Model.setPageCaption({ caption: cp.fbizformid.fname + ' - ' + cp.fentityid.fname + ' - 个性化设置' });
                that.Model.setStyle({ id: '.form-horizontal', value: { 'padding-bottom': '10px' } });
                that.parentPage = Index.getPage(cp.parentPageId);
            }
            that.InitItem();
        };

        _child.prototype.InitItem = function () {
            var that = this;
            var $dropDown = $('.moveto');
            var $dropDownMenu = $('.movediv');
            var moveup = $('#tbMoveUp');
            moveup.click(function () {

                that.moveup();
                that.isChanged = true;
            })
            var movedown = $('#tbMoveDown');
            movedown.click(function () {

                that.movedown();
                that.isChanged = true;
            })
            var moveto = $('#tbMoveTo');
            moveto.click(function () {

                that.moveto();
                that.isChanged = true;
            })
            $dropDown.hover(function () {
                $dropDownMenu.show();
            }, function () {
                $dropDownMenu.hide();
            });
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //基本设置
                case 'basicset':
                    e.result = true;

                    break;
                case 'fieldsort':
                    e.result = true;
                    that.Model.deleteEntryData({ id: that.fieldsortId });
                    var rowIndex = 0;
                    var basicSetEntry = that.Model.getEntryData({ id: that.basicSetId });
                    var fieldsortEntry = that.Model.getEntryData({ id: that.fieldsortId });
                    var entrys = [];
                    for (var i = 0; i < basicSetEntry.length; i++) {
                        if (basicSetEntry[i].fshow && basicSetEntry[i].ffieldname && !basicSetEntry[i].fexpanded) {
                            var row = {};
                            row.fseq_e = basicSetEntry[i].fdisporder;
                            row.ffieldkey_s = basicSetEntry[i].ffieldid;
                            row.ffieldid_s = basicSetEntry[i].ffieldid;
                            row.ffieldtype_s = basicSetEntry[i].ffieldtype;
                            row.ffieldname_s = basicSetEntry[i].ffieldname;
                            rowIndex++;
                            entrys.push(row);
                        }
                    }

                    if (entrys.length > 1) {
                        let max = entrys.sort((a, b) => a.fseq_e - b.fseq_e);
                        let maxseq = max[max.length - 1].fseq_e;
                        for (var i = 0; i < entrys.length; i++) {
                            if (entrys[i].fseq_e == 0) {
                                entrys[i].fseq_e = maxseq + (i + 1);
                            }
                        }
                    }

                    entrys.sort(function (a, b) {
                        // 先按 优先级 字段排序
                        if (a.fseq_e < b.fseq_e) {
                            return -1;
                        }
                        if (a.fseq_e > b.fseq_e) {
                            return 1;
                        }
                        return 0;
                    });
                    //重置顺序
                    if (entrys[0].fseq_e != 1) {
                        for (var i = 0; i < entrys.length; i++) {
                            entrys[i].fseq_e = (i + 1);
                        }
                    }
                    for (var i = 0; i < entrys.length; i++) {
                        var row = that.Model.addRow({ id: that.fieldsortId, data: entrys[i] });
                    }
                    that.Model.setVisible({ id: '.y-showcol', value: true });
                    break;
                //样式设置
                case 'styleset':
                    e.result = true;
                    if (!that.styleLoad) {
                        var bizFormId = that.Model.getSimpleValue({ id: 'fbizformid' });
                        that.Model.invokeFormOperation({
                            id: 'getlistcolorsetting',
                            opcode: 'getlistcolorsetting',
                            param: {
                                formId: 'sys_mainfw',
                                domainType: Consts.domainType.bill,
                                bizFormId: bizFormId,
                                entityKey: that.formContext.cp.fentityid.id
                            }
                        });
                    }
                    break;
                //取消
                case 'pagecancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确定
                case 'pageconfirm':
                    e.result = true;

                    //结束表格编辑状态
                    that.Model.endEditEntry({ id: that.basicSetId });

                    var temps = [];
                    var styles = that.getStyleSet();
                    for (var i = 0; i < styles.length; i++) {
                        var hash = styles[i].fldKey + styles[i].opSymbol + styles[i].fldValue;
                        if (temps.indexOf(hash) > -1) {
                            yiDialog.error('不允许设置重复的字段条件值，请检查样式设置！');
                            return;
                        }
                        temps.push(hash);
                    }

                    var data = that.Model.clone();
                    data.fstyleset = styles;

                    //设置对话框的返回数据
                    that.Model.setReturnData({
                        isSuccess: true,
                        isChanged: that.isChanged,
                        styleChange: that.styleChange,
                        data: data
                    });
                    that.Model.close();
                    break;
                case 'moveto':
                    e.result = true;
                    var row = that.Model.getSelectRows({ id: that.fieldsortId });
                    if (row.length == 0) {
                        yiDialog.mt({ msg: '请您选择需要移动的行！', skinseq: 2 });
                        return;
                    } if (row.length > 1) {
                        yiDialog.mt({ msg: '您只能选择一条移动的行！', skinseq: 2 });
                        return;
                    }
                    that.isChanged = true;
                    break;
                case 'moveup':
                    e.result = true;
                    that.moveup()
                    that.isChanged = true;
                    break;
                case 'movedown':
                    e.result = true;
                    that.movedown();
                    that.isChanged = true;
                    break;
                case 'moveconfirm':
                    e.result = true;
                    that.move();
                    that.isChanged = true;
                    break;
            }
        };



        //例如：sourcerow是第二行，targetrow是第八行，移动后，sourcerow是第八行，targetrow是第九行
        _child.prototype.move = function () {
            var that = this;


            var targetseq = $(".movetorow").val();
            var row = that.Model.getSelectRows({ id: that.fieldsortId });
            if (row.length == 0) {
                yiDialog.mt({ msg: '请您选择需要移动的行！', skinseq: 2 });
                return;
            }
            if (row.length > 1) {
                yiDialog.mt({ msg: '您只能选择一条移动的行！', skinseq: 2 });
                return;
            }
            var sourceseq = row[0].data.fseq_e;
            if (targetseq == sourceseq) {
                yiDialog.mt({ msg: '当前已在第' + sourceseq + '行！', skinseq: 2 });
                return;
            }
            var entrys = that.Model.getEntryData({ id: that.fieldsortId });
            if (targetseq > entrys.length + 1) {
                yiDialog.mt({ msg: '当前输入的目标行不存在！', skinseq: 2 });
                return;
            }
            targetseq = parseInt(targetseq);
            var entrys = that.Model.getEntryData({ id: that.fieldsortId });
            var type = '';
            var sourceRowId = '';
            var targetRowId = '';
            for (var i = 0; i < entrys.length; i++) {
                if (entrys[i].fseq_e == sourceseq) {
                    sourceRowId = entrys[i].id;
                    continue;
                }
                if (entrys[i].fseq_e == targetseq) {
                    targetRowId = entrys[i].id;
                    continue;
                }
            }

            if (sourceseq < targetseq) {
                //向下移
                type = 'down';
                for (var i = 0; i < entrys.length; i++) {
                    if (entrys[i].fseq_e <= targetseq && entrys[i].fseq_e > sourceseq) {
                        entrys[i].fseq_e--;
                    }
                }
                var sourceEntry = that.Model.getEntryRowData({ id: that.fieldsortId, row: sourceRowId });

                sourceEntry["fseq_e"] = targetseq;
            }
            if (sourceseq > targetseq) {
                //向上移
                type = 'up';
                for (var i = 0; i < entrys.length; i++) {
                    if (entrys[i].fseq_e < sourceseq && entrys[i].fseq_e >= targetseq) {
                        entrys[i].fseq_e++;
                    }
                }
                var targetEntry = that.Model.getEntryRowData({ id: that.fieldsortId, row: sourceRowId });
                if (targetEntry) {
                    targetEntry["fseq_e"] = targetseq;
                }
            }
            var ids = [];
            ids.push(sourceRowId);

            entrys.sort(function (a, b) {
                // 先按 优先级 字段排序
                if (a.fseq_e < b.fseq_e) {
                    return -1;
                }
                if (a.fseq_e > b.fseq_e) {
                    return 1;
                }
                return 0;
            });
            let scrollTop = 0;
            //将滚动条拉至最下面
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                if (j == 0) {
                                    scrollTop = childs[j].scrollTop;
                                }
                            }
                        }
                    }
                }
            }
            that.Model.refreshEntry({ id: that.fieldsortId });
            that.Model.setSelectRows({ id: that.fieldsortId, rows: ids });
            console.log(entrys);
            scrollTop = scrollTop - 10;

            //将滚动条拉到指定位置
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    var childs1 = entryHtmlToArray[i].querySelectorAll(".slick-viewport-top");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs[j].scrollTop = scrollTop;
                            }
                        }
                    }
                    if (childs1.length > 0) {
                        for (var j = 0; j < childs1.length; j++) {
                            if (childs1[j].scrollTop != childs1[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs1[j].scrollTop = scrollTop;
                            }
                        }
                    }
                }
            }
        }

        _child.prototype.moveup = function () {
            var that = this;

            var row = that.Model.getSelectRows({ id: that.fieldsortId });
            if (row.length == 0) {
                yiDialog.mt({ msg: '请您选择需要移动的行！', skinseq: 2 });
                return;
            }

            row = row.sort((a, b) => a.data.fseq_e - b.data.fseq_e);
            var entrys = that.Model.getEntryData({ id: that.fieldsortId });
            var upRowId = '';
            for (var i = 0; i < entrys.length; i++) {
                if (entrys[i].fseq_e == row[0].data.fseq_e - 1) {
                    upRowId = entrys[i].id;
                    continue;
                }
            }
            if (upRowId == '') {
                yiDialog.warn('已经是第一行，无法上移！');
                return;
            }
            var ids = [];
            for (var i = 0; i < row.length; i++) {
                ids.push(row[i].data.id);
                row[i].data.fseq_e--;
            }
            var targetEntry = that.Model.getEntryRowData({ id: that.fieldsortId, row: upRowId });
            if (targetEntry) {
                targetEntry["fseq_e"] = targetEntry["fseq_e"] + row.length;
            }

            entrys.sort(function (a, b) {
                // 先按 优先级 字段排序
                if (a.fseq_e < b.fseq_e) {
                    return -1;
                }
                if (a.fseq_e > b.fseq_e) {
                    return 1;
                }
                return 0;
            });
            let scrollTop = 0;
            //将滚动条拉至最下面
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                if (j == 0) {
                                    scrollTop = childs[j].scrollTop;
                                }
                            }
                        }
                    }
                }
            }
            //var _entrys = jQuery.extend(true, [], entrys);
            //that.Model.deleteEntryData({ id: that.fieldsortId });
            //for (var i = 0; i < _entrys.length; i++) {
            //    that.Model.addRow({ id: that.fieldsortId, data: _entrys[i] });
            //}

            that.Model.refreshEntry({ id: that.fieldsortId });
            that.Model.setSelectRows({ id: that.fieldsortId, rows: ids });
            console.log(entrys);
            scrollTop = scrollTop - 10;
            //将滚动条拉到指定位置
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    var childs1 = entryHtmlToArray[i].querySelectorAll(".slick-viewport-top");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs[j].scrollTop = scrollTop;
                            }
                        }
                    }
                    if (childs1.length > 0) {
                        for (var j = 0; j < childs1.length; j++) {
                            if (childs1[j].scrollTop != childs1[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs1[j].scrollTop = scrollTop;
                            }
                        }
                    }
                }
            }

        }


        _child.prototype.movedown = function () {
            var that = this;

            var row = that.Model.getSelectRows({ id: that.fieldsortId });
            if (row.length == 0) {
                yiDialog.mt({ msg: '请您选择需要移动的行！', skinseq: 2 });
                return;
            }
            row = row.sort((a, b) => a.data.fseq_e - b.data.fseq_e);
            var entrys = that.Model.getEntryData({ id: that.fieldsortId });
            var upRowId = '';
            for (var i = 0; i < entrys.length; i++) {
                if (entrys[i].fseq_e == row[row.length - 1].data.fseq_e + 1) {
                    upRowId = entrys[i].id;
                    continue;
                }
            }
            if (upRowId == '') {
                yiDialog.warn('已经是末尾行，无法下移！');
                return;
            }

            var ids = [];
            for (var i = 0; i < row.length; i++) {
                ids.push(row[i].data.id);
                row[i].data.fseq_e++;
            }
            var targetEntry = that.Model.getEntryRowData({ id: that.fieldsortId, row: upRowId });
            if (targetEntry) {
                targetEntry["fseq_e"] = targetEntry["fseq_e"] - row.length;
            }

            entrys.sort(function (a, b) {
                // 先按 优先级 字段排序
                if (a.fseq_e < b.fseq_e) {
                    return -1;
                }
                if (a.fseq_e > b.fseq_e) {
                    return 1;
                }
                return 0;
            });
            let scrollTop = 0;
            //将滚动条拉至最下面
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                if (j == 0) {
                                    scrollTop = childs[j].scrollTop;
                                }
                            }
                        }
                    }
                }
            }
            that.Model.refreshEntry({ id: that.fieldsortId });
            that.Model.setSelectRows({ id: that.fieldsortId, rows: ids });
            console.log(entrys);
            scrollTop = scrollTop - 10;

            //将滚动条拉到指定位置
            var entryHtml = that.Model.getEleMent({ id: that.fieldsortId, way: Consts.eleFetchWay.id });
            if (entryHtml.length > 0) {
                var entryHtmlToArray = Array.from(entryHtml);
                for (var i = 0; i < entryHtmlToArray.length; i++) {
                    var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                    var childs1 = entryHtmlToArray[i].querySelectorAll(".slick-viewport-top");
                    if (childs.length > 0) {
                        for (var j = 0; j < childs.length; j++) {
                            if (childs[j].scrollTop != childs[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs[j].scrollTop = scrollTop;
                            }
                        }
                    }
                    if (childs1.length > 0) {
                        for (var j = 0; j < childs1.length; j++) {
                            if (childs1[j].scrollTop != childs1[j].scrollHeight) {
                                //childs[j].scrollTop = childs[j].scrollHeight;
                                childs1[j].scrollTop = scrollTop;
                            }
                        }
                    }
                }
            }
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            switch (e.id) {
                case 'fshow':

                    break;
            }
            that.isChanged = true;
        };

        //加载下拉框数据源时触发：可由业务插件提供数据源
        _child.prototype.onComboDataLoading = function (e) {
            var that = this;
            switch (e.id) {
                case 'faggreatestyle':
                    var fieldType = that.Model.getValue({ id: 'ffieldtype', row: e.row });
                    switch (fieldType) {
                        case Consts.elementType.qtyField:
                        case Consts.elementType.priceField:
                        case Consts.elementType.amountField:
                        case Consts.elementType.sizeField:
                        case Consts.elementType.decimalField:
                        case Consts.elementType.integerField:
                            //数字类字段，可以选择所有统计类型
                            e.data = [
                                { id: '1', name: '合计' },
                                { id: '2', name: '平均' },
                                { id: '3', name: '最大' },
                                { id: '4', name: '最小' },
                                { id: '5', name: '计数' }
                            ];
                            break;
                        case Consts.elementType.basePropField:
                            e.data = [
                                { id: '1', name: '合计' },
                                { id: '2', name: '平均' },
                                { id: '3', name: '最大' },
                                { id: '4', name: '最小' },
                                { id: '5', name: '计数' }
                            ];
                            break;
                        default:
                            //其他类字段，只能选择空或者计数
                            e.data = [
                                { id: '1', name: '合计', disable: true },
                                { id: '2', name: '平均', disable: true },
                                { id: '3', name: '最大', disable: true },
                                { id: '4', name: '最小', disable: true },
                                { id: '5', name: '计数' }
                            ];
                            break;
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var simpleData = e.result.operationResult.simpleData;
            switch (e.opcode) {
                //加载样式设置信息
                case 'getlistcolorsetting':
                    that.styleLoad = true;
                    that.styleFields = JSON.parse(simpleData.fldInfo);
                    that.styleSets = JSON.parse(simpleData.setting);
                    that.initStyleSet();
                    break;
            }
        };

        /*渲染样式设置相关的逻辑（个性化设置属于平台功能，而且样式设置逻辑不具共性，无需过度抽象，在此时直接基于jquery实现）*************************************************************************************************************************************************/

        //初始化样式设置
        _child.prototype.initStyleSet = function () {
            var that = this;
            var styleSets = that.styleSets;

            //绑定事件
            that.bindEvent();

            //如果有设置过，则还原
            if (styleSets && styleSets.length > 0) {
                for (var i = 0; i < styleSets.length; i++) {
                    that.newStyle(styleSets[i]);
                }
            } else {
                //如果没有设置过，则自动新增一个空行
                that.newStyle();
            }
            //美化滚动条
            // Index.beautyScrollBar(that.$styleset);
        }

        //绑定事件
        _child.prototype.bindEvent = function () {
            var that = this;
            var $target = that.$styleset;

            //新增样式
            $target.on('click', '.ui-icon-plus', function () {
                that.newStyle();
                that.styleChange = true;
                //美化滚动条
                // Index.beautyScrollBar(that.$styleset);
            });

            //删除样式
            $target.on('click', '.ui-icon-trash', function (e) {
                e.stopPropagation();
                var styles = that.$styleset.find('.stylelist .y-styleitem').length;
                if (styles <= 1) {
                    yiDialog.error('至少要保留一行！');
                    return;
                }
                var $filterItem = $(this).closest('.y-styleitem');
                $filterItem.find('select').select2('destroy');
                $filterItem.removeData('itemdata').remove();
                that.styleChange = true;
            });

            //字段下拉框选择事件
            $target.on('change', '.field-container select', function () {
                var $that = $(this);
                fieldSelect($that);
                that.setItemData($that, 'fldKey');
                that.setItemData($that, 'opSymbol', '');
                that.setItemData($that, 'fldValue', '');
            });

            //比较符下拉框选择事件
            $target.on('change', '.operator-container select', function () {
                var $that = $(this);
                that.setItemData($that, 'opSymbol');
            });

            //字段值控件：下拉框选择事件（可能是辅助资料，或者是字段下拉框，这取决于字段和比较符）
            $target.on('change', '.value-container select', function () {
                that.setItemData($(this), 'fldValue');
            });

            //字段值控件：普通文本框的值改变事件
            $target.on('change', '.value-container .ordinary', function () {
                that.setItemData($(this), 'fldValue');
            });

            //文本框控件在得到焦点时自动选中其内容
            $target.on('focus', '.value-container input.form-control', function () {
                $(this).select();
            });

            //数字文本框录入值事件
            $target.on('keypress', '.value-container input.input-number', function (e) {
                var $that = $(this);
                var isInt = $that.data('isInt') === true;
                return yiCommon.filter(e, $that.val(), isInt);
            }).on('blur', '.value-container input.input-number', function (e) {
                _numberHandle(this);
            }).on('keydown', '.value-container input.input-number', function (e) {
                if (e.keyCode == 13) {
                    _numberHandle(this);
                }
            });
            function _numberHandle(dom) {
                var $el = $(dom);
                var isInt = $el.data('isInt') === true;
                $el.val(yiCommon.parser($el.val(), isInt));
                that.setItemData($el, 'fldValue');
            }

            //字段下拉框选择事件的处理函数
            function fieldSelect($ctl) {
                var $filterItem = $ctl.closest('.y-styleitem');
                var field = null;
                var fieldId = $.trim($ctl.val());
                if (fieldId) {
                    field = that.findField(fieldId);
                }

                //渲染比较符控件
                that.renderOperatorCtl({
                    $filterItem: $filterItem,
                    field: field
                });

                //渲染字段值控件
                that.renderValueCtl({
                    $filterItem: $filterItem,
                    field: field
                });

                //渲染颜色选择控件
                that.renderColorCtl({
                    $filterItem: $filterItem,
                    field: field
                });
            }
        };

        //设置样式数据项字段值
        _child.prototype.setItemData = function ($ctl, key, value) {
            var that = this;
            if (value === undefined) {
                value = $.trim($ctl.val());
            }
            var $filterItem = $ctl.closest('.y-styleitem');
            var itemData = $filterItem.data('itemdata');
            if (!itemData) {
                itemData = that.buildEmptyObj();
            }
            itemData[key] = value;
            $filterItem.data('itemdata', itemData);
            that.styleChange = true;
        };

        //新增样式
        _child.prototype.newStyle = function (data) {
            var that = this;
            var fields = that.styleFields;
            if (!fields) {
                yiDialog.error('没有业务字段可供设置，请检查！');
                return;
            }

            var defData = that.buildEmptyObj();
            var itemData = $.extend({}, defData, data || {});

            var field = null;
            if (itemData.fldKey) {
                field = that.findField(itemData.fldKey);
            }

            var fieldOpt = '<option value=""></option>';
            for (var i = 0; i < fields.length; i++) {
                var _field = fields[i];
                if (_field) {
                    var _sed = '';
                    if (itemData.fldKey && _field.id === itemData.fldKey) {
                        _sed = ' selected';
                    }
                    fieldOpt += '<option value="{0}"{2}>{1}</option>'.format(_field.id, _field.caption, _sed);
                }
            }
            var $filterItem = $('\
            <div class="row y-styleitem">\
                <div class="col-xs-4 field-container">\
                    <select class="form-control input-sm select2me" data-placeholder="字段名称">' + fieldOpt + '</select>\
                </div>\
                <div class="col-xs-2 operator-container">\
                    <select class="form-control input-sm select2me" data-placeholder="比较符"></select>\
                </div>\
                <div class="col-xs-4 value-container">\
                    <input class="form-control ordinary" autocomplete="off" placeholder="字段值" />\
                </div>\
                <div class="col-xs-1 color-container">\
                    <input class="color-box" />\
                </div>\
                <div class="col-xs-1 action-container">\
                    <span class="ui-icon ui-icon-plus"></span><span class="ui-icon ui-icon-trash"></span>\
                </div>\
            </div>');
            that.$styleset.find('.stylelist').append($filterItem);
            $filterItem.data('itemdata', itemData).find('.select2me').select2({ allowClear: true });

            //渲染比较符控件
            that.renderOperatorCtl({
                $filterItem: $filterItem,
                field: field,
                value: itemData.opSymbol,
            });

            //渲染字段值控件
            that.renderValueCtl({
                $filterItem: $filterItem,
                field: field,
                value: itemData.fldValue
            });

            //渲染颜色选择控件
            that.renderColorCtl({
                $filterItem: $filterItem,
                field: field,
                value: itemData.color
            });
        };

        //渲染比较符控件
        _child.prototype.renderOperatorCtl = function (e) {
            e.value = $.trim(e.value);
            var optHtml = '<option value=""></option>';
            if (e.field && e.field.opSymbol) {
                for (var i = 0; i < e.field.opSymbol.length; i++) {
                    var _op = e.field.opSymbol[i];
                    var _sed = '';
                    if (e.value && _op.id === e.value) {
                        _sed = ' selected';
                    }
                    optHtml += '<option value="{0}"{2}>{1}</option>'.format(_op.id, _op.name, _sed);
                }
            }
            e.$filterItem.find('.operator-container select').html(optHtml).select2({ allowClear: true });
        };

        //渲染字段值控件
        _child.prototype.renderValueCtl = function (e) {
            var that = this;
            var cp = that.formContext.cp;

            //字段值控件容器
            var $value_container = e.$filterItem.find('.value-container');

            //默认为普通文本框
            var ctlHtml = '<input class="form-control ordinary" autocomplete="off" placeholder="字段值" value="{0}" />'.format($.trim(e.value));
            if (!e.field) {
                $value_container.html(ctlHtml);
                return;
            }

            //如果有数据源则渲染为下拉框（由后端决定）
            if ($.isArray(e.field.dataSource)) {

                //先销毁字段值控件容器中多余的下拉框控件（有可能是上一次生成的）
                $value_container.find('select').select2('destroy');

                var optHtml = '<option value=""></option>';
                for (var i = 0; i < e.field.dataSource.length; i++) {
                    var data = e.field.dataSource[i];
                    var _sed = '';
                    if (e.value && data.id === e.value) {
                        _sed = ' selected';
                    }
                    optHtml += '<option value="{0}"{2}>{1}</option>'.format(data.id, data.name, _sed);
                }
                ctlHtml = '<select class="form-control input-sm select2me" data-placeholder="字段值">{0}</select>'.format(optHtml);
                $value_container.html(ctlHtml).find('select').select2({ allowClear: true });

            }

            //基础资料
            else if (e.field.elementType == Consts.elementType.baseDataField
                || e.field.elementType == Consts.elementType.sourceFormField
                || e.field.elementType == Consts.elementType.unitIdField) {

                ctlHtml = '\
                <div class="input-group">\
                    <input type="lookup" class="form-control" autocomplete="off" name="{0}" placeholder="字段值" value="{1}" />\
                </div>'.format(e.field.id, e.value);
                var $baseCtl = $value_container.html(ctlHtml).find('input');
                $baseCtl.bdSelect({
                    pageId: cp.parentPageId,
                    formId: cp.fbizformid.id,
                    domainType: 'bill',
                    onSelect: function (_e) {
                        //这里直接取文本框的值即可
                        var bdName = $.trim($baseCtl.val());
                        that.setItemData($baseCtl, 'fldValue', bdName);
                    }
                });
            }

            else {

                $value_container.html(ctlHtml);

                //数字类（整数，小数，数量，单价，金额，打印次数，附件数，批注数）
                var numbers = [101, 102, 103, 104, 105, 128, 129, 130];
                if (numbers.indexOf(e.field.elementType) >= 0) {
                    var $input = $value_container.find('input');
                    $input.attr('maxlength', 6).addClass('input-number');
                    //整数
                    if ([101, 128, 129, 130].indexOf(e.field.elementType) >= 0) {
                        $input.data('isInt', true);
                    }
                }
            }
        };

        //渲染颜色选择控件
        _child.prototype.renderColorCtl = function (e) {
            var that = this;
            var color = $.trim(e.value);
            if (color) {
                //将颜色值转换为十六进制
                if (color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)) {
                    if ($.browser.msie && $.browser.version > 8 || $.browser.mozilla || $.browser.webkit) {
                        color = color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                        color = hex(color[1]) + hex(color[2]) + hex(color[3]);
                        function hex(x) {
                            return ("0" + parseInt(x).toString(16)).slice(-2);
                        }
                    }
                }
            } else {
                //默认为橘黄色
                color = 'rgb(247, 150, 70)';
            }
            e.$filterItem.find('.color-box').spectrum({
                allowEmpty: true,
                showInput: true,
                showInitial: true,
                showPalette: true,
                showSelectionPalette: true,
                showAlpha: true,
                color: color,
                maxPaletteSize: 10,
                cancelText: "取消并关闭",
                chooseText: "确定",
                clearText: "清除所选颜色",
                preferredFormat: "hex",
                containerClassName: "full-spectrum",
                localStorageKey: "spectrum.demo",
                move: function (color) {

                },
                show: function () {

                },
                beforeShow: function () {

                },
                hide: function (color) {

                },
                change: function () {
                    that.styleChange = true;
                },
                palette: [
                    ['#ffffff', '#000000', '#eeece1', '#1f497d', '#4f81bd', '#c0504d', '#9bbb59', '#8064a2', '#4bacc6', '#f79646'],
                    ['#f2f2f2', '#7f7f7f', '#ddd9c3', '#c6d9f0', '#dbe5f1', '#f2dcdb', '#ebf1dd', '#e5e0ec', '#dbeef3', '#fdeada'],
                    ['#d8d8d8', '#595959', '#c4bd97', '#8db3e2', '#b8cce4', '#e5b9b7', '#d7e3bc', '#ccc1d9', '#b7dde8', '#fbd5b5'],
                    ['#bfbfbf', '#3f3f3f', '#938953', '#548dd4', '#95b3d7', '#d99694', '#c3d69b', '#b2a2c7', '#92cddc', '#fac08f'],
                    ['#a5a5a5', '#262626', '#494429', '#17365d', '#366092', '#953734', '#76923c', '#5f497a', '#31859b', '#e36c09'],
                    ['#7f7f7f', '#0c0c0c', '#1d1b10', '#0f243e', '#244061', '#632423', '#4f6128', '#3f3151', '#205867', '#974806'],
                    ['#c00000', '#ff0000', '#ffc000', '#ffff00', '#92d050', '#00b050', '#00b0f0', '#0070c0', '#002060', '#7030a0']
                ]
            });
        };

        //获取样式设置信息
        _child.prototype.getStyleSet = function () {
            var that = this;
            var styleSet = [];
            that.$styleset.find('.stylelist .y-styleitem').each(function () {
                var $that = $(this);
                var data = $that.data('itemdata');
                if (data.fldKey && data.opSymbol) {
                    data.color = $that.find('.sp-preview-inner').css('background-color');
                    styleSet.push(data);
                }
            });
            //允许用户不设置任何字段样式，所以需要增加一个空行
            if (styleSet.length < 1) {
                styleSet.push(that.buildEmptyObj());
            }
            return styleSet;
        };

        //查找字段模型
        _child.prototype.findField = function (id) {
            var fields = this.styleFields;
            if (fields && id) {
                for (var i = 0; i < fields.length; i++) {
                    if (fields[i].id === id) {
                        return fields[i];
                    }
                }
            }
            return null;
        };

        //生成一个空的对象
        _child.prototype.buildEmptyObj = function () {
            return { fldKey: '', opSymbol: '', fldValue: '', color: '' };
        };

        /*渲染样式设置相关的逻辑*************************************************************************************************************************************************/

        return _child;
    })(BasePlugIn);
    window.sys_personalizedsetentry = sys_personalizedsetentry;
})();