
/*
 /// @ sourceURL=/fw/js/sys/sys_billparamsetting.js
 */
; (function () {
    var sys_billparamsetting = (function (_super) {
        //构造函数
        var _child = function (args) {
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.onInitialized = function () {
            var that = this;
            var typeObjs = [
                { id: "1", name: "无" },
                { id: "2", name: "角色" },
                { id: "3", name: "用户" }
            ];
            var dataSource = that.Model.getEntryData({ id: "fentity" });
            for (var i = 0; i < dataSource.length; i++) {
                for (var j = 0; j < typeObjs.length; j++) {
                    if (dataSource[i].flinktype.id == typeObjs[j].id) {
                        that.Model.setValue({ id: "flinktype", row: dataSource[i].id, value: { id: typeObjs[j].id, fnumber: typeObjs[j].name, fname: typeObjs[j].name } });
                        break;
                    }
                }
            }
            if (that.formContext && that.formContext.cp && that.formContext.cp.btnCmbData) {
                var cmbData = that.formContext.cp.btnCmbData;
                for (var i = 0; i < dataSource.length; i++) {
                    for (var j = 0; j < cmbData.length; j++) {
                        if (dataSource[i].fbtnname.id == cmbData[j].id) {
                            that.Model.setValue({ id: "fbtnname", row: dataSource[i].id, value: { id: cmbData[j].id, fnumber: cmbData[j].name, fname: cmbData[j].name } });
                            break;
                        }
                    }
                }
            }
        }

        _child.prototype.onComboDataLoading = function (args) {
            var that = this;
            switch (args.id) {
                case "flinktype":
                    args.data = [
                        { id: "1", name: "无" },
                        { id: "2", name: "角色" },
                        { id: "3", name: "用户" }
                    ];
                    break;
                case "fbtnname":
                    if (that.formContext && that.formContext.cp && that.formContext.cp.btnCmbData) {
                        args.data = that.formContext.cp.btnCmbData;
                    }
                    break;
            }
        }

        return _child;
    })(BillPlugIn);
    window.sys_billparamsetting = window.sys_billparamsetting || sys_billparamsetting;
})();
