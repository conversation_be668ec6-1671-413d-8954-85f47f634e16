var checks = {
    consts: {
        //页面初始化时自动从后端读取
        loginUrl: '',
        code: ''
    },

    checkPhone: function ($phone) {  /*电话号码验证*/
        var $pError = $("#pError");
        var $display = $(".display-hide");
        if ($.trim($phone.val()) === "") {
            $phone.addClass("input_error");
            $pError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            var reg = /^[1]{1}[3,4,5,6,7,8,9]{1}[0-9]{9}$/;
            if (reg.test($.trim($phone.val())) === false) {
                $phone.addClass("input_error");
                $pError.show().removeClass().html("电话号码格式不正确！").addClass("alerts");
                return false;
            } else {
                $phone.removeClass("input_error");
                $pError.hide();
                return true;
            }
        }
    },

    checkRecode: function ($code) {  /*验证码验证*/
        var recError = $("#recError");
        if (!$.trim($code.val())) {
            $code.addClass("input_error");
            recError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $code.removeClass("input_error");
            recError.hide();
            return true;

        }
    },

    check1: function () {  /*鼠标离开时判断是否满足验证要求*/
        $(document).on("blur", "#txtPhone", function () {  //手机号
            checks.checkPhone($(this));
        });
        $(document).on("blur", "#txtCode", function () { //验证码
            checks.checkRecode($(this));
        });
    },

    check2: function () {   /*登录,注册提交*/
        

    },

    check3: function () {
        ///*验证码的动态获取*/
		var timer;
		var flagRe=true;
        $(".authcode").on('click', function () {
        	
        	var times=60;
        	if(!flagRe){
        		return;
        	}
            if (!checks.checkPhone($("#txtPhone"))) {
                return;
            }
            var phoneNo = $.trim($("#txtPhone").val());

            yiAjax.g('/authuser/userexist/{0}'.format(phoneNo), null,
            function (ret)
            {
                if (ret === false) {
                    yiCommon.showError('账号不存在。');
                    return;
                }
                yiAjax.g('/sms/code?format=json&mobilePhone={0}'.format(phoneNo), null,
                    function (r) {

                        if (r && r.operationResult && r.operationResult.isSuccess) {
                            flagRe = false;
                            checks.consts.code = r.operationResult.srvData
                            yiCommon.showError('验证码发送成功！', 'success');
                            $('.authcode').addClass('disabled')
                                    .html('60秒之内不能重复发送');
                            timer = setInterval(function () {
                                times = times - 1;
                                if (times == 0) {
                                    flagRe = true;
                                    $('.authcode').removeClass('disabled').html('获取验证码');
                                    clearInterval(timer);
                                } else {
                                    $('.authcode').addClass('disabled')
                                    .html('{0}秒之内不能重复发送'.format(times));
                                }

                            }, 1000);
                        }
                        else {
                            if (r && r.operationResult && r.operationResult.simpleMessage) {
                                yiCommon.showError(r.operationResult.simpleMessage, 'success');
                            }
                        }
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );
                
            },function (m) {

                //提示错误信息
                yiCommon.showError(yiCommon.extract(m));

            }, false, $("body"))

            
        });
        //手机号和验证码的双重验证，成功后跳转到重置密码板块
        $("#show-pwd").on('click', function () {
        	var phone=$.trim($("#txtPhone").val());
            if (!checks.checkPhone($("#txtPhone"))) {
                return;
            }
            var code=$.trim($("#txtCode").val());
            if(!code){
            	return;
            }
            
            var phoneNo = $.trim($("#txtPhone").val());
            yiAjax.g('/sms/code/validate.json?mobilePhone={0}&authCode={1}'.format(phoneNo,code), null,
                function (r) {
                	
                    if (r && r.operationResult && r.operationResult.isSuccess) {
                        checks.consts.code = r.operationResult.srvData
                        yiCommon.showError('验证码校验成功！', 'success');
                        $('.login-rightbox .login-form').addClass('display-hide');
                        $('.login-rightbox .login-form.set-newpwd').data('recode',code).removeClass('display-hide');
                        $('.login-rightbox .login-form.set-newpwd').data('phone',phone).removeClass('display-hide');
                    	$('.enterprise_phone').text(phone);
                    }
                    
                },
                function (m) {

                    //提示错误信息
                    yiCommon.showError(yiCommon.extract(m));

                }, false, $("body")
            );
        });
        
    },

    checkPwd: function ($pwd) {  /*新密码验证*/
        var $pwError = $('#pwError');

        if ($.trim($pwd.val()) === "") {
            $pwd.addClass('input_error');
            $pwError.show().removeClass().html("请输入新密码！").addClass("alerts");
            return false;
        } else {
            var reg = /^\S{6,16}$/;
            if (reg.test($.trim($pwd.val())) === false) {
                $pwd.addClass('input_error');
                $pwError.show().removeClass().html("新密码长度必须为 6-16 位！").addClass("alerts");
                return false;
            } else {
                $pwd.removeClass('input_error');
                $pwError.hide();
                return true;
            }
        }
    },

    checkRepwd: function ($repwd) {  /*新密码确认验证*/
        var $repwError = $('#repwError');

        if ($.trim($repwd.val()) === "") {
            $repwd.addClass('input_error');
            $repwError.show().removeClass().html("请再次输入新密码！").addClass("alerts");
            return false;
        } else {
            var reg = /^\S{6,16}$/;
            if ($.trim($repwd.val()) != $.trim($("#newPassword").val())) {
                $repwd.addClass('input_error');
                $repwError.show().removeClass().html("两次输入密码不一致，请重新输入！").addClass("alerts");
                return false;
            } else {
                $repwd.removeClass('input_error');
                $repwError.hide();
                return true;
            }
        }

    },

    check4: function () {  /*鼠标离开时判断是否满足验证要求*/
        $("#newPassword").on("blur", function () {  //新密码
            checks.checkPwd($(this));
        });
        $("#newRePassword").on("blur", function () {  //新密码确认
            checks.checkRepwd($(this));
        });
    },

    check5: function () {   /*登录,注册提交*/
        var b = true;
        var $phone=$("#txtPhone");
        var $pwd = $("#newPassword");
        var $repwd = $("#newRePassword");

        //注册处理
        $(document).on("click", "#resetPwd", function () {
        	
            var a = [];//用来存放b的数组
            var flag = true;
            //参数
            var params = {
            	mobilePhone:$.trim($phone.val()),
                password: $.trim($pwd.val()),
                confirmPassword: $.trim($repwd.val()),
                activeCode: $.trim($('.login-rightbox .login-form.set-newpwd').data('recode'))
            };
            $pwd.each(function () {
                if (checks.checkPwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $repwd.each(function () {
                if (checks.checkRepwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            for (var i = 0; i < a.length; i++) {
                if (a[i] === false) {
                    flag = false;
                }
            }
            if (flag === true) {

                yiAjax.p('/authuser/update', params,
                    function (r) {
                        //提示用户
                        if (r.operationResult.isSuccess == true) {

                            $(".alert").hide();
                            $('form.set-newpwd').hide();
                            $('form.set-success').show();
                            var i = 5;
                            $('.timer').html(i);
                            setInterval(function () {
                                i--;
                                $('.timer').html(i);
                                if (i == 0) {
                                    window.location.href = '/views/account/login_enterprise.html';
                                }
                            }, 1000);

                        }
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );
            } else {
                $(".alert").html("请填写以下信息").show();
            }
            function getUserInfo() {
                return {
                    pwd: $.trim($pwd.val()),
                    repwd: $.trim($repwd.val()),
                }
                
            }
        });

    },

    init: function () {
        this.check1();
        this.check2();
        this.check3();
        this.check4();
        this.check5();
        //获取系统信息
        yiAjax.p('/authuser/hostconfig', {},
            function (r) {
                if (r && r.operationResult && r.operationResult.srvData) {
                    var srvd = r.operationResult.srvData;
                    if (!srvd) { return; }
                    //产品名称
                    if ($.trim(srvd.productName)) {
                        document.title = srvd.productName + ' - 重置密码';
                    }
                    //版权信息
                    if ($.trim(srvd.serviceProvider)) {
                        $('.copyright').html('Copyright &copy; 2013 - {0} {1}'.format(srvd.endYear, srvd.serviceProvider));
                    }
                    //登录页logo
                    if ($.trim(srvd.icon)) {
                    	$('.logo').html('<img src="../../fw/icons/login/'+ srvd.icon +'"/>');
                    }
                }
            }
        );
    }
}

checks.init();