var Login_Dialog = {

    //常量
    passwordId: '#txtPasswordBox',
    dialogSelector: '#login-dialog',

    //初始化
    init: function (index) {

        var that = this;

        //回车响应登录
        $(that.passwordId).keypress(function (e) {
            if (e.which === 13) {
                that.login(index);
                return false;
            }
        });

        //将光标移到密码框中
        setTimeout(function () {
            $(that.passwordId).focus();
        }, 500)
    },

    //注销
    logout: function () {

        yiCommon.showError('正在注销中，请稍等 . . .', 'success', this.dialogSelector);
    },

    //登录
    login: function (index, callback) {

        var that = this;

        var txtPassword = $(that.passwordId);
        var password = $.trim(txtPassword.val());
        if (!password) {
            yiCommon.showError('请输入您的密码！', '', that.dialogSelector);
            txtPassword.focus();
            return;
        }

        debugger;
        var meta = {};
        meta.byexpirylogin = "过期重新登录";
        meta.company = window.__companyId ;


        //提示进度信息
        yiCommon.showError('正在登录中，请稍等 . . .', 'success', that.dialogSelector);

        //提交登录
        yiAjax.p('/auth/credentials', { userName: Consts.loginUserName, password: password, meta: meta},

            function (r) {

                //提示成功信息
                yiCommon.showError('登录成功，正在执行后续操作 . . .', 'success', that.dialogSelector);

                //关闭对话框
                layer.close(index);
                if (r && r.meta && r.meta.company && r.meta.company) {
                    var org = JSON.parse(r.meta.company);
                    if (org && org.companyId) {
                        window.__companyId = org.companyId;
                    }
                }

                debugger;

                if (r && r.meta && r.meta.usertoken) {
                    window.__tokenId = r.meta.usertoken;
                    window.__xuid = $.trim(r.userId);

                    var oldUrl = document.location.href + '&s=1';
                    var newTokenUrl = oldUrl.replace(/(token=).*?(&)/, '$1' + r.meta.usertoken + '$2').trimEnd('&s=1');
                    history.replaceState({}, document.title, newTokenUrl)

                }
                //执行回调函数
                $.isFunction() && callback();
            },

            function (m) {

                //提示错误信息
                yiCommon.showError(yiCommon.extract(m), '', that.dialogSelector);

            }, false, $(that.dialogSelector).parent()
        );
    }
};