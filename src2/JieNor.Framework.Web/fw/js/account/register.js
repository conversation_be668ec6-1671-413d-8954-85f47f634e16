/// <reference path="../../../typings/jquery/jquery.d.ts" />

var checks = {
    consts: {
        //页面初始化时自动从后端读取
        loginUrl: ''
    },
    checkEmail: function ($email) {  /*电子邮箱验证*/
        var $emError = $("#emError");
        var $display = $(".display-hide");
        if ($email.val() === "") {
            //$email.focus();
            $email.addClass("input_error");
            $email.siblings('i').addClass("i_error");
            $emError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $email.removeClass("input_error");
            $email.siblings('i').removeClass("i_error");
            $("#email-number").html($email.val());//动态获取用户输入的电子邮箱
            $emError.hide();
            return true;
        }
    },

    checkRecode: function ($code) {  /*验证码验证*/
        var recError = $("#recError");
        if ($code.val() === "") {
            $code.addClass("input_error");
            $code.siblings('i').addClass("i_error");
            recError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $code.removeClass("input_error");
            $code.siblings('i').removeClass("i_error");
            recError.hide();
            return true;
        }
    },

    checkName: function ($userName) {  /*用户名验证*/
        var $unError = $("#unError");
        if ($userName.val() === "") {
            $userName.addClass("input_error");
            $userName.siblings('i').addClass("i_error");
            $unError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $userName.removeClass("input_error");
            $userName.siblings('i').removeClass("i_error");
            $unError.hide();
            return true;

        }
    },

    checkPhone: function ($phone) {  /*电话号码验证*/
        var $telError = $("#telError");
        if ($phone.val() === "") {
            $phone.addClass("input_error");
            $phone.siblings('i').addClass("i_error");
            $telError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            var reg = /^[1]{1}[3,4,5,6,7,8,9]{1}[0-9]{9}$/;//   /^[1][0-9]{10}$/;
            if (reg.test($phone.val()) === false) {
                $phone.addClass("input_error");
                $phone.siblings('i').addClass("i_error");
                $telError.show().removeClass().html("电话号码以1开头，且长度为11位").addClass("alerts");
                return false;
            } else {
                $phone.removeClass("input_error");
                $phone.siblings('i').removeClass("i_error");
                $telError.hide();
                return true;
            }
        }
    },

    checkPwd: function ($pwd) {  /*密码验证*/
        var $pwError = $("#pwError");
        if ($pwd.val() === "") {
            $pwd.addClass("input_error");
            $pwd.siblings('i').addClass("i_error");
            $pwError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            var reg = /^\S{6,16}$/;
            if (reg.test($pwd.val()) === false) {
                $pwd.addClass("input_error");
                $pwd.siblings('i').addClass("i_error");
                $pwError.show().removeClass().html("登录密码长度为6-16").addClass("alerts");
                return false;
            } else {
                $pwd.removeClass("input_error");
                $pwd.siblings('i').removeClass("i_error");
                $pwError.hide();
                return true;
            }
        }
    },
    checkRepwd: function ($repwd) {  /*确认密码验证*/
        var repwError = $("#repwError");
        if ($repwd.val() === "") {
            $repwd.addClass("input_error");
            $repwd.siblings('i').addClass("i_error");
            repwError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            if ($repwd.val() !== $("#txtPassword").val()) {
                $repwd.addClass("input_error");
                $repwd.siblings('i').addClass("i_error");
                repwError.show().removeClass().html("两次输入密码不一致，请重新输入！").addClass("alerts");
                return false;
            } else {
                $repwd.removeClass("input_error");
                $repwd.siblings('i').removeClass("i_error");
                repwError.hide();
                return true;
            }
        }
    },

    checkEnterpriseId: function ($company) {  /*确认密码验证*/
        var entError = $("#entError");
        if ($company.val() === "") {
            $company.addClass("input_error");
            $company.siblings('i').addClass("i_error");
            entError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $company.removeClass("input_error");
            $company.siblings('i').removeClass("i_error");
            entError.hide();
            return true;
        }
    },

    checkJoinCode: function ($joinCode) {  /*确认密码验证*/
        var joinCodeError = $("#joinCodeError");
        if ($joinCode.val() === "") {
            $joinCode.addClass("input_error");
            $joinCode.siblings('i').addClass("i_error");
            joinCodeError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $joinCode.removeClass("input_error");
            $joinCode.siblings('i').removeClass("i_error");
            joinCodeError.hide();
            return true;
        }
    },

    check1: function () {  /*鼠标离开时判断是否满足验证要求*/        
        $(document).on("blur", "#txtCode", function () { //验证码
            checks.checkRecode($(this));
        });
        $(document).on("blur", "#txtUserName", function () {  //用户名
            checks.checkName($(this));
        });
        $(document).on("blur", "#txtPhone", function () {  //电话号码
            checks.checkPhone($(this));
        });
        $(document).on("blur", "#txtPassword", function () {  //密码
            checks.checkPwd($(this));
        });
        $(document).on("blur", "#txtRePassword", function () {  //确认密码
            checks.checkRepwd($(this));
        });

        $(document).on("blur", "#txtJoinCode", function () {  //加入企业
            checks.checkJoinCode($(this));
        });
        $(document).on("blur", "#txtJoinEnterpriseId", function () {  //企业加入密码
            checks.checkEnterpriseId($(this));
        });
    },

    check2: function () {   /*登录,注册提交*/
        var b = true;
        //var $email = $("#txtEm1");
        var $code = $("#txtCode");
        var $userName = $("#txtUserName");
        var $phone = $("#txtPhone");
        var $passWord = $("#txtPassword");
        var $rePassword = $("#txtRePassword");
        var $joinCode = $("#txtJoinCode");
        var $joinCompany = $("#txtJoinEnterpriseId")

        var redirectto = $.query.get("redirect");

        //注册处理
        $(document).on("click", ".to-register", function () {
            var a = [];//用来存放b的数组
            var flag = true;
            $userName.each(function () {
                if (checks.checkName($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $passWord.each(function () {
                if (checks.checkPwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $rePassword.each(function () {
                if (checks.checkRepwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $code.each(function () {
                if (checks.checkRecode($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $joinCode.each(function () {
                if (checks.checkJoinCode($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $joinCompany.each(function () {
                if (checks.checkEnterpriseId($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            for (var i = 0; i < a.length; i++) {
                if (a[i] === false) {
                    flag = false;
                }
            }
            if (flag === true) {
                yiAjax.p('/reguser', getUserInfo(),
                    function (r) {
                        //提示用户
                        yiCommon.showError('注册成功，正在跳转至登录界面 . . .', 'success');
                        setTimeout(function () {
                            if (redirectto) {
                                window.location.href = redirectto;
                            }
                            else {
                                window.location.href = checks.consts.loginUrl;
                            }
                        }, 1500)
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );
            } else {
                $(".alert").html("请填写以下信息").show();
            }
            function getUserInfo() {
                return {
                    //email: $.trim($email.val()),
                    userName: $.trim($userName.val()),
                    passWord: $.trim($passWord.val()),
                    rePassword: $.trim($rePassword.val()),
                    phoneNumber: $.trim($phone.val()),
                    activeCode: $.trim($code.val()),
                    company: $.trim($joinCompany.val()),
                    joinCode:$.trim($joinCode.val())
                }
            }
        });

        //判断是否是用户激活失败返回的界面
        var message = $.query.get("message");
        if (message && message !== '') {
            yiCommon.showError(message);
        }
        var userName = $.query.get("userName");
        if (userName && userName !== '') {
            $email.val(userName);
        }       

    },

    check3: function () {
        ///*验证码的动态获取*/
        //$(document).on("click", ".change", function () { //随机产生数字
        //    var letter = new Array("1234", "5876", "5475", "5788", "7757", "5246", "4788", "2555", "7526", "1672", "2868", "2587", "8786", "6758", "4572", "4783", "4785", "5757", "5242", "9682", "0235", "7774", "6835", "5535", "8635", "4263");
        //    var index = Math.floor(Math.random() * 26);
        //    $("#code").html(letter[index]);
        //});

        //$("a.change").click();
		var time_bool = true;
        $(".authcode").on('click', function () {
        	
        	if(time_bool == true){
        		if (!checks.checkPhone($("#txtPhone"))) {
	                return;
	            }
	            var phoneNo = $.trim($("#txtPhone").val());
	            yiAjax.g('/sms/code?format=json&mobilePhone={0}'.format(phoneNo), {},
	                function (r) {
	                    if (r && r.operationResult && r.operationResult.isSuccess) {
	                        console.log("验证码发送成功！");
	                    }                    
	                },
	                function (m) {
	
	                    //提示错误信息
	                    yiCommon.showError(yiCommon.extract(m));
	
	                }, false, $("body")
	            );
        	}else{
        		return;
        	}
    		
            //点击添加遮罩层使用户无法点击
            time_bool = false;
			//60秒验证码重试倒计时
            $(this).css({"background-color":"#ccc !important","cursor":"default"}).html(60+'秒后重试');
			var setTime;
			var time=parseInt($(this).text());
            setTime=setInterval(function(){
                if(time<=0){
                	time_bool = true;
                    clearInterval(setTime);
                    $(".authcode").css({"background-color":"#77b1ef !important","cursor":"pointer"});
                    $(".authcode").text("获取验证码");
                    return;
                    
                }
                time--;
                $(".authcode").text(time+'秒后重试');
            },1000);
        });
    },

    loadCompanyList: function () {
        yiAjax.g('/authuser/company?format=json', {},
            function (r) {
                //if (r && r.operationResult && r.operationResult.simpleData && r.operationResult.simpleData.productname) {
                //    document.title = r.operationResult.simpleData.productname + ' - 用户注册';
                //    $('.logo a').text(r.operationResult.simpleData.productname);
                //}
                if (r && r.operationResult && r.operationResult.srvData)

                    var data = r.operationResult.srvData;
                //var optHtml = '<option value="">请选择默认加入的公司</option>';
                var optHtml = '';
                for (var i = 0; i < data.length; i++) {
                    optHtml += '<option value="{0}">{1}</option>'.format(data[i].id, data[i].text);
                }
                $("#companylist").html(optHtml).select2({ placeholder: '加入企业' });
            },
            function (m) {

                //提示错误信息
                yiCommon.showError(yiCommon.extract(m));

            }, false, $("body")
        );
    },

    //加载 HostConfig 配置
    loadHostConfig: function () {
        yiAjax.p('/authuser/hostconfig', {},
            function (r) {
                if (r && r.operationResult && r.operationResult.srvData) {
                    var srvd = r.operationResult.srvData;
                    if (!srvd) { return; }
                    //产品名称
                    if ($.trim(srvd.productName)) {
                        document.title = srvd.productName + ' - 用户注册';
                        $('.logo a').text(srvd.productName);
                    }
                    //版权信息
                    if ($.trim(srvd.serviceProvider)) {
                        $('.copyright').html('Copyright &copy; 2013 - {0} {1}'.format(srvd.endYear, srvd.serviceProvider));
                    }
                    //默认登录页面地址
                    if ($.trim(srvd.defaultPage)) {
                        checks.consts.loginUrl = srvd.defaultPage;
                    }
                }
            }
        );
    },

    init: function () {
        this.loadHostConfig();
        this.check1();
        this.check2();
        this.check3();
        //this.loadCompanyList();
        //获取系统信息
        yiAjax.p('/authuser/hostconfig', {},
            function (r) {
                if (r && r.operationResult && r.operationResult.srvData) {
                    var srvd = r.operationResult.srvData;
                    if (!srvd) { return; }
                    //产品名称
                    if ($.trim(srvd.productName)) {
                        document.title = srvd.productName + ' - 用户注册';
                    }
                    //版权信息
                    if ($.trim(srvd.serviceProvider)) {
                        $('.copyright').html('Copyright &copy; 2013 - {0} {1}'.format(srvd.endYear, srvd.serviceProvider));
                    }
                    //登录页logo
                    if ($.trim(srvd.icon)) {
                    	$('.logo').html('<img src="../../fw/icons/login/'+ srvd.icon +'"/>');
                    }
                }
            }
        );
    }
}

checks.init();