
var checks = {
    consts: {
        code: ''
    },

    checkName: function ($userName) {  /*用户名验证*/
        var $unError = $("#unError");
        if ($userName.val() === "") {
            $userName.addClass("input_error");
            $unError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $userName.removeClass("input_error");
            $unError.hide();
            return true;

        }
    },

    checkPhone: function ($phone) {  /*电话号码验证*/
        var $pError = $("#pError");
        var $display = $(".display-hide");
        if ($phone.val() === "") {
            $phone.addClass("input_error");
            $pError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            var reg = /^[1]{1}[3,4,5,7,8]{1}[0-9]{9}$/;
            if (reg.test($phone.val()) === false) {
                $phone.addClass("input_error");
                $pError.show().removeClass().html("电话号码格式不正确！").addClass("alerts");
                return false;
            } else {
                $phone.removeClass("input_error");
                $pError.hide();
                return true;
            }
        }
    },

    checkRecode: function ($code) {  /*验证码验证*/
        var recError = $("#recError");
        if ($code.val() === "") {
            $code.addClass("input_error");
            recError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $code.removeClass("input_error");
            recError.hide();
            return true;
        }
    },

    check1: function () {  /*鼠标离开时判断是否满足验证要求*/
        $(document).on("blur", "#txtUserName", function () {  //用户名
            checks.checkName($(this));
        });

        $(document).on("blur", "#txtPhone", function () {  //手机号
            checks.checkPhone($(this));
        });
        $(document).on("blur", "#txtCode", function () { //验证码
            checks.checkRecode($(this));
        });
    },

    check2: function () {   /*登录,注册提交*/
        var b = true;
        var $code = $("#txtCode");
        var $userName = $("#txtUserName");
        var $phone = $("#txtPhone");

        var redirectto = $.query.get("redirect");

        $(document).on("click", "#btnLogIn", function () {
            var a = [];//用来存放b的数组
            var flag = true;
            $userName.each(function () {
                if (checks.checkName($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $phone.each(function () {
                if (checks.checkPhone($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $code.each(function () {
                if (checks.checkRecode($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            for (var i = 0; i < a.length; i++) {
                if (a[i] === false) {
                    flag = false;
                }
            }
            if (flag === true) {
                yiCommon.showError('正在为您开通试用产品服务 . . .请稍候', 'success');
                yiAjax.p('/try/login', getUserInfo(),
                    function (r) {
                        if (r && r.operationResult && r.operationResult.srvData) {
                            //提示用户
                            yiCommon.showError('正在为您开通试用产品服务 . . .已完成，正在加载产品体验中心列表 . . .', 'success');
                            setTimeout(function () {
                                var serviceList = "";
                                for (var i = 0; i < r.operationResult.srvData.length; i++) {
                                    var trialProduct = r.operationResult.srvData[i];
                                    serviceList += "<p><a target='_blank' href='{0}'><span>{1}</span></a></p>".format(trialProduct.demoUrl,trialProduct.productName);
                                }
                                
                                $(".login-form").addClass('display-hide');
                                $(".exp-result").removeClass('display-hide');
                                $(".exp-result h3").after(serviceList);

                            }, 1500)
                        }
                        else {
                            yiDialog.a('无法获取产品演示中心地址！');
                        }
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );
            } else {
                $(".alert").html("请填写以下信息").show();
            }
            function getUserInfo() {
                return {
                    displayName: $.trim($userName.val()),
                    phoneNumber: $.trim($phone.val()),
                    validCode: $.trim($code.val()),
                }
            }
        });

    },

    check3: function () {
        ///*验证码的动态获取*/

        $(".authcode").on('click', function () {
            if (!checks.checkPhone($("#txtPhone"))) {
                return;
            }
            var phoneNo = $.trim($("#txtPhone").val());
            yiAjax.g('/sms/code?format=json&mobilePhone={0}'.format(phoneNo), null,
                function (r) {
                    if (r && r.operationResult && r.operationResult.isSuccess) {
                        checks.consts.code = r.operationResult.srvData
                        yiCommon.showError('验证码发送成功！', 'success');
                    }
                },
                function (m) {

                    //提示错误信息
                    yiCommon.showError(yiCommon.extract(m));

                }, false, $("body")
            );
        });
    },

    init: function () {
        this.check1();
        this.check2();
        this.check3();
        //获取系统信息
        yiAjax.p('/authuser/hostconfig', {},
            function (r) {
                if (r && r.operationResult && r.operationResult.srvData) {
                    var srvd = r.operationResult.srvData;
                    if (!srvd) { return; }
                    //产品名称
                    if ($.trim(srvd.productName)) {
                        document.title = srvd.productName + ' - 体验';
                    }
                    //版权信息
                    if ($.trim(srvd.serviceProvider)) {
                        $('.copyright').html('Copyright &copy; 2013 - {0} {1}'.format(srvd.endYear, srvd.serviceProvider));
                    }
                    //登录页logo
                    if ($.trim(srvd.icon)) {
                    	$('.logo').html('<img src="../../fw/icons/login/'+ srvd.icon +'"/>');
                    }
                }
            }
        );
    }
}

checks.init();