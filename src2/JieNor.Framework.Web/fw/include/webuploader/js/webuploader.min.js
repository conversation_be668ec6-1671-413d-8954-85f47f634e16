!function(e,t){var n,i={},r=function(e,t){var n,i,r;if("string"==typeof e)return a(e);for(n=[],i=e.length,r=0;r<i;r++)n.push(a(e[r]));return t.apply(null,n)},o=function(e,t,n){2===arguments.length&&(n=t,t=null),r(t||[],function(){s(e,n,arguments)})},s=function(e,t,n){var o,s={exports:t};"function"==typeof t&&(n.length||(n=[r,s.exports,s]),void 0!==(o=t.apply(null,n))&&(s.exports=o)),i[e]=s.exports},a=function(t){var n=i[t]||e[t];if(!n)throw new Error("`"+t+"` is undefined");return n},u=function(t){return e.__dollar=t,function(e){var t,n,r,o,s,a;for(t in a=function(e){return e&&e.charAt(0).toUpperCase()+e.substr(1)},i)if(n=e,i.hasOwnProperty(t)){for(s=a((r=t.split("/")).pop());o=a(r.shift());)n[o]=n[o]||{},n=n[o];n[s]=i[t]}return e}((n=e,a=r,(s=o)("dollar-third",[],function(){var e=n.require,t=n.__dollar||n.jQuery||n.Zepto||e("jquery")||e("zepto");if(!t)throw new Error("jQuery or Zepto not found!");return t}),s("dollar",["dollar-third"],function(e){return e}),s("promise-third",["dollar"],function(e){return{Deferred:e.Deferred,when:e.when,isPromise:function(e){return e&&"function"==typeof e.then}}}),s("promise",["promise-third"],function(e){return e}),s("base",["dollar","promise"],function(e,t){var i,r,o,s,a,u,c,l,f,h,d=function(){},p=Function.call;function g(e,t){return function(){return e.apply(t,arguments)}}return{version:"0.1.6",$:e,Deferred:t.Deferred,isPromise:t.isPromise,when:t.when,browser:(o=navigator.userAgent,s={},a=o.match(/WebKit\/([\d.]+)/),u=o.match(/Chrome\/([\d.]+)/)||o.match(/CriOS\/([\d.]+)/),c=o.match(/MSIE\s([\d\.]+)/)||o.match(/(?:trident)(?:.*rv:([\w.]+))?/i),l=o.match(/Firefox\/([\d.]+)/),f=o.match(/Safari\/([\d.]+)/),h=o.match(/OPR\/([\d.]+)/),a&&(s.webkit=parseFloat(a[1])),u&&(s.chrome=parseFloat(u[1])),c&&(s.ie=parseFloat(c[1])),l&&(s.firefox=parseFloat(l[1])),f&&(s.safari=parseFloat(f[1])),h&&(s.opera=parseFloat(h[1])),s),os:function(e){var t={},n=e.match(/(?:Android);?[\s\/]+([\d.]+)?/),i=e.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/);return n&&(t.android=parseFloat(n[1])),i&&(t.ios=parseFloat(i[1].replace(/_/g,"."))),t}(navigator.userAgent),inherits:function(t,n,i){var r,o,s;return"function"==typeof n?(r=n,n=null):r=n&&n.hasOwnProperty("constructor")?n.constructor:function(){return t.apply(this,arguments)},e.extend(!0,r,t,i||{}),r.__super__=t.prototype,r.prototype=(o=t.prototype,Object.create?Object.create(o):((s=function(){}).prototype=o,new s)),n&&e.extend(!0,r.prototype,n),r},noop:d,bindFn:g,log:n.console?g(console.log,console):d,nextTick:function(e){setTimeout(e,1)},slice:(r=[].slice,function(){return p.apply(r,arguments)}),guid:(i=0,function(e){for(var t=(+new Date).toString(32),n=0;n<5;n++)t+=Math.floor(65535*Math.random()).toString(32);return(e||"wu_")+t+(i++).toString(32)}),formatSize:function(e,t,n){var i,r;for(n=n||["B","K","M","G","TB"];(i=n.shift())&&e>1024;)e/=1024;return r=0==t?e.toFixed():1==t?e.toFixed(1):e.toFixed(2),("B"===i?e:r)+i}}}),s("mediator",["base"],function(e){var t,n=e.$,i=[].slice,r=/\s+/;function o(e,t,i,r){return n.grep(e,function(e){return e&&(!t||e.e===t)&&(!i||e.cb===i||e.cb._cb===i)&&(!r||e.ctx===r)})}function s(e,t,i){n.each((e||"").split(r),function(e,n){i(n,t)})}function a(e,t){for(var n,i=!1,r=-1,o=e.length;++r<o;)if(!1===(n=e[r]).cb.apply(n.ctx2,t)){i=!0;break}return!i}return t={on:function(e,t,n){var i,r=this;return t?(i=this._events||(this._events=[]),s(e,t,function(e,t){var o={e:e};o.cb=t,o.ctx=n,o.ctx2=n||r,o.id=i.length,i.push(o)}),this):this},once:function(e,t,n){var i=this;return t?(s(e,t,function(e,t){var r=function(){return i.off(e,r),t.apply(n||i,arguments)};r._cb=t,i.on(e,r,n)}),i):i},off:function(e,t,i){var r=this._events;return r?e||t||i?(s(e,t,function(e,t){n.each(o(r,e,t,i),function(){delete r[this.id]})}),this):(this._events=[],this):this},trigger:function(e){var t,n,r;return this._events&&e?(t=i.call(arguments,1),n=o(this._events,e),r=o(this._events,"all"),a(n,t)&&a(r,arguments)):this}},n.extend({installTo:function(e){return n.extend(e,t)}},t)}),s("uploader",["base","mediator"],function(e,t){var n=e.$;function i(e){this.options=n.extend(!0,{},i.options,e),this._init(this.options)}return i.options={},t.installTo(i.prototype),n.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",cancelFile:"cancel-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",md5File:"md5-file",getDimension:"get-dimension",addButton:"add-btn",predictRuntimeType:"predict-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(e,t){i.prototype[e]=function(){return this.request(t,arguments)}}),n.extend(i.prototype,{state:"pending",_init:function(e){var t=this;t.request("init",e,function(){t.state="ready",t.trigger("ready")})},option:function(e,t){var i=this.options;if(!(arguments.length>1))return e?i[e]:i;n.isPlainObject(t)&&n.isPlainObject(i[e])?n.extend(i[e],t):i[e]=t},getStats:function(){var e=this.request("get-stats");return e?{successNum:e.numOfSuccess,progressNum:e.numOfProgress,cancelNum:e.numOfCancel,invalidNum:e.numOfInvalid,uploadFailNum:e.numOfUploadFailed,queueNum:e.numOfQueue,interruptNum:e.numofInterrupt}:{}},trigger:function(e){var i=[].slice.call(arguments,1),r=this.options,o="on"+e.substring(0,1).toUpperCase()+e.substring(1);return!(!1===t.trigger.apply(this,arguments)||n.isFunction(r[o])&&!1===r[o].apply(this,i)||n.isFunction(this[o])&&!1===this[o].apply(this,i)||!1===t.trigger.apply(t,[this,e].concat(i)))},destroy:function(){this.request("destroy",arguments),this.off()},request:e.noop}),e.create=i.create=function(e){return new i(e)},e.Uploader=i,i}),s("runtime/runtime",["base","mediator"],function(e,t){var n=e.$,i={},r=function(e){for(var t in e)if(e.hasOwnProperty(t))return t;return null};function o(t){this.options=n.extend({container:document.body},t),this.uid=e.guid("rt_")}return n.extend(o.prototype,{getContainer:function(){var e,t,i=this.options;return this._container?this._container:(e=n(i.container||document.body),(t=n(document.createElement("div"))).attr("id","rt_"+this.uid),t.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.append(t),e.addClass("webuploader-container"),this._container=t,this._parent=e,t)},init:e.noop,exec:e.noop,destroy:function(){this._container&&this._container.remove(),this._parent&&this._parent.removeClass("webuploader-container"),this.off()}}),o.orders="html5,flash",o.addRuntime=function(e,t){i[e]=t},o.hasRuntime=function(e){return!!(e?i[e]:r(i))},o.create=function(e,t){var s;if(t=t||o.orders,n.each(t.split(/\s*,\s*/g),function(){if(i[this])return s=this,!1}),!(s=s||r(i)))throw new Error("Runtime Error");return new i[s](e)},t.installTo(o.prototype),o}),s("runtime/client",["base","mediator","runtime/runtime"],function(e,t,n){var i,r;function o(t,r){var o,s,a=e.Deferred();this.uid=e.guid("client_"),this.runtimeReady=function(e){return a.done(e)},this.connectRuntime=function(t,s){if(o)throw new Error("already connected!");return a.done(s),"string"==typeof t&&i.get(t)&&(o=i.get(t)),(o=o||i.get(null,r))?(e.$.extend(o.options,t),o.__promise.then(a.resolve),o.__client++):((o=n.create(t,t.runtimeOrder)).__promise=a.promise(),o.once("ready",a.resolve),o.init(),i.add(o),o.__client=1),r&&(o.__standalone=r),o},this.getRuntime=function(){return o},this.disconnectRuntime=function(){o&&(o.__client--,o.__client<=0&&(i.remove(o),delete o.__promise,o.destroy()),o=null)},this.exec=function(){if(o){var n=e.slice(arguments);return t&&n.unshift(t),o.exec.apply(this,n)}},this.getRuid=function(){return o&&o.uid},this.destroy=(s=this.destroy,function(){s&&s.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()})}return r={},i={add:function(e){r[e.uid]=e},get:function(e,t){var n;if(e)return r[e];for(n in r)if(!t||!r[n].__standalone)return r[n];return null},remove:function(e){delete r[e.uid]}},t.installTo(o.prototype),o}),s("lib/dnd",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},r.options,e)).container=i(e.container),e.container.length&&n.call(this,"DragAndDrop")}return r.options={accept:null,disableGlobalDnd:!1},e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})}}),t.installTo(r.prototype),r}),s("widgets/widget",["base","uploader"],function(e,t){var n=e.$,i=t.prototype._init,r=t.prototype.destroy,o={},s=[];function a(e){this.owner=e,this.options=e.options}return n.extend(a.prototype,{init:e.noop,invoke:function(e,t){var i=this.responseMap;return i&&e in i&&i[e]in this&&n.isFunction(this[i[e]])?this[i[e]].apply(this,t):o},request:function(){return this.owner.request.apply(this.owner,arguments)}}),n.extend(t.prototype,{_init:function(){var e=this,t=e._widgets=[],r=e.options.disableWidgets||"";return n.each(s,function(n,i){(!r||!~r.indexOf(i._name))&&t.push(new i(e))}),i.apply(e,arguments)},request:function(t,i,r){var s,a,u,c=0,l=this._widgets,f=l&&l.length,h=[],d=[];for(i=function(e){if(!e)return!1;var t=e.length,i=n.type(e);return!(1!==e.nodeType||!t)||"array"===i||"function"!==i&&"string"!==i&&(0===t||"number"==typeof t&&t>0&&t-1 in e)}(i)?i:[i];c<f;c++)(s=l[c].invoke(t,i))!==o&&(e.isPromise(s)?d.push(s):h.push(s));return r||d.length?(a=e.when.apply(e,d))[u=a.pipe?"pipe":"then"](function(){var t=e.Deferred(),n=arguments;return 1===n.length&&(n=n[0]),setTimeout(function(){t.resolve(n)},1),t.promise()})[r?u:"done"](r||e.noop):h[0]},destroy:function(){r.apply(this,arguments),this._widgets=null}}),t.register=a.register=function(t,i){var r,o={init:"init",destroy:"destroy",name:"anonymous"};return 1===arguments.length?(i=t,n.each(i,function(e){"_"!==e[0]&&"name"!==e?o[e.replace(/[A-Z]/g,"-$&").toLowerCase()]=e:"name"===e&&(o.name=i.name)})):o=n.extend(o,t),i.responseMap=o,(r=e.inherits(a,i))._name=o.name,s.push(r),r},t.unRegister=a.unRegister=function(e){if(e&&"anonymous"!==e)for(var t=s.length;t--;)s[t]._name===e&&s.splice(t,1)},a}),s("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(e,t,n){var i=e.$;return t.options.dnd="",t.register({name:"dnd",init:function(t){if(t.dnd&&"html5"===this.request("predict-runtime-type")){var r,o=this,s=e.Deferred(),a=i.extend({},{disableGlobalDnd:t.disableGlobalDnd,container:t.dnd,accept:t.accept});return this.dnd=r=new n(a),r.once("ready",s.resolve),r.on("drop",function(e){o.request("add-file",[e])}),r.on("accept",function(e){return o.owner.trigger("dndAccept",e)}),r.init(),s.promise()}},destroy:function(){this.dnd&&this.dnd.destroy()}})}),s("lib/filepaste",["base","mediator","runtime/client"],function(e,t,n){var i=e.$;function r(e){(e=this.options=i.extend({},e)).container=i(e.container||document.body),n.call(this,"FilePaste")}return e.inherits(n,{constructor:r,init:function(){var e=this;e.connectRuntime(e.options,function(){e.exec("init"),e.trigger("ready")})}}),t.installTo(r.prototype),r}),s("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(e,t,n){var i=e.$;return t.register({name:"paste",init:function(t){if(t.paste&&"html5"===this.request("predict-runtime-type")){var r,o=this,s=e.Deferred(),a=i.extend({},{container:t.paste,accept:t.accept});return this.paste=r=new n(a),r.once("ready",s.resolve),r.on("paste",function(e){o.owner.request("add-file",[e])}),r.init(),s.promise()}},destroy:function(){this.paste&&this.paste.destroy()}})}),s("lib/blob",["base","runtime/client"],function(e,t){function n(e,n){this.source=n,this.ruid=e,this.size=n.size||0,!n.type&&this.ext&&~"jpg,jpeg,png,gif,bmp".indexOf(this.ext)?this.type="image/"+("jpg"===this.ext?"jpeg":this.ext):this.type=n.type||"application/octet-stream",t.call(this,"Blob"),this.uid=n.uid||this.uid,e&&this.connectRuntime(e)}return e.inherits(t,{constructor:n,slice:function(e,t){return this.exec("slice",e,t)},getSource:function(){return this.source}}),n}),s("lib/file",["base","lib/blob"],function(e,t){var n=1,i=/\.([^.]+)$/;return e.inherits(t,function(e,r){var o;this.name=r.name||"untitled"+n++,!(o=i.exec(r.name)?RegExp.$1.toLowerCase():"")&&r.type&&(o=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(r.type)?RegExp.$1.toLowerCase():"",this.name+="."+o),this.ext=o,this.lastModifiedDate=r.lastModifiedDate||(new Date).toLocaleString(),t.apply(this,arguments)})}),s("lib/filepicker",["base","runtime/client","lib/file"],function(e,t,i){var r=e.$;function o(e){if((e=this.options=r.extend({},o.options,e)).container=r(e.id),!e.container.length)throw new Error("按钮指定错误");e.innerHTML=e.innerHTML||e.label||e.container.html()||"",e.button=r(e.button||document.createElement("div")),e.button.html(e.innerHTML),e.container.html(e.button),t.call(this,"FilePicker",!0)}return o.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file",style:"webuploader-pick"},e.inherits(t,{constructor:o,init:function(){var t=this,o=t.options,s=o.button,a=o.style;a&&s.addClass("webuploader-pick"),t.on("all",function(e){var n;switch(e){case"mouseenter":a&&s.addClass("webuploader-pick-hover");break;case"mouseleave":a&&s.removeClass("webuploader-pick-hover");break;case"change":n=t.exec("getFiles"),t.trigger("select",r.map(n,function(e){return(e=new i(t.getRuid(),e))._refer=o.container,e}),o.container)}}),t.connectRuntime(o,function(){t.refresh(),t.exec("init",o),t.trigger("ready")}),this._resizeHandler=e.bindFn(this.refresh,this),r(n).on("resize",this._resizeHandler)},refresh:function(){var e=this.getRuntime().getContainer(),t=this.options.button,n=t.outerWidth?t.outerWidth():t.width(),i=t.outerHeight?t.outerHeight():t.height(),r=t.offset();n&&i&&e.css({bottom:"auto",right:"auto",width:n+"px",height:i+"px"}).offset(r)},enable:function(){this.options.button.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var e=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),e.addClass("webuploader-pick-disable")},destroy:function(){var e=this.options.button;r(n).off("resize",this._resizeHandler),e.removeClass("webuploader-pick-disable webuploader-pick-hover webuploader-pick")}}),o}),s("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(e,t,n){var i=e.$;return i.extend(t.options,{pick:null,accept:null}),t.register({name:"picker",init:function(e){return this.pickers=[],e.pick&&this.addBtn(e.pick)},refresh:function(){i.each(this.pickers,function(){this.refresh()})},addBtn:function(t){var r=this,o=r.options,s=o.accept,a=[];if(t)return i.isPlainObject(t)||(t={id:t}),i(t.id).each(function(){var u,c,l;l=e.Deferred(),u=i.extend({},t,{accept:i.isPlainObject(s)?[s]:s,swf:o.swf,runtimeOrder:o.runtimeOrder,id:this}),(c=new n(u)).once("ready",l.resolve),c.on("select",function(e){r.owner.request("add-file",[e])}),c.on("dialogopen",function(){r.owner.trigger("dialogOpen",c.button)}),c.init(),r.pickers.push(c),a.push(l.promise())}),e.when.apply(e,a)},disable:function(){i.each(this.pickers,function(){this.disable()})},enable:function(){i.each(this.pickers,function(){this.enable()})},destroy:function(){i.each(this.pickers,function(){this.destroy()}),this.pickers=null}})}),s("lib/image",["base","runtime/client","lib/blob"],function(e,t,n){var i=e.$;function r(e){this.options=i.extend({},r.options,e),t.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}return r.options={quality:90,crop:!1,preserveHeaders:!1,allowMagnify:!1},e.inherits(t,{constructor:r,info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._meta=e,this):this._meta},loadFromBlob:function(e){var t=this,n=e.getRuid();this.connectRuntime(n,function(){t.exec("init",t.options),t.exec("loadFromBlob",e)})},resize:function(){var t=e.slice(arguments);return this.exec.apply(this,["resize"].concat(t))},crop:function(){var t=e.slice(arguments);return this.exec.apply(this,["crop"].concat(t))},getAsDataUrl:function(e){return this.exec("getAsDataUrl",e)},getAsBlob:function(e){var t=this.exec("getAsBlob",e);return new n(this.getRuid(),t)}}),r}),s("widgets/image",["base","uploader","lib/image","widgets/widget"],function(e,t,n){var i,r,o,s,a=e.$;return r=0,o=[],s=function(){for(var e;o.length&&r<5242880;)e=o.shift(),r+=e[0],e[1]()},i=function(e,t,n){o.push([t,n]),e.once("destroy",function(){r-=t,setTimeout(s,1)}),setTimeout(s,1)},a.extend(t.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),t.register({name:"image",makeThumb:function(e,t,r,o){var s,u;(e=this.request("get-file",e)).type.match(/^image/)?(s=a.extend({},this.options.thumb),a.isPlainObject(r)&&(s=a.extend(s,r),r=null),r=r||s.width,o=o||s.height,(u=new n(s)).once("load",function(){e._info=e._info||u.info(),e._meta=e._meta||u.meta(),r<=1&&r>0&&(r=e._info.width*r),o<=1&&o>0&&(o=e._info.height*o),u.resize(r,o)}),u.once("complete",function(){t(!1,u.getAsDataUrl(s.type)),u.destroy()}),u.once("error",function(e){t(e||!0),u.destroy()}),i(u,e.source.size,function(){e._info&&u.info(e._info),e._meta&&u.meta(e._meta),u.loadFromBlob(e.source)})):t(!0)},beforeSendFile:function(t){var i,r,o=this.options.compress||this.options.resize,s=o&&o.compressSize||0,u=o&&o.noCompressIfLarger||!1;if(t=this.request("get-file",t),o&&~"image/jpeg,image/jpg".indexOf(t.type)&&!(t.size<s)&&!t._compressed)return o=a.extend({},o),r=e.Deferred(),i=new n(o),r.always(function(){i.destroy(),i=null}),i.once("error",r.reject),i.once("load",function(){var e=o.width,n=o.height;t._info=t._info||i.info(),t._meta=t._meta||i.meta(),e<=1&&e>0&&(e=t._info.width*e),n<=1&&n>0&&(n=t._info.height*n),i.resize(e,n)}),i.once("complete",function(){var e,n;try{e=i.getAsBlob(o.type),n=t.size,(!u||e.size<n)&&(t.source=e,t.size=e.size,t.trigger("resize",e.size,n)),t._compressed=!0,r.resolve()}catch(e){r.resolve()}}),t._info&&i.info(t._info),t._meta&&i.meta(t._meta),i.loadFromBlob(t.source),r.promise()}})}),s("file",["base","mediator"],function(e,t){var n=e.$,i="WU_FILE_",r=0,o=/\.([^.]+)$/,s={};function a(e){this.name=e.name||"Untitled",this.size=e.size||0,this.type=e.type||"application/octet-stream",this.lastModifiedDate=e.lastModifiedDate||1*new Date,this.id=i+r++,this.ext=o.exec(this.name)?RegExp.$1:"",this.statusText="",s[this.id]=a.Status.INITED,this.source=e,this.loaded=0,this.on("error",function(e){this.setStatus(a.Status.ERROR,e)})}return n.extend(a.prototype,{setStatus:function(e,t){var n=s[this.id];void 0!==t&&(this.statusText=t),e!==n&&(s[this.id]=e,this.trigger("statuschange",e,n))},getStatus:function(){return s[this.id]},getSource:function(){return this.source},destroy:function(){this.off(),delete s[this.id]}}),t.installTo(a.prototype),a.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},a}),s("queue",["base","mediator","file"],function(e,t,n){var i=e.$,r=n.Status;function o(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0,numofDeleted:0,numofInterrupt:0},this._queue=[],this._map={}}return i.extend(o.prototype,{append:function(e){return this._queue.push(e),this._fileAdded(e),this},prepend:function(e){return this._queue.unshift(e),this._fileAdded(e),this},getFile:function(e){return"string"!=typeof e?e:this._map[e]},fetch:function(e){var t,n,i=this._queue.length;for(e=e||r.QUEUED,t=0;t<i;t++)if(e===(n=this._queue[t]).getStatus())return n;return null},sort:function(e){"function"==typeof e&&this._queue.sort(e)},getFiles:function(){for(var e,t=[].slice.call(arguments,0),n=[],r=0,o=this._queue.length;r<o;r++)e=this._queue[r],t.length&&!~i.inArray(e.getStatus(),t)||n.push(e);return n},removeFile:function(e){this._map[e.id]&&(delete this._map[e.id],e.destroy(),this.stats.numofDeleted++)},_fileAdded:function(e){var t=this;this._map[e.id]||(this._map[e.id]=e,e.on("statuschange",function(e,n){t._onFileStatusChange(e,n)}))},_onFileStatusChange:function(e,t){var n=this.stats;switch(t){case r.PROGRESS:n.numOfProgress--;break;case r.QUEUED:n.numOfQueue--;break;case r.ERROR:n.numOfUploadFailed--;break;case r.INVALID:n.numOfInvalid--;break;case r.INTERRUPT:n.numofInterrupt--}switch(e){case r.QUEUED:n.numOfQueue++;break;case r.PROGRESS:n.numOfProgress++;break;case r.ERROR:n.numOfUploadFailed++;break;case r.COMPLETE:n.numOfSuccess++;break;case r.CANCELLED:n.numOfCancel++;break;case r.INVALID:n.numOfInvalid++;break;case r.INTERRUPT:n.numofInterrupt++}}}),t.installTo(o.prototype),o}),s("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(e,t,n,i,r,o){var s=e.$,a=/\.\w+$/,u=i.Status;return t.register({name:"queue",init:function(t){var i,r,a,u,c,l,f,h=this;if(s.isPlainObject(t.accept)&&(t.accept=[t.accept]),t.accept){for(c=[],a=0,r=t.accept.length;a<r;a++)(u=t.accept[a].extensions)&&c.push(u);c.length&&(l="\\."+c.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),h.accept=new RegExp(l,"i")}if(h.queue=new n,h.stats=h.queue.stats,"html5"===this.request("predict-runtime-type"))return i=e.Deferred(),this.placeholder=f=new o("Placeholder"),f.connectRuntime({runtimeOrder:"html5"},function(){h._ruid=f.getRuid(),i.resolve()}),i.promise()},_wrapFile:function(e){if(!(e instanceof i)){if(!(e instanceof r)){if(!this._ruid)throw new Error("Can't add external files.");e=new r(this._ruid,e)}e=new i(e)}return e},acceptFile:function(e){return!(!e||!e.size||this.accept&&a.exec(e.name)&&!this.accept.test(e.name))},_addFile:function(e){var t=this;if(e=t._wrapFile(e),t.owner.trigger("beforeFileQueued",e)){if(t.acceptFile(e))return t.queue.append(e),t.owner.trigger("fileQueued",e),e;t.owner.trigger("error","Q_TYPE_DENIED",e)}},getFile:function(e){return this.queue.getFile(e)},addFile:function(e){var t=this;e.length||(e=[e]),(e=s.map(e,function(e){return t._addFile(e)})).length&&(t.owner.trigger("filesQueued",e),t.options.auto&&setTimeout(function(){t.request("start-upload")},20))},getStats:function(){return this.stats},removeFile:function(e,t){e=e.id?e:this.queue.getFile(e),this.request("cancel-file",e),t&&this.queue.removeFile(e)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(e,t){var n,i,r;if(e)return(e=e.id?e:this.queue.getFile(e)).setStatus(u.QUEUED),void(t||this.request("start-upload"));for(i=0,r=(n=this.queue.getFiles(u.ERROR)).length;i<r;i++)(e=n[i]).setStatus(u.QUEUED);this.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.owner.trigger("reset"),this.queue=new n,this.stats=this.queue.stats},destroy:function(){this.reset(),this.placeholder&&this.placeholder.destroy()}})}),s("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(e,t){return e.support=function(){return t.hasRuntime.apply(t,arguments)},e.register({name:"runtime",init:function(){if(!this.predictRuntimeType())throw Error("Runtime Error")},predictRuntimeType:function(){var e,n,i=this.options.runtimeOrder||t.orders,r=this.type;if(!r)for(e=0,n=(i=i.split(/\s*,\s*/g)).length;e<n;e++)if(t.hasRuntime(i[e])){this.type=r=i[e];break}return r}})}),s("lib/transport",["base","runtime/client","mediator"],function(e,t,n){var i=e.$;function r(e){var n=this;e=n.options=i.extend(!0,{},r.options,e||{}),t.call(this,"Transport"),this._blob=null,this._formData=e.formData||{},this._headers=e.headers||{},this.on("progress",this._timeout),this.on("load error",function(){n.trigger("progress",1),clearTimeout(n._timer)})}return r.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},i.extend(r.prototype,{appendBlob:function(e,t,n){var i=this,r=i.options;i.getRuid()&&i.disconnectRuntime(),i.connectRuntime(t.ruid,function(){i.exec("init")}),i._blob=t,r.fileVal=e||r.fileVal,r.filename=n||r.filename},append:function(e,t){"object"==typeof e?i.extend(this._formData,e):this._formData[e]=t},setRequestHeader:function(e,t){"object"==typeof e?i.extend(this._headers,e):this._headers[e]=t},send:function(e){this.exec("send",e),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var e=this,t=e.options.timeout;t&&(clearTimeout(e._timer),e._timer=setTimeout(function(){e.abort(),e.trigger("error","timeout")},t))}}),n.installTo(r.prototype),r}),s("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(e,t,n,i){var r=e.$,o=e.isPromise,s=n.Status;r.extend(t.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:{}}),t.register({name:"upload",init:function(){var t=this.owner,n=this;this.runing=!1,this.progress=!1,t.on("startUpload",function(){n.progress=!0}).on("uploadFinished",function(){n.progress=!1}),this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this.__tick=e.bindFn(this._tick,this),t.on("uploadComplete",function(e){e.blocks&&r.each(e.blocks,function(e,t){t.transport&&(t.transport.abort(),t.transport.destroy()),delete t.transport}),delete e.blocks,delete e.remaning})},reset:function(){this.request("stop-upload",!0),this.runing=!1,this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this._trigged=!1,this._promise=null},startUpload:function(t){var n=this;if(r.each(n.request("get-files",s.INVALID),function(){n.request("remove-file",this)}),t?(t=t.id?t:n.request("get-file",t)).getStatus()===s.INTERRUPT?(t.setStatus(s.QUEUED),r.each(n.pool,function(e,n){n.file===t&&(n.transport&&n.transport.send(),t.setStatus(s.PROGRESS))})):t.getStatus()!==s.PROGRESS&&t.setStatus(s.QUEUED):r.each(n.request("get-files",[s.INITED]),function(){this.setStatus(s.QUEUED)}),n.runing)return e.nextTick(n.__tick);n.runing=!0;var i=[];t||r.each(n.pool,function(e,t){var r=t.file;r.getStatus()===s.INTERRUPT&&(n._trigged=!1,i.push(r),t.transport&&t.transport.send())}),r.each(i,function(){this.setStatus(s.PROGRESS)}),t||r.each(n.request("get-files",s.INTERRUPT),function(){this.setStatus(s.PROGRESS)}),n._trigged=!1,e.nextTick(n.__tick),n.owner.trigger("startUpload")},stopUpload:function(t,n){var i,o=this;if(!0===t&&(n=t,t=null),!1!==o.runing){if(t){if((t=t.id?t:o.request("get-file",t)).getStatus()!==s.PROGRESS&&t.getStatus()!==s.QUEUED)return;return t.setStatus(s.INTERRUPT),r.each(o.pool,function(e,n){if(n.file===t)return i=n,!1}),i.transport&&i.transport.abort(),n&&(o._putback(i),o._popBlock(i)),e.nextTick(o.__tick)}o.runing=!1,this._promise&&this._promise.file&&this._promise.file.setStatus(s.INTERRUPT),n&&r.each(o.pool,function(e,t){t.transport&&t.transport.abort(),t.file.setStatus(s.INTERRUPT)}),o.owner.trigger("stopUpload")}},cancelFile:function(e){(e=e.id?e:this.request("get-file",e)).blocks&&r.each(e.blocks,function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)}),e.setStatus(s.CANCELLED),this.owner.trigger("fileDequeued",e)},isInProgress:function(){return!!this.progress},_getStats:function(){return this.request("get-stats")},skipFile:function(e,t){(e=e.id?e:this.request("get-file",e)).setStatus(t||s.COMPLETE),e.skipped=!0,e.blocks&&r.each(e.blocks,function(e,t){var n=t.transport;n&&(n.abort(),n.destroy(),delete t.transport)}),this.owner.trigger("uploadSkip",e)},_tick:function(){var t,n,i=this,r=i.options;if(i._promise)return i._promise.always(i.__tick);i.pool.length<r.threads&&(n=i._nextBlock())?(i._trigged=!1,t=function(t){i._promise=null,t&&t.file&&i._startSend(t),e.nextTick(i.__tick)},i._promise=o(n)?n.always(t):t(n)):i.remaning||i._getStats().numOfQueue||i._getStats().numofInterrupt||(i.runing=!1,i._trigged||e.nextTick(function(){i.owner.trigger("uploadFinished")}),i._trigged=!0)},_putback:function(e){e.cuted.unshift(e),~this.stack.indexOf(e.cuted)||this.stack.unshift(e.cuted)},_getStack:function(){for(var e,t=0;e=this.stack[t++];){if(e.has()&&e.file.getStatus()===s.PROGRESS)return e;(!e.has()||e.file.getStatus()!==s.PROGRESS&&e.file.getStatus()!==s.INTERRUPT)&&this.stack.splice(--t,1)}return null},_nextBlock:function(){var e,t,n,i,r=this,s=r.options;return(e=this._getStack())?(s.prepareNextFile&&!r.pending.length&&r._prepareNextFile(),e.shift()):r.runing?(!r.pending.length&&r._getStats().numOfQueue&&r._prepareNextFile(),t=r.pending.shift(),n=function(t){return t?(e=function(e,t){var n,i,r=[],o=e.source.size,s=t?Math.ceil(o/t):1,a=0,u=0;for(i={file:e,has:function(){return!!r.length},shift:function(){return r.shift()},unshift:function(e){r.unshift(e)}};u<s;)n=Math.min(t,o-a),r.push({file:e,start:a,end:t?a+n:o,total:o,chunks:s,chunk:u++,cuted:i}),a+=n;return e.blocks=r.concat(),e.remaning=r.length,i}(t,s.chunked?s.chunkSize:0),r.stack.push(e),e.shift()):null},o(t)?(i=t.file,(t=t[t.pipe?"pipe":"then"](n)).file=i,t):n(t)):void 0},_prepareNextFile:function(){var e,t=this,n=t.request("fetch-file"),i=t.pending;n&&(e=t.request("before-send-file",n,function(){return n.getStatus()===s.PROGRESS||n.getStatus()===s.INTERRUPT?n:t._finishFile(n)}),t.owner.trigger("uploadStart",n),n.setStatus(s.PROGRESS),e.file=n,e.done(function(){var t=r.inArray(e,i);~t&&i.splice(t,1,n)}),e.fail(function(e){n.setStatus(s.ERROR,e),t.owner.trigger("uploadError",n,e),t.owner.trigger("uploadComplete",n)}),i.push(e))},_popBlock:function(e){var t=r.inArray(e,this.pool);this.pool.splice(t,1),e.file.remaning--,this.remaning--},_startSend:function(t){var n=this,i=t.file;i.getStatus()===s.PROGRESS?(n.pool.push(t),n.remaning++,t.blob=1===t.chunks?i.source:i.source.slice(t.start,t.end),n.request("before-send",t,function(){i.getStatus()===s.PROGRESS?n._doSend(t):(n._popBlock(t),e.nextTick(n.__tick))}).fail(function(){1===i.remaning?n._finishFile(i).always(function(){t.percentage=1,n._popBlock(t),n.owner.trigger("uploadComplete",i),e.nextTick(n.__tick)}):(t.percentage=1,n.updateFileProgress(i),n._popBlock(t),e.nextTick(n.__tick))})):i.getStatus()===s.INTERRUPT&&n._putback(t)},_doSend:function(t){var n,o,a=this,u=a.owner,c=a.options,l=t.file,f=new i(c),h=r.extend({},c.formData),d=r.extend({},c.headers);t.transport=f,f.on("destroy",function(){delete t.transport,a._popBlock(t),e.nextTick(a.__tick)}),f.on("progress",function(e){t.percentage=e,a.updateFileProgress(l)}),n=function(e){var n;return(o=f.getResponseAsJson()||{})._raw=f.getResponse(),n=function(t){e=t},u.trigger("uploadAccept",t,o,n)||(e=e||"server"),e},f.on("error",function(e,i){t.retried=t.retried||0,t.chunks>1&&~"http,abort".indexOf(e)&&t.retried<c.chunkRetry?(t.retried++,f.send()):(i||"server"!==e||(e=n(e)),l.setStatus(s.ERROR,e),u.trigger("uploadError",l,e),u.trigger("uploadComplete",l))}),f.on("load",function(){var e;(e=n())?f.trigger("error",e,!0):1===l.remaning?a._finishFile(l,o):f.destroy()}),h=r.extend(h,{id:l.id,name:l.name,type:l.type,lastModifiedDate:l.lastModifiedDate,size:l.size}),t.chunks>1&&r.extend(h,{chunks:t.chunks,chunk:t.chunk}),u.trigger("uploadBeforeSend",t,h,d),f.appendBlob(c.fileVal,t.blob,l.name),f.append(h),f.setRequestHeader(d),f.send()},_finishFile:function(e,t,n){var i=this.owner;return i.request("after-send-file",arguments,function(){e.setStatus(s.COMPLETE),i.trigger("uploadSuccess",e,t,n)}).fail(function(t){e.getStatus()===s.PROGRESS&&e.setStatus(s.ERROR,t),i.trigger("uploadError",e,t)}).always(function(){i.trigger("uploadComplete",e)})},updateFileProgress:function(e){var t,n=0;e.blocks&&(r.each(e.blocks,function(e,t){n+=(t.percentage||0)*(t.end-t.start)}),t=n/e.size,this.owner.trigger("uploadProgress",e,t||0))}})}),s("widgets/validator",["base","uploader","file","widgets/widget"],function(e,t,n){var i,r=e.$,o={};return i={addValidator:function(e,t){o[e]=t},removeValidator:function(e){delete o[e]}},t.register({name:"validator",init:function(){var t=this;e.nextTick(function(){r.each(o,function(){this.call(t.owner)})})}}),i.addValidator("fileNumLimit",function(){var e=this.options,t=0,n=parseInt(e.fileNumLimit,10),i=!0;n&&(this.on("beforeFileQueued",function(e){return t>=n&&i&&(i=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",n,e),setTimeout(function(){i=!0},1)),!(t>=n)}),this.on("fileQueued",function(){t++}),this.on("fileDequeued",function(){t--}),this.on("reset",function(){t=0}))}),i.addValidator("fileSizeLimit",function(){var e=this.options,t=0,n=parseInt(e.fileSizeLimit,10),i=!0;n&&(this.on("beforeFileQueued",function(e){var r=t+e.size>n;return r&&i&&(i=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",n,e),setTimeout(function(){i=!0},1)),!r}),this.on("fileQueued",function(e){t+=e.size}),this.on("fileDequeued",function(e){t-=e.size}),this.on("reset",function(){t=0}))}),i.addValidator("fileSingleSizeLimit",function(){var e=this.options.fileSingleSizeLimit;e&&this.on("beforeFileQueued",function(t){if(t.size>e)return t.setStatus(n.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",e,t),!1})}),i.addValidator("duplicate",function(){var e=this.options,t={};e.duplicate||(this.on("beforeFileQueued",function(e){var n=e.__hash||(e.__hash=function(e){for(var t=0,n=0,i=e.length;n<i;n++)t=e.charCodeAt(n)+(t<<6)+(t<<16)-t;return t}(e.name+e.size+e.lastModifiedDate));if(t[n])return this.trigger("error","F_DUPLICATE",e),!1}),this.on("fileQueued",function(e){var n=e.__hash;n&&(t[n]=!0)}),this.on("fileDequeued",function(e){var n=e.__hash;n&&delete t[n]}),this.on("reset",function(){t={}}))}),i}),s("lib/md5",["runtime/client","mediator"],function(e,t){function n(){e.call(this,"Md5")}return t.installTo(n.prototype),n.prototype.loadFromBlob=function(e){var t=this;t.getRuid()&&t.disconnectRuntime(),t.connectRuntime(e.ruid,function(){t.exec("init"),t.exec("loadFromBlob",e)})},n.prototype.getResult=function(){return this.exec("getResult")},n}),s("widgets/md5",["base","uploader","lib/md5","lib/blob","widgets/widget"],function(e,t,n,i){return t.register({name:"md5",md5File:function(t,r,o){var s=new n,a=e.Deferred(),u=t instanceof i?t:this.request("get-file",t).source;return s.on("progress load",function(e){e=e||{},a.notify(e.total?e.loaded/e.total:1)}),s.on("complete",function(){a.resolve(s.getResult())}),s.on("error",function(e){a.reject(e)}),arguments.length>1&&(o=o||0,(r=r||0)<0&&(r=u.size+r),o<0&&(o=u.size+o),o=Math.min(o,u.size),u=u.slice(r,o)),s.loadFromBlob(u),a.promise()}})}),s("runtime/compbase",[],function(){return function(e,t){this.owner=e,this.options=e.options,this.getRuntime=function(){return t},this.getRuid=function(){return t.uid},this.trigger=function(){return e.trigger.apply(e,arguments)}}}),s("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(e,t,i){var r="html5",o={};function s(){var n={},i=this,s=this.destroy;t.apply(i,arguments),i.type=r,i.exec=function(t,r){var s,a=this.uid,u=e.slice(arguments,2);if(o[t]&&(s=n[a]=n[a]||new o[t](this,i))[r])return s[r].apply(s,u)},i.destroy=function(){return s&&s.apply(this,arguments)}}return e.inherits(t,{constructor:s,init:function(){var e=this;setTimeout(function(){e.trigger("ready")},1)}}),s.register=function(t,n){return o[t]=e.inherits(i,n)},n.Blob&&n.FileReader&&n.DataView&&t.addRuntime(r,s),s}),s("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(e,t){return e.register("Blob",{slice:function(e,n){var i=this.owner.source;return i=(i.slice||i.webkitSlice||i.mozSlice).call(i,e,n),new t(this.getRuid(),i)}})}),s("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(e,t,n){var i=e.$,r="webuploader-dnd-";return t.register("DragAndDrop",{init:function(){var t=this.elem=this.options.container;this.dragEnterHandler=e.bindFn(this._dragEnterHandler,this),this.dragOverHandler=e.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=e.bindFn(this._dragLeaveHandler,this),this.dropHandler=e.bindFn(this._dropHandler,this),this.dndOver=!1,t.on("dragenter",this.dragEnterHandler),t.on("dragover",this.dragOverHandler),t.on("dragleave",this.dragLeaveHandler),t.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(i(document).on("dragover",this.dragOverHandler),i(document).on("drop",this.dropHandler))},_dragEnterHandler:function(e){var t,n=this,i=n._denied||!1;return e=e.originalEvent||e,n.dndOver||(n.dndOver=!0,(t=e.dataTransfer.items)&&t.length&&(n._denied=i=!n.trigger("accept",t)),n.elem.addClass(r+"over"),n.elem[i?"addClass":"removeClass"](r+"denied")),e.dataTransfer.dropEffect=i?"none":"copy",!1},_dragOverHandler:function(e){var t=this.elem.parent().get(0);return!(t&&!i.contains(t,e.currentTarget)||(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,e),1))},_dragLeaveHandler:function(){var e,t=this;return e=function(){t.dndOver=!1,t.elem.removeClass(r+"over "+r+"denied")},clearTimeout(t._leaveTimer),t._leaveTimer=setTimeout(e,100),!1},_dropHandler:function(e){var t,o,s=this,a=s.getRuid(),u=s.elem.parent().get(0);if(u&&!i.contains(u,e.currentTarget))return!1;t=(e=e.originalEvent||e).dataTransfer;try{o=t.getData("text/html")}catch(e){}return s.dndOver=!1,s.elem.removeClass(r+"over"),o?void 0:(s._getTansferFiles(t,function(e){s.trigger("drop",i.map(e,function(e){return new n(a,e)}))}),!1)},_getTansferFiles:function(t,n){var i,r,o,s,a,u,c,l=[],f=[];for(i=t.items,r=t.files,c=!(!i||!i[0].webkitGetAsEntry),a=0,u=r.length;a<u;a++)o=r[a],s=i&&i[a],c&&s.webkitGetAsEntry().isDirectory?f.push(this._traverseDirectoryTree(s.webkitGetAsEntry(),l)):l.push(o);e.when.apply(e,f).done(function(){l.length&&n(l)})},_traverseDirectoryTree:function(t,n){var i=e.Deferred(),r=this;return t.isFile?t.file(function(e){n.push(e),i.resolve()}):t.isDirectory&&t.createReader().readEntries(function(t){var o,s=t.length,a=[],u=[];for(o=0;o<s;o++)a.push(r._traverseDirectoryTree(t[o],u));e.when.apply(e,a).then(function(){n.push.apply(n,u),i.resolve()},i.reject)}),i.promise()},destroy:function(){var e=this.elem;e&&(e.off("dragenter",this.dragEnterHandler),e.off("dragover",this.dragOverHandler),e.off("dragleave",this.dragLeaveHandler),e.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(i(document).off("dragover",this.dragOverHandler),i(document).off("drop",this.dropHandler)))}})}),s("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(e,t,n){return t.register("FilePaste",{init:function(){var t,n,i,r,o=this.options,s=this.elem=o.container,a=".*";if(o.accept){for(t=[],n=0,i=o.accept.length;n<i;n++)(r=o.accept[n].mimeTypes)&&t.push(r);t.length&&(a=(a=t.join(",")).replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=a=new RegExp(a,"i"),this.hander=e.bindFn(this._pasteHander,this),s.on("paste",this.hander)},_pasteHander:function(e){var t,i,r,o,s,a=[],u=this.getRuid();for(o=0,s=(t=(e=e.originalEvent||e).clipboardData.items).length;o<s;o++)"file"===(i=t[o]).kind&&(r=i.getAsFile())&&a.push(new n(u,r));a.length&&(e.preventDefault(),e.stopPropagation(),this.trigger("paste",a))},destroy:function(){this.elem.off("paste",this.hander)}})}),s("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(e,t){var n=e.$;return t.register("FilePicker",{init:function(){var e,t,i,r,o=this.getRuntime().getContainer(),s=this,a=s.owner,u=s.options,c=this.label=n(document.createElement("label")),l=this.input=n(document.createElement("input"));if(l.attr("type","file"),l.attr("capture","camera"),l.attr("name",u.name),l.addClass("webuploader-element-invisible"),c.on("click",function(e){l.trigger("click"),e.stopPropagation(),a.trigger("dialogopen")}),c.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),u.multiple&&l.attr("multiple","multiple"),u.accept&&u.accept.length>0){for(e=[],t=0,i=u.accept.length;t<i;t++)e.push(u.accept[t].mimeTypes);l.attr("accept",e.join(","))}o.append(l),o.append(c),r=function(e){a.trigger(e.type)},l.on("change",function(e){var t,i=arguments.callee;s.files=e.target.files,(t=this.cloneNode(!0)).value=null,this.parentNode.replaceChild(t,this),l.off(),l=n(t).on("change",i).on("mouseenter mouseleave",r),a.trigger("change")}),c.on("mouseenter mouseleave",r)},getFiles:function(){return this.files},destroy:function(){this.input.off(),this.label.off()}})}),s("runtime/html5/util",["base"],function(e){var t=n.createObjectURL&&n||n.URL&&URL.revokeObjectURL&&URL||n.webkitURL,i=e.noop,r=i;return t&&(i=function(){return t.createObjectURL.apply(t,arguments)},r=function(){return t.revokeObjectURL.apply(t,arguments)}),{createObjectURL:i,revokeObjectURL:r,dataURL2Blob:function(e){var t,n,i,r,o,s;for(t=~(s=e.split(","))[0].indexOf("base64")?atob(s[1]):decodeURIComponent(s[1]),i=new ArrayBuffer(t.length),n=new Uint8Array(i),r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return o=s[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(i,o)},dataURL2ArrayBuffer:function(e){var t,n,i,r;for(t=~(r=e.split(","))[0].indexOf("base64")?atob(r[1]):decodeURIComponent(r[1]),n=new Uint8Array(t.length),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return n.buffer},arrayBufferToBlob:function(e,t){var i,r=n.BlobBuilder||n.WebKitBlobBuilder;return r?((i=new r).append(e),i.getBlob(t)):new Blob([e],t?{type:t}:{})},canvasToDataUrl:function(e,t,n){return e.toDataURL(t,n/100)},parseMeta:function(e,t){t(!1,{})},updateImageHead:function(e){return e}}}),s("runtime/html5/imagemeta",["runtime/html5/util"],function(e){var t;return t={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(e,t){var n=this,i=new FileReader;i.onload=function(){t(!1,n._parse(this.result)),i=i.onload=i.onerror=null},i.onerror=function(e){t(e.message),i=i.onload=i.onerror=null},e=e.slice(0,n.maxMetaDataSize),i.readAsArrayBuffer(e.getSource())},_parse:function(e,n){if(!(e.byteLength<6)){var i,r,o,s,a=new DataView(e),u=2,c=a.byteLength-4,l=u,f={};if(65496===a.getUint16(0)){for(;u<c&&((i=a.getUint16(u))>=65504&&i<=65519||65534===i)&&!(u+(r=a.getUint16(u+2)+2)>a.byteLength);){if(o=t.parsers[i],!n&&o)for(s=0;s<o.length;s+=1)o[s].call(t,a,u,r,f);l=u+=r}l>6&&(e.slice?f.imageHead=e.slice(2,l):f.imageHead=new Uint8Array(e).subarray(2,l))}return f}},updateImageHead:function(e,t){var n,i,r,o=this._parse(e,!0);return r=2,o.imageHead&&(r=2+o.imageHead.byteLength),i=e.slice?e.slice(r):new Uint8Array(e).subarray(r),(n=new Uint8Array(t.byteLength+2+i.byteLength))[0]=255,n[1]=216,n.set(new Uint8Array(t),2),n.set(new Uint8Array(i),t.byteLength+2),n.buffer}},e.parseMeta=function(){return t.parse.apply(t,arguments)},e.updateImageHead=function(){return t.updateImageHead.apply(t,arguments)},t}),s("runtime/html5/imagemeta/exif",["base","runtime/html5/imagemeta"],function(e,t){var n={ExifMap:function(){return this}};return n.ExifMap.prototype.map={Orientation:274},n.ExifMap.prototype.get=function(e){return this[e]||this[this.map[e]]},n.exifTagTypes={1:{getValue:function(e,t){return e.getUint8(t)},size:1},2:{getValue:function(e,t){return String.fromCharCode(e.getUint8(t))},size:1,ascii:!0},3:{getValue:function(e,t,n){return e.getUint16(t,n)},size:2},4:{getValue:function(e,t,n){return e.getUint32(t,n)},size:4},5:{getValue:function(e,t,n){return e.getUint32(t,n)/e.getUint32(t+4,n)},size:8},9:{getValue:function(e,t,n){return e.getInt32(t,n)},size:4},10:{getValue:function(e,t,n){return e.getInt32(t,n)/e.getInt32(t+4,n)},size:8}},n.exifTagTypes[7]=n.exifTagTypes[1],n.getExifValue=function(t,i,r,o,s,a){var u,c,l,f,h,d,p=n.exifTagTypes[o];if(p){if(!((c=(u=p.size*s)>4?i+t.getUint32(r+8,a):r+8)+u>t.byteLength)){if(1===s)return p.getValue(t,c,a);for(l=[],f=0;f<s;f+=1)l[f]=p.getValue(t,c+f*p.size,a);if(p.ascii){for(h="",f=0;f<l.length&&"\0"!==(d=l[f]);f+=1)h+=d;return h}return l}e.log("Invalid Exif data: Invalid data offset.")}else e.log("Invalid Exif data: Invalid tag type.")},n.parseExifTag=function(e,t,i,r,o){var s=e.getUint16(i,r);o.exif[s]=n.getExifValue(e,t,i,e.getUint16(i+2,r),e.getUint32(i+4,r),r)},n.parseExifTags=function(t,n,i,r,o){var s,a,u;if(i+6>t.byteLength)e.log("Invalid Exif data: Invalid directory offset.");else{if(!((a=i+2+12*(s=t.getUint16(i,r)))+4>t.byteLength)){for(u=0;u<s;u+=1)this.parseExifTag(t,n,i+2+12*u,r,o);return t.getUint32(a,r)}e.log("Invalid Exif data: Invalid directory size.")}},n.parseExifData=function(t,i,r,o){var s,a,u=i+10;if(1165519206===t.getUint32(i+4))if(u+8>t.byteLength)e.log("Invalid Exif data: Invalid segment size.");else if(0===t.getUint16(i+8)){switch(t.getUint16(u)){case 18761:s=!0;break;case 19789:s=!1;break;default:return void e.log("Invalid Exif data: Invalid byte alignment marker.")}42===t.getUint16(u+2,s)?(a=t.getUint32(u+4,s),o.exif=new n.ExifMap,a=n.parseExifTags(t,u,u+a,s,o)):e.log("Invalid Exif data: Missing TIFF marker.")}else e.log("Invalid Exif data: Missing byte alignment offset.")},t.parsers[65505].push(n.parseExifData),n}),s("runtime/html5/jpegencoder",[],function(e,t,n){function i(e){Math.round;var t,n,i,r,o,s=Math.floor,a=new Array(64),u=new Array(64),c=new Array(64),l=new Array(64),f=new Array(65535),h=new Array(65535),d=new Array(64),p=new Array(64),g=[],m=0,v=7,b=new Array(64),_=new Array(64),y=new Array(64),w=new Array(256),x=new Array(2048),R=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],E=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],k=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],F=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],T=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],A=[0,1,2,3,4,5,6,7,8,9,10,11],U=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],O=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function D(e,t){for(var n=0,i=0,r=new Array,o=1;o<=16;o++){for(var s=1;s<=e[o];s++)r[t[i]]=[],r[t[i]][0]=n,r[t[i]][1]=o,i++,n++;n*=2}return r}function I(e){for(var t=e[0],n=e[1]-1;n>=0;)t&1<<n&&(m|=1<<v),n--,--v<0&&(255==m?(C(255),C(0)):C(m),v=7,m=0)}function C(e){g.push(w[e])}function q(e){C(e>>8&255),C(255&e)}function B(e,t,n,i,r){for(var o,s=r[0],a=r[240],u=function(e,t){var n,i,r,o,s,a,u,c,l,f,h=0;for(l=0;l<8;++l){n=e[h],i=e[h+1],r=e[h+2],o=e[h+3],s=e[h+4],a=e[h+5],u=e[h+6];var p=n+(c=e[h+7]),g=n-c,m=i+u,v=i-u,b=r+a,_=r-a,y=o+s,w=o-s,x=p+y,R=p-y,E=m+b,k=m-b;e[h]=x+E,e[h+4]=x-E;var S=.707106781*(k+R);e[h+2]=R+S,e[h+6]=R-S;var F=.382683433*((x=w+_)-(k=v+g)),T=.5411961*x+F,A=1.306562965*k+F,U=.707106781*(E=_+v),O=g+U,D=g-U;e[h+5]=D+T,e[h+3]=D-T,e[h+1]=O+A,e[h+7]=O-A,h+=8}for(h=0,l=0;l<8;++l){n=e[h],i=e[h+8],r=e[h+16],o=e[h+24],s=e[h+32],a=e[h+40],u=e[h+48];var I=n+(c=e[h+56]),C=n-c,q=i+u,B=i-u,P=r+a,L=r-a,z=o+s,H=o-s,M=I+z,j=I-z,N=q+P,$=q-P;e[h]=M+N,e[h+32]=M-N;var Q=.707106781*($+j);e[h+16]=j+Q,e[h+48]=j-Q;var V=.382683433*((M=H+L)-($=B+C)),G=.5411961*M+V,J=1.306562965*$+V,W=.707106781*(N=L+B),X=C+W,Z=C-W;e[h+40]=Z+G,e[h+24]=Z-G,e[h+8]=X+J,e[h+56]=X-J,h++}for(l=0;l<64;++l)f=e[l]*t[l],d[l]=f>0?f+.5|0:f-.5|0;return d}(e,t),c=0;c<64;++c)p[R[c]]=u[c];var l=p[0]-n;n=p[0],0==l?I(i[0]):(I(i[h[o=32767+l]]),I(f[o]));for(var g=63;g>0&&0==p[g];g--);if(0==g)return I(s),n;for(var m,v=1;v<=g;){for(var b=v;0==p[v]&&v<=g;++v);var _=v-b;if(_>=16){m=_>>4;for(var y=1;y<=m;++y)I(a);_&=15}o=32767+p[v],I(r[(_<<4)+h[o]]),I(f[o]),v++}return 63!=g&&I(s),n}function P(e){e<=0&&(e=1),e>100&&(e=100),o!=e&&(function(e){for(var t=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var i=s((t[n]*e+50)/100);i<1?i=1:i>255&&(i=255),a[R[n]]=i}for(var r=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var f=s((r[o]*e+50)/100);f<1?f=1:f>255&&(f=255),u[R[o]]=f}for(var h=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var g=0;g<8;g++)c[d]=1/(a[R[d]]*h[p]*h[g]*8),l[d]=1/(u[R[d]]*h[p]*h[g]*8),d++}(e<50?Math.floor(5e3/e):Math.floor(200-2*e)),o=e)}this.encode=function(e,o){o&&P(o),g=new Array,m=0,v=7,q(65496),q(65504),q(16),C(74),C(70),C(73),C(70),C(0),C(1),C(1),C(0),q(1),q(1),C(0),C(0),function(){q(65499),q(132),C(0);for(var e=0;e<64;e++)C(a[e]);C(1);for(var t=0;t<64;t++)C(u[t])}(),function(e,t){q(65472),q(17),C(8),q(t),q(e),C(3),C(1),C(17),C(0),C(2),C(17),C(1),C(3),C(17),C(1)}(e.width,e.height),function(){q(65476),q(418),C(0);for(var e=0;e<16;e++)C(E[e+1]);for(var t=0;t<=11;t++)C(k[t]);C(16);for(var n=0;n<16;n++)C(S[n+1]);for(var i=0;i<=161;i++)C(F[i]);C(1);for(var r=0;r<16;r++)C(T[r+1]);for(var o=0;o<=11;o++)C(A[o]);C(17);for(var s=0;s<16;s++)C(U[s+1]);for(var a=0;a<=161;a++)C(O[a])}(),q(65498),q(12),C(3),C(1),C(0),C(2),C(17),C(3),C(17),C(0),C(63),C(0);var s=0,f=0,h=0;m=0,v=7,this.encode.displayName="_encode_";for(var d,p,w,R,D,L,z,H,M,j=e.data,N=e.width,$=e.height,Q=4*N,V=0;V<$;){for(d=0;d<Q;){for(L=D=Q*V+d,z=-1,H=0,M=0;M<64;M++)L=D+(H=M>>3)*Q+(z=4*(7&M)),V+H>=$&&(L-=Q*(V+1+H-$)),d+z>=Q&&(L-=d+z-Q+4),p=j[L++],w=j[L++],R=j[L++],b[M]=(x[p]+x[w+256>>0]+x[R+512>>0]>>16)-128,_[M]=(x[p+768>>0]+x[w+1024>>0]+x[R+1280>>0]>>16)-128,y[M]=(x[p+1280>>0]+x[w+1536>>0]+x[R+1792>>0]>>16)-128;s=B(b,c,s,t,i),f=B(_,l,f,n,r),h=B(y,l,h,n,r),d+=32}V+=8}if(v>=0){var G=[];G[1]=v+1,G[0]=(1<<v+1)-1,I(G)}q(65497);var J="data:image/jpeg;base64,"+btoa(g.join(""));return g=[],J},e||(e=50),function(){for(var e=String.fromCharCode,t=0;t<256;t++)w[t]=e(t)}(),t=D(E,k),n=D(T,A),i=D(S,F),r=D(U,O),function(){for(var e=1,t=2,n=1;n<=15;n++){for(var i=e;i<t;i++)h[32767+i]=n,f[32767+i]=[],f[32767+i][1]=n,f[32767+i][0]=i;for(var r=-(t-1);r<=-e;r++)h[32767+r]=n,f[32767+r]=[],f[32767+r][1]=n,f[32767+r][0]=t-1+r;e<<=1,t<<=1}}(),function(){for(var e=0;e<256;e++)x[e]=19595*e,x[e+256>>0]=38470*e,x[e+512>>0]=7471*e+32768,x[e+768>>0]=-11059*e,x[e+1024>>0]=-21709*e,x[e+1280>>0]=32768*e+8421375,x[e+1536>>0]=-27439*e,x[e+1792>>0]=-5329*e}(),P(e)}return i.encode=function(e,t){return new i(t).encode(e)},i}),s("runtime/html5/androidpatch",["runtime/html5/util","runtime/html5/jpegencoder","base"],function(e,t,n){var i,r=e.canvasToDataUrl;e.canvasToDataUrl=function(e,o,s){var a,u,c,l,f;return n.os.android?("image/jpeg"===o&&void 0===i&&(f=(l=r.apply(null,arguments)).split(","),l=(l=~f[0].indexOf("base64")?atob(f[1]):decodeURIComponent(f[1])).substring(0,2),i=255===l.charCodeAt(0)&&216===l.charCodeAt(1)),"image/jpeg"!==o||i?r.apply(null,arguments):(u=e.width,c=e.height,a=e.getContext("2d"),t.encode(a.getImageData(0,0,u,c),s))):r.apply(null,arguments)}}),s("runtime/html5/image",["base","runtime/html5/runtime","runtime/html5/util"],function(e,t,n){return t.register("Image",{modified:!1,init:function(){var e=this,t=new Image;t.onload=function(){e._info={type:e.type,width:this.width,height:this.height},e._metas||"image/jpeg"!==e.type?e.owner.trigger("load"):n.parseMeta(e._blob,function(t,n){e._metas=n,e.owner.trigger("load")})},t.onerror=function(){e.owner.trigger("error")},e._img=t},loadFromBlob:function(e){var t=this._img;this._blob=e,this.type=e.type,t.src=n.createObjectURL(e.getSource()),this.owner.once("load",function(){n.revokeObjectURL(t.src)})},resize:function(e,t){var n=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,n,e,t),this._blob=null,this.modified=!0,this.owner.trigger("complete","resize")},crop:function(e,t,n,i,r){var o=this._canvas||(this._canvas=document.createElement("canvas")),s=this.options,a=this._img,u=a.naturalWidth,c=a.naturalHeight,l=this.getOrientation();r=r||1,o.width=n,o.height=i,s.preserveHeaders||this._rotate2Orientaion(o,l),this._renderImageToCanvas(o,a,-e,-t,u*r,c*r),this._blob=null,this.modified=!0,this.owner.trigger("complete","crop")},getAsBlob:function(e){var t,i=this._blob,r=this.options;if(e=e||this.type,this.modified||this.type!==e){if(t=this._canvas,"image/jpeg"===e){if(i=n.canvasToDataUrl(t,e,r.quality),r.preserveHeaders&&this._metas&&this._metas.imageHead)return i=n.dataURL2ArrayBuffer(i),i=n.updateImageHead(i,this._metas.imageHead),i=n.arrayBufferToBlob(i,e)}else i=n.canvasToDataUrl(t,e);i=n.dataURL2Blob(i)}return i},getAsDataUrl:function(e){var t=this.options;return"image/jpeg"===(e=e||this.type)?n.canvasToDataUrl(this._canvas,e,t.quality):this._canvas.toDataURL(e)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(e){return e?(this._info=e,this):this._info},meta:function(e){return e?(this._metas=e,this):this._metas},destroy:function(){var e=this._canvas;this._img.onload=null,e&&(e.getContext("2d").clearRect(0,0,e.width,e.height),e.width=e.height=0,this._canvas=null),this._img.src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D",this._img=this._blob=null},_resize:function(e,t,n,i){var r,o,s,a,u,c=this.options,l=e.width,f=e.height,h=this.getOrientation();~[5,6,7,8].indexOf(h)&&(n^=i,n^=i^=n),r=Math[c.crop?"max":"min"](n/l,i/f),c.allowMagnify||(r=Math.min(1,r)),o=l*r,s=f*r,c.crop?(t.width=n,t.height=i):(t.width=o,t.height=s),a=(t.width-o)/2,u=(t.height-s)/2,c.preserveHeaders||this._rotate2Orientaion(t,h),this._renderImageToCanvas(t,e,a,u,o,s)},_rotate2Orientaion:function(e,t){var n=e.width,i=e.height,r=e.getContext("2d");switch(t){case 5:case 6:case 7:case 8:e.width=i,e.height=n}switch(t){case 2:r.translate(n,0),r.scale(-1,1);break;case 3:r.translate(n,i),r.rotate(Math.PI);break;case 4:r.translate(0,i),r.scale(1,-1);break;case 5:r.rotate(.5*Math.PI),r.scale(1,-1);break;case 6:r.rotate(.5*Math.PI),r.translate(0,-i);break;case 7:r.rotate(.5*Math.PI),r.translate(n,-i),r.scale(-1,1);break;case 8:r.rotate(-.5*Math.PI),r.translate(-n,0)}},_renderImageToCanvas:function(){if(!e.os.ios)return function(t){var n=e.slice(arguments,1),i=t.getContext("2d");i.drawImage.apply(i,n)};function t(e,t,n){var i,r,o=document.createElement("canvas"),s=o.getContext("2d"),a=0,u=n,c=n;for(o.width=1,o.height=n,s.drawImage(e,0,0),i=s.getImageData(0,0,1,n).data;c>a;)0===i[4*(c-1)+3]?u=c:a=c,c=u+a>>1;return 0==(r=c/n)?1:r}return e.os.ios>=7?function(e,n,i,r,o,s){var a=n.naturalWidth,u=n.naturalHeight,c=t(n,0,u);return e.getContext("2d").drawImage(n,0,0,a*c,u*c,i,r,o,s)}:function(e,n,i,r,o,s){var a,u,c,l,f,h,d,p=n.naturalWidth,g=n.naturalHeight,m=e.getContext("2d"),v=function(e){var t,n,i=e.naturalWidth;return i*e.naturalHeight>1048576&&((t=document.createElement("canvas")).width=t.height=1,(n=t.getContext("2d")).drawImage(e,1-i,0),0===n.getImageData(0,0,1,1).data[3])}(n),b="image/jpeg"===this.type,_=1024,y=0,w=0;for(v&&(p/=2,g/=2),m.save(),(a=document.createElement("canvas")).width=a.height=_,u=a.getContext("2d"),c=b?t(n,0,g):1,l=Math.ceil(_*o/p),f=Math.ceil(_*s/g/c);y<g;){for(h=0,d=0;h<p;)u.clearRect(0,0,_,_),u.drawImage(n,-h,-y),m.drawImage(a,0,0,_,_,i+d,r+w,l,f),h+=_,d+=l;y+=_,w+=f}m.restore(),a=u=null}}()})}),s("runtime/html5/transport",["base","runtime/html5/runtime"],function(e,t){var n=e.noop,i=e.$;return t.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var t,n,r,o=this.owner,s=this.options,a=this._initAjax(),u=o._blob,c=s.server;s.sendAsBinary?(c+=(/\?/.test(c)?"&":"?")+i.param(o._formData),n=u.getSource()):(t=new FormData,i.each(o._formData,function(e,n){t.append(e,n)}),t.append(s.fileVal,u.getSource(),s.filename||o._formData.name||"")),s.withCredentials&&"withCredentials"in a?(a.open(s.method,c,!0),a.withCredentials=!0):a.open(s.method,c),this._setRequestHeader(a,s.headers),n?(a.overrideMimeType&&a.overrideMimeType("application/octet-stream"),e.os.android?((r=new FileReader).onload=function(){a.send(this.result),r=r.onload=null},r.readAsArrayBuffer(n)):a.send(n)):a.send(t)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getStatus:function(){return this._status},abort:function(){var e=this._xhr;e&&(e.upload.onprogress=n,e.onreadystatechange=n,e.abort(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var e=this,t=new XMLHttpRequest;return!this.options.withCredentials||"withCredentials"in t||"undefined"==typeof XDomainRequest||(t=new XDomainRequest),t.upload.onprogress=function(t){var n=0;return t.lengthComputable&&(n=t.loaded/t.total),e.trigger("progress",n)},t.onreadystatechange=function(){if(4===t.readyState)return t.upload.onprogress=n,t.onreadystatechange=n,e._xhr=null,e._status=t.status,t.status>=200&&t.status<300?(e._response=t.responseText,e.trigger("load")):t.status>=500&&t.status<600?(e._response=t.responseText,e.trigger("error","server")):e.trigger("error",e._status?"http":"abort")},e._xhr=t,t},_setRequestHeader:function(e,t){i.each(t,function(t,n){e.setRequestHeader(t,n)})},_parseJson:function(e){var t;try{t=JSON.parse(e)}catch(e){t={}}return t}})}),s("runtime/html5/md5",["runtime/html5/runtime"],function(e){var t=function(e,t){return e+t&4294967295},n=function(e,n,i,r,o,s){return n=t(t(n,e),t(r,s)),t(n<<o|n>>>32-o,i)},i=function(e,t,i,r,o,s,a){return n(t&i|~t&r,e,t,o,s,a)},r=function(e,t,i,r,o,s,a){return n(t&r|i&~r,e,t,o,s,a)},o=function(e,t,i,r,o,s,a){return n(t^i^r,e,t,o,s,a)},s=function(e,t,i,r,o,s,a){return n(i^(t|~r),e,t,o,s,a)},a=function(e,n){var a=e[0],u=e[1],c=e[2],l=e[3];a=i(a,u,c,l,n[0],7,-680876936),l=i(l,a,u,c,n[1],12,-389564586),c=i(c,l,a,u,n[2],17,606105819),u=i(u,c,l,a,n[3],22,-1044525330),a=i(a,u,c,l,n[4],7,-176418897),l=i(l,a,u,c,n[5],12,1200080426),c=i(c,l,a,u,n[6],17,-1473231341),u=i(u,c,l,a,n[7],22,-45705983),a=i(a,u,c,l,n[8],7,1770035416),l=i(l,a,u,c,n[9],12,-1958414417),c=i(c,l,a,u,n[10],17,-42063),u=i(u,c,l,a,n[11],22,-1990404162),a=i(a,u,c,l,n[12],7,1804603682),l=i(l,a,u,c,n[13],12,-40341101),c=i(c,l,a,u,n[14],17,-1502002290),u=i(u,c,l,a,n[15],22,1236535329),a=r(a,u,c,l,n[1],5,-165796510),l=r(l,a,u,c,n[6],9,-1069501632),c=r(c,l,a,u,n[11],14,643717713),u=r(u,c,l,a,n[0],20,-373897302),a=r(a,u,c,l,n[5],5,-701558691),l=r(l,a,u,c,n[10],9,38016083),c=r(c,l,a,u,n[15],14,-660478335),u=r(u,c,l,a,n[4],20,-405537848),a=r(a,u,c,l,n[9],5,568446438),l=r(l,a,u,c,n[14],9,-1019803690),c=r(c,l,a,u,n[3],14,-187363961),u=r(u,c,l,a,n[8],20,1163531501),a=r(a,u,c,l,n[13],5,-1444681467),l=r(l,a,u,c,n[2],9,-51403784),c=r(c,l,a,u,n[7],14,1735328473),u=r(u,c,l,a,n[12],20,-1926607734),a=o(a,u,c,l,n[5],4,-378558),l=o(l,a,u,c,n[8],11,-2022574463),c=o(c,l,a,u,n[11],16,1839030562),u=o(u,c,l,a,n[14],23,-35309556),a=o(a,u,c,l,n[1],4,-1530992060),l=o(l,a,u,c,n[4],11,1272893353),c=o(c,l,a,u,n[7],16,-155497632),u=o(u,c,l,a,n[10],23,-1094730640),a=o(a,u,c,l,n[13],4,681279174),l=o(l,a,u,c,n[0],11,-358537222),c=o(c,l,a,u,n[3],16,-722521979),u=o(u,c,l,a,n[6],23,76029189),a=o(a,u,c,l,n[9],4,-640364487),l=o(l,a,u,c,n[12],11,-421815835),c=o(c,l,a,u,n[15],16,530742520),u=o(u,c,l,a,n[2],23,-995338651),a=s(a,u,c,l,n[0],6,-198630844),l=s(l,a,u,c,n[7],10,1126891415),c=s(c,l,a,u,n[14],15,-1416354905),u=s(u,c,l,a,n[5],21,-57434055),a=s(a,u,c,l,n[12],6,1700485571),l=s(l,a,u,c,n[3],10,-1894986606),c=s(c,l,a,u,n[10],15,-1051523),u=s(u,c,l,a,n[1],21,-2054922799),a=s(a,u,c,l,n[8],6,1873313359),l=s(l,a,u,c,n[15],10,-30611744),c=s(c,l,a,u,n[6],15,-1560198380),u=s(u,c,l,a,n[13],21,1309151649),a=s(a,u,c,l,n[4],6,-145523070),l=s(l,a,u,c,n[11],10,-1120210379),c=s(c,l,a,u,n[2],15,718787259),u=s(u,c,l,a,n[9],21,-343485551),e[0]=t(a,e[0]),e[1]=t(u,e[1]),e[2]=t(c,e[2]),e[3]=t(l,e[3])},u=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n},c=function(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n},l=function(e){var t,n,i,r,o,s,c=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=c;t+=64)a(l,u(e.substring(t-64,t)));for(n=(e=e.substring(t-64)).length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)i[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),t>55)for(a(l,i),t=0;t<16;t+=1)i[t]=0;return r=(r=8*c).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(r[2],16),s=parseInt(r[1],16)||0,i[14]=o,i[15]=s,a(l,i),l},f=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],h=function(e){var t,n="";for(t=0;t<4;t+=1)n+=f[e>>8*t+4&15]+f[e>>8*t&15];return n},d=function(e){var t;for(t=0;t<e.length;t+=1)e[t]=h(e[t]);return e.join("")},p=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==d(l("hello"))&&(t=function(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}),p.prototype.append=function(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),this.appendBinary(e),this},p.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,n=this._buff.length;for(t=64;t<=n;t+=64)a(this._state,u(this._buff.substring(t-64,t)));return this._buff=this._buff.substr(t-64),this},p.prototype.end=function(e){var t,n,i=this._buff,r=i.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)o[t>>2]|=i.charCodeAt(t)<<(t%4<<3);return this._finish(o,r),n=e?this._state:d(this._state),this.reset(),n},p.prototype._finish=function(e,t){var n,i,r,o=t;if(e[o>>2]|=128<<(o%4<<3),o>55)for(a(this._state,e),o=0;o<16;o+=1)e[o]=0;n=(n=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),i=parseInt(n[2],16),r=parseInt(n[1],16)||0,e[14]=i,e[15]=r,a(this._state,e)},p.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},p.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},p.hash=function(e,t){/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e)));var n=l(e);return t?n:d(n)},p.hashBinary=function(e,t){var n=l(e);return t?n:d(n)},(p.ArrayBuffer=function(){this.reset()}).prototype.append=function(e){var t,n=this._concatArrayBuffer(this._buff,e),i=n.length;for(this._length+=e.byteLength,t=64;t<=i;t+=64)a(this._state,c(n.subarray(t-64,t)));return this._buff=t-64<i?n.subarray(t-64):new Uint8Array(0),this},p.ArrayBuffer.prototype.end=function(e){var t,n,i=this._buff,r=i.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)o[t>>2]|=i[t]<<(t%4<<3);return this._finish(o,r),n=e?this._state:d(this._state),this.reset(),n},p.ArrayBuffer.prototype._finish=p.prototype._finish,p.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},p.ArrayBuffer.prototype.destroy=p.prototype.destroy,p.ArrayBuffer.prototype._concatArrayBuffer=function(e,t){var n=e.length,i=new Uint8Array(n+t.byteLength);return i.set(e),i.set(new Uint8Array(t),n),i},p.ArrayBuffer.hash=function(e,t){var n=function(e){var t,n,i,r,o,s,u=e.length,l=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=u;t+=64)a(l,c(e.subarray(t-64,t)));for(n=(e=t-64<u?e.subarray(t-64):new Uint8Array(0)).length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<n;t+=1)i[t>>2]|=e[t]<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),t>55)for(a(l,i),t=0;t<16;t+=1)i[t]=0;return r=(r=8*u).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(r[2],16),s=parseInt(r[1],16)||0,i[14]=o,i[15]=s,a(l,i),l}(new Uint8Array(e));return t?n:d(n)},e.register("Md5",{init:function(){},loadFromBlob:function(e){var t,n,i=e.getSource(),r=Math.ceil(i.size/2097152),o=0,s=this.owner,a=new p.ArrayBuffer,u=this,c=i.mozSlice||i.webkitSlice||i.slice;n=new FileReader,(t=function(){var l,f;l=2097152*o,f=Math.min(l+2097152,i.size),n.onload=function(t){a.append(t.target.result),s.trigger("progress",{total:e.size,loaded:f})},n.onloadend=function(){n.onloadend=n.onload=null,++o<r?setTimeout(t,1):setTimeout(function(){s.trigger("load"),u.result=a.end(),t=e=i=a=null,s.trigger("complete")},50)},n.readAsArrayBuffer(c.call(i,l,f))})()},getResult:function(){return this.result}})}),s("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(e,t,i){var r=e.$,o="flash",s={};function a(){var i={},r={},a=this.destroy,u=this,c=e.guid("webuploader_");t.apply(u,arguments),u.type=o,u.exec=function(t,n){var o,a=this.uid,c=e.slice(arguments,2);return r[a]=this,s[t]&&(i[a]||(i[a]=new s[t](this,u)),(o=i[a])[n])?o[n].apply(o,c):u.flashExec.apply(this,arguments)},n[c]=function(){var e=arguments;setTimeout(function(){(function(e,t){var n,i,o=e.type||e;i=(n=o.split("::"))[0],"Ready"===(o=n[1])&&i===u.uid?u.trigger("ready"):r[i]&&r[i].trigger(o.toLowerCase(),e,t)}).apply(null,e)},1)},this.jsreciver=c,this.destroy=function(){return a&&a.apply(this,arguments)},this.flashExec=function(t,n){var i=u.getFlash(),r=e.slice(arguments,2);return i.exec(this.uid,t,n,r)}}return e.inherits(t,{constructor:a,init:function(){var t,n=this.getContainer(),i=this.options;n.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),t='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+i.swf+'" ',e.browser.ie&&(t+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),t+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+i.swf+'" /><param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',n.html(t)},getFlash:function(){return this._flash?this._flash:(this._flash=r("#"+this.uid).get(0),this._flash)}}),a.register=function(t,n){return n=s[t]=e.inherits(i,r.extend({flashExec:function(){var e=this.owner;return this.getRuntime().flashExec.apply(e,arguments)}},n))},function(){var e;try{e=(e=navigator.plugins["Shockwave Flash"]).description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(t){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1],10)}()>=11.4&&t.addRuntime(o,a),a}),s("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(e,t){var n=e.$;return t.register("FilePicker",{init:function(e){var t,i,r=n.extend({},e);for(t=r.accept&&r.accept.length,i=0;i<t;i++)r.accept[i].title||(r.accept[i].title="Files");delete r.button,delete r.id,delete r.container,this.flashExec("FilePicker","init",r)},destroy:function(){this.flashExec("FilePicker","destroy")}})}),s("runtime/flash/image",["runtime/flash/runtime"],function(e){return e.register("Image",{loadFromBlob:function(e){var t=this.owner;t.info()&&this.flashExec("Image","info",t.info()),t.meta()&&this.flashExec("Image","meta",t.meta()),this.flashExec("Image","loadFromBlob",e.uid)}})}),s("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(e,t,i){var r=e.$;return t.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var e,t=this.owner,n=this.options,i=this._initAjax(),o=t._blob,s=n.server;i.connectRuntime(o.ruid),n.sendAsBinary?(s+=(/\?/.test(s)?"&":"?")+r.param(t._formData),e=o.uid):(r.each(t._formData,function(e,t){i.exec("append",e,t)}),i.exec("appendBlob",n.fileVal,o.uid,n.filename||t._formData.name||"")),this._setRequestHeader(i,n.headers),i.exec("send",{method:n.method,url:s,forceURLStream:n.forceURLStream,mimeType:"application/octet-stream"},e)},getStatus:function(){return this._status},getResponse:function(){return this._response||""},getResponseAsJson:function(){return this._responseJson},abort:function(){var e=this._xhr;e&&(e.exec("abort"),e.destroy(),this._xhr=e=null)},destroy:function(){this.abort()},_initAjax:function(){var e=this,t=new i("XMLHttpRequest");return t.on("uploadprogress progress",function(t){var n=t.loaded/t.total;return n=Math.min(1,Math.max(0,n)),e.trigger("progress",n)}),t.on("load",function(){var i,r=t.exec("getStatus"),o=!1,s="";return t.off(),e._xhr=null,r>=200&&r<300?o=!0:r>=500&&r<600?(o=!0,s="server"):s="http",o&&(e._response=t.exec("getResponse"),e._response=decodeURIComponent(e._response),i=function(e){try{return n.JSON&&n.JSON.parse?JSON.parse(e):new Function("return "+e).call()}catch(e){return{}}},e._responseJson=e._response?i(e._response):{}),t.destroy(),t=null,s?e.trigger("error",s):e.trigger("load")}),t.on("error",function(){t.off(),e._xhr=null,e.trigger("error","http")}),e._xhr=t,t},_setRequestHeader:function(e,t){r.each(t,function(t,n){e.exec("setRequestHeader",t,n)})}})}),s("runtime/flash/blob",["runtime/flash/runtime","lib/blob"],function(e,t){return e.register("Blob",{slice:function(e,n){var i=this.flashExec("Blob","slice",e,n);return new t(this.getRuid(),i)}})}),s("runtime/flash/md5",["runtime/flash/runtime"],function(e){return e.register("Md5",{init:function(){},loadFromBlob:function(e){return this.flashExec("Md5","loadFromBlob",e.uid)}})}),s("preset/all",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","widgets/md5","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/imagemeta/exif","runtime/html5/androidpatch","runtime/html5/image","runtime/html5/transport","runtime/html5/md5","runtime/flash/filepicker","runtime/flash/image","runtime/flash/transport","runtime/flash/blob","runtime/flash/md5"],function(e){return e}),s("widgets/log",["base","uploader","widgets/widget"],function(e,t){var n,i=e.$,r=" http://static.tieba.baidu.com/tb/pms/img/st.gif??",o=(location.hostname||location.host||"protected").toLowerCase();if(o&&/baidu/i.exec(o))return n={dv:3,master:"webuploader",online:/test/.exec(o)?0:1,module:"",product:o,type:0},t.register({name:"log",init:function(){var e=this.owner,t=0,n=0;e.on("error",function(e){s({type:2,c_error_code:e})}).on("uploadError",function(e,t){s({type:2,c_error_code:"UPLOAD_ERROR",c_reason:""+t})}).on("uploadComplete",function(e){t++,n+=e.size}).on("uploadFinished",function(){s({c_count:t,c_size:n}),t=n=0}),s({c_usage:1})}});function s(e){var t=i.extend({},n,e),o=r.replace(/^(.*)\?/,"$1"+i.param(t));(new Image).src=o}}),s("webuploader",["preset/all","widgets/log"],function(e){return e}),a("webuploader")));var n,s,a};"object"==typeof module&&"object"==typeof module.exports?module.exports=u():"function"==typeof define&&define.amd?define(["jquery"],u):(n=e.WebUploader,e.WebUploader=u(),e.WebUploader.noConflict=function(){e.WebUploader=n})}(window);