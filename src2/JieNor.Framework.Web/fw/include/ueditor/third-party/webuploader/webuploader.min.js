!function(a,b){var j,c={},d=function(a,b){var c,d,e;if("string"==typeof a)return g(a);for(c=[],d=a.length,e=0;d>e;e++)c.push(g(a[e]));return b.apply(null,c)},e=function(a,b,c){2===arguments.length&&(c=b,b=null),d(b||[],function(){f(a,c,arguments)})},f=function(a,b,e){var g,f={exports:b};"function"==typeof b&&(e.length||(e=[d,f.exports,f]),g=b.apply(null,e),void 0!==g&&(f.exports=g)),c[a]=f.exports},g=function(b){var d=c[b]||a[b];if(!d)throw new Error("`"+b+"` is undefined");return d},h=function(a){var b,d,e,f,g,h;h=function(a){return a&&a.charAt(0).toUpperCase()+a.substr(1)};for(b in c)if(d=a,c.hasOwnProperty(b)){for(e=b.split("/"),g=h(e.pop());f=h(e.shift());)d[f]=d[f]||{},d=d[f];d[g]=c[b]}return a},i=function(c){return a.__dollar=c,h(b(a,e,d))};"object"==typeof module&&"object"==typeof module.exports?module.exports=i():"function"==typeof define&&define.amd?define(["jquery"],i):(j=a.WebUploader,a.WebUploader=i(),a.WebUploader.noConflict=function(){a.WebUploader=j})}(window,function(a,b,c){return b("dollar-third",[],function(){var b=a.require,c=a.__dollar||a.jQuery||a.Zepto||b("jquery")||b("zepto");if(!c)throw new Error("jQuery or Zepto not found!");return c}),b("dollar",["dollar-third"],function(a){return a}),b("promise-third",["dollar"],function(a){return{Deferred:a.Deferred,when:a.when,isPromise:function(a){return a&&"function"==typeof a.then}}}),b("promise",["promise-third"],function(a){return a}),b("base",["dollar","promise"],function(b,c){function f(a){return function(){return e.apply(a,arguments)}}function g(a,b){return function(){return a.apply(b,arguments)}}function h(a){var b;return Object.create?Object.create(a):(b=function(){},b.prototype=a,new b)}var d=function(){},e=Function.call;return{version:"0.1.6",$:b,Deferred:c.Deferred,isPromise:c.isPromise,when:c.when,browser:function(a){var b={},c=a.match(/WebKit\/([\d.]+)/),d=a.match(/Chrome\/([\d.]+)/)||a.match(/CriOS\/([\d.]+)/),e=a.match(/MSIE\s([\d\.]+)/)||a.match(/(?:trident)(?:.*rv:([\w.]+))?/i),f=a.match(/Firefox\/([\d.]+)/),g=a.match(/Safari\/([\d.]+)/),h=a.match(/OPR\/([\d.]+)/);return c&&(b.webkit=parseFloat(c[1])),d&&(b.chrome=parseFloat(d[1])),e&&(b.ie=parseFloat(e[1])),f&&(b.firefox=parseFloat(f[1])),g&&(b.safari=parseFloat(g[1])),h&&(b.opera=parseFloat(h[1])),b}(navigator.userAgent),os:function(a){var b={},c=a.match(/(?:Android);?[\s\/]+([\d.]+)?/),d=a.match(/(?:iPad|iPod|iPhone).*OS\s([\d_]+)/);return c&&(b.android=parseFloat(c[1])),d&&(b.ios=parseFloat(d[1].replace(/_/g,"."))),b}(navigator.userAgent),inherits:function(a,c,d){var e;return"function"==typeof c?(e=c,c=null):e=c&&c.hasOwnProperty("constructor")?c.constructor:function(){return a.apply(this,arguments)},b.extend(!0,e,a,d||{}),e.__super__=a.prototype,e.prototype=h(a.prototype),c&&b.extend(!0,e.prototype,c),e},noop:d,bindFn:g,log:function(){return a.console?g(console.log,console):d}(),nextTick:function(){return function(a){setTimeout(a,1)}}(),slice:f([].slice),guid:function(){var a=0;return function(b){for(var c=(+new Date).toString(32),d=0;5>d;d++)c+=Math.floor(65535*Math.random()).toString(32);return(b||"wu_")+c+(a++).toString(32)}}(),formatSize:function(a,b,c){var d,e;for(c=c||["B","K","M","G","TB"];(d=c.shift())&&a>1024;)a/=1024;return e=0==b?a.toFixed():1==b?a.toFixed(1):a.toFixed(2),("B"===d?a:e)+d}}}),b("mediator",["base"],function(a){function f(a,c,d,e){return b.grep(a,function(a){return!(!a||c&&a.e!==c||d&&a.cb!==d&&a.cb._cb!==d||e&&a.ctx!==e)})}function g(a,c,e){b.each((a||"").split(d),function(a,b){e(b,c)})}function h(a,b){for(var f,c=!1,d=-1,e=a.length;++d<e;)if(f=a[d],f.cb.apply(f.ctx2,b)===!1){c=!0;break}return!c}var e,b=a.$,c=[].slice,d=/\s+/;return e={on:function(a,b,c){var e,d=this;return b?(e=this._events||(this._events=[]),g(a,b,function(a,b){var f={e:a};f.cb=b,f.ctx=c,f.ctx2=c||d,f.id=e.length,e.push(f)}),this):this},once:function(a,b,c){var d=this;return b?(g(a,b,function(a,b){var e=function(){return d.off(a,e),b.apply(c||d,arguments)};e._cb=b,d.on(a,e,c)}),d):d},off:function(a,c,d){var e=this._events;return e?a||c||d?(g(a,c,function(a,c){b.each(f(e,a,c,d),function(){delete e[this.id]})}),this):(this._events=[],this):this},trigger:function(a){var b,d,e;return this._events&&a?(b=c.call(arguments,1),d=f(this._events,a),e=f(this._events,"all"),h(d,b)&&h(e,arguments)):this}},b.extend({installTo:function(a){return b.extend(a,e)}},e)}),b("uploader",["base","mediator"],function(a,b){function d(a){this.options=c.extend(!0,{},d.options,a),this._init(this.options)}var c=a.$;return d.options={},b.installTo(d.prototype),c.each({upload:"start-upload",stop:"stop-upload",getFile:"get-file",getFiles:"get-files",addFile:"add-file",addFiles:"add-file",sort:"sort-files",removeFile:"remove-file",cancelFile:"cancel-file",skipFile:"skip-file",retry:"retry",isInProgress:"is-in-progress",makeThumb:"make-thumb",md5File:"md5-file",getDimension:"get-dimension",addButton:"add-btn",predictRuntimeType:"predict-runtime-type",refresh:"refresh",disable:"disable",enable:"enable",reset:"reset"},function(a,b){d.prototype[a]=function(){return this.request(b,arguments)}}),c.extend(d.prototype,{state:"pending",_init:function(a){var b=this;b.request("init",a,function(){b.state="ready",b.trigger("ready")})},option:function(a,b){var d=this.options;return arguments.length>1?(c.isPlainObject(b)&&c.isPlainObject(d[a])?c.extend(d[a],b):d[a]=b,void 0):a?d[a]:d},getStats:function(){var a=this.request("get-stats");return a?{successNum:a.numOfSuccess,progressNum:a.numOfProgress,cancelNum:a.numOfCancel,invalidNum:a.numOfInvalid,uploadFailNum:a.numOfUploadFailed,queueNum:a.numOfQueue,interruptNum:a.numofInterrupt}:{}},trigger:function(a){var d=[].slice.call(arguments,1),e=this.options,f="on"+a.substring(0,1).toUpperCase()+a.substring(1);return b.trigger.apply(this,arguments)===!1||c.isFunction(e[f])&&e[f].apply(this,d)===!1||c.isFunction(this[f])&&this[f].apply(this,d)===!1||b.trigger.apply(b,[this,a].concat(d))===!1?!1:!0},destroy:function(){this.request("destroy",arguments),this.off()},request:a.noop}),a.create=d.create=function(a){return new d(a)},a.Uploader=d,d}),b("runtime/runtime",["base","mediator"],function(a,b){function f(b){this.options=c.extend({container:document.body},b),this.uid=a.guid("rt_")}var c=a.$,d={},e=function(a){for(var b in a)if(a.hasOwnProperty(b))return b;return null};return c.extend(f.prototype,{getContainer:function(){var b,d,a=this.options;return this._container?this._container:(b=c(a.container||document.body),d=c(document.createElement("div")),d.attr("id","rt_"+this.uid),d.css({position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),b.append(d),b.addClass("webuploader-container"),this._container=d,this._parent=b,d)},init:a.noop,exec:a.noop,destroy:function(){this._container&&this._container.remove(),this._parent&&this._parent.removeClass("webuploader-container"),this.off()}}),f.orders="html5,flash",f.addRuntime=function(a,b){d[a]=b},f.hasRuntime=function(a){return!!(a?d[a]:e(d))},f.create=function(a,b){var g,h;if(b=b||f.orders,c.each(b.split(/\s*,\s*/g),function(){return d[this]?(g=this,!1):void 0}),g=g||e(d),!g)throw new Error("Runtime Error");return h=new d[g](a)},b.installTo(f.prototype),f}),b("runtime/client",["base","mediator","runtime/runtime"],function(a,b,c){function e(b,e){var g,f=a.Deferred();this.uid=a.guid("client_"),this.runtimeReady=function(a){return f.done(a)},this.connectRuntime=function(b,h){if(g)throw new Error("already connected!");return f.done(h),"string"==typeof b&&d.get(b)&&(g=d.get(b)),g=g||d.get(null,e),g?(a.$.extend(g.options,b),g.__promise.then(f.resolve),g.__client++):(g=c.create(b,b.runtimeOrder),g.__promise=f.promise(),g.once("ready",f.resolve),g.init(),d.add(g),g.__client=1),e&&(g.__standalone=e),g},this.getRuntime=function(){return g},this.disconnectRuntime=function(){g&&(g.__client--,g.__client<=0&&(d.remove(g),delete g.__promise,g.destroy()),g=null)},this.exec=function(){if(g){var c=a.slice(arguments);return b&&c.unshift(b),g.exec.apply(this,c)}},this.getRuid=function(){return g&&g.uid},this.destroy=function(a){return function(){a&&a.apply(this,arguments),this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()}}(this.destroy)}var d;return d=function(){var a={};return{add:function(b){a[b.uid]=b},get:function(b,c){var d;if(b)return a[b];for(d in a)if(!c||!a[d].__standalone)return a[d];return null},remove:function(b){delete a[b.uid]}}}(),b.installTo(e.prototype),e}),b("lib/dnd",["base","mediator","runtime/client"],function(a,b,c){function e(a){a=this.options=d.extend({},e.options,a),a.container=d(a.container),a.container.length&&c.call(this,"DragAndDrop")}var d=a.$;return e.options={accept:null,disableGlobalDnd:!1},a.inherits(c,{constructor:e,init:function(){var a=this;a.connectRuntime(a.options,function(){a.exec("init"),a.trigger("ready")})}}),b.installTo(e.prototype),e}),b("widgets/widget",["base","uploader"],function(a,b){function h(a){if(!a)return!1;var b=a.length,d=c.type(a);return 1===a.nodeType&&b?!0:"array"===d||"function"!==d&&"string"!==d&&(0===b||"number"==typeof b&&b>0&&b-1 in a)}function i(a){this.owner=a,this.options=a.options}var c=a.$,d=b.prototype._init,e=b.prototype.destroy,f={},g=[];return c.extend(i.prototype,{init:a.noop,invoke:function(a,b){var d=this.responseMap;return d&&a in d&&d[a]in this&&c.isFunction(this[d[a]])?this[d[a]].apply(this,b):f},request:function(){return this.owner.request.apply(this.owner,arguments)}}),c.extend(b.prototype,{_init:function(){var a=this,b=a._widgets=[],e=a.options.disableWidgets||"";return c.each(g,function(c,d){(!e||!~e.indexOf(d._name))&&b.push(new d(a))}),d.apply(a,arguments)},request:function(b,c,d){var l,m,n,o,e=0,g=this._widgets,i=g&&g.length,j=[],k=[];for(c=h(c)?c:[c];i>e;e++)l=g[e],m=l.invoke(b,c),m!==f&&(a.isPromise(m)?k.push(m):j.push(m));return d||k.length?(n=a.when.apply(a,k),o=n.pipe?"pipe":"then",n[o](function(){var b=a.Deferred(),c=arguments;return 1===c.length&&(c=c[0]),setTimeout(function(){b.resolve(c)},1),b.promise()})[d?o:"done"](d||a.noop)):j[0]},destroy:function(){e.apply(this,arguments),this._widgets=null}}),b.register=i.register=function(b,d){var f,e={init:"init",destroy:"destroy",name:"anonymous"};return 1===arguments.length?(d=b,c.each(d,function(a){return"_"===a[0]||"name"===a?("name"===a&&(e.name=d.name),void 0):(e[a.replace(/[A-Z]/g,"-$&").toLowerCase()]=a,void 0)})):e=c.extend(e,b),d.responseMap=e,f=a.inherits(i,d),f._name=e.name,g.push(f),f},b.unRegister=i.unRegister=function(a){if(a&&"anonymous"!==a)for(var b=g.length;b--;)g[b]._name===a&&g.splice(b,1)},i}),b("widgets/filednd",["base","uploader","lib/dnd","widgets/widget"],function(a,b,c){var d=a.$;return b.options.dnd="",b.register({name:"dnd",init:function(b){if(b.dnd&&"html5"===this.request("predict-runtime-type")){var h,e=this,f=a.Deferred(),g=d.extend({},{disableGlobalDnd:b.disableGlobalDnd,container:b.dnd,accept:b.accept});return this.dnd=h=new c(g),h.once("ready",f.resolve),h.on("drop",function(a){e.request("add-file",[a])}),h.on("accept",function(a){return e.owner.trigger("dndAccept",a)}),h.init(),f.promise()}},destroy:function(){this.dnd&&this.dnd.destroy()}})}),b("lib/filepaste",["base","mediator","runtime/client"],function(a,b,c){function e(a){a=this.options=d.extend({},a),a.container=d(a.container||document.body),c.call(this,"FilePaste")}var d=a.$;return a.inherits(c,{constructor:e,init:function(){var a=this;a.connectRuntime(a.options,function(){a.exec("init"),a.trigger("ready")})}}),b.installTo(e.prototype),e}),b("widgets/filepaste",["base","uploader","lib/filepaste","widgets/widget"],function(a,b,c){var d=a.$;return b.register({name:"paste",init:function(b){if(b.paste&&"html5"===this.request("predict-runtime-type")){var h,e=this,f=a.Deferred(),g=d.extend({},{container:b.paste,accept:b.accept});return this.paste=h=new c(g),h.once("ready",f.resolve),h.on("paste",function(a){e.owner.request("add-file",[a])}),h.init(),f.promise()}},destroy:function(){this.paste&&this.paste.destroy()}})}),b("lib/blob",["base","runtime/client"],function(a,b){function c(a,c){var d=this;d.source=c,d.ruid=a,this.size=c.size||0,this.type=!c.type&&this.ext&&~"jpg,jpeg,png,gif,bmp".indexOf(this.ext)?"image/"+("jpg"===this.ext?"jpeg":this.ext):c.type||"application/octet-stream",b.call(d,"Blob"),this.uid=c.uid||this.uid,a&&d.connectRuntime(a)}return a.inherits(b,{constructor:c,slice:function(a,b){return this.exec("slice",a,b)},getSource:function(){return this.source}}),c}),b("lib/file",["base","lib/blob"],function(a,b){function e(a,e){var f;this.name=e.name||"untitled"+c++,f=d.exec(e.name)?RegExp.$1.toLowerCase():"",!f&&e.type&&(f=/\/(jpg|jpeg|png|gif|bmp)$/i.exec(e.type)?RegExp.$1.toLowerCase():"",this.name+="."+f),this.ext=f,this.lastModifiedDate=e.lastModifiedDate||(new Date).toLocaleString(),b.apply(this,arguments)}var c=1,d=/\.([^.]+)$/;return a.inherits(b,e)}),b("lib/filepicker",["base","runtime/client","lib/file"],function(b,c,d){function f(a){if(a=this.options=e.extend({},f.options,a),a.container=e(a.id),!a.container.length)throw new Error("按钮指定错误");a.innerHTML=a.innerHTML||a.label||a.container.html()||"",a.button=e(a.button||document.createElement("div")),a.button.html(a.innerHTML),a.container.html(a.button),c.call(this,"FilePicker",!0)}var e=b.$;return f.options={button:null,container:null,label:null,innerHTML:null,multiple:!0,accept:null,name:"file",style:"webuploader-pick"},b.inherits(c,{constructor:f,init:function(){var c=this,f=c.options,g=f.button,h=f.style;h&&g.addClass("webuploader-pick"),c.on("all",function(a){var b;switch(a){case"mouseenter":h&&g.addClass("webuploader-pick-hover");break;case"mouseleave":h&&g.removeClass("webuploader-pick-hover");break;case"change":b=c.exec("getFiles"),c.trigger("select",e.map(b,function(a){return a=new d(c.getRuid(),a),a._refer=f.container,a}),f.container)}}),c.connectRuntime(f,function(){c.refresh(),c.exec("init",f),c.trigger("ready")}),this._resizeHandler=b.bindFn(this.refresh,this),e(a).on("resize",this._resizeHandler)},refresh:function(){var a=this.getRuntime().getContainer(),b=this.options.button,c=b.outerWidth?b.outerWidth():b.width(),d=b.outerHeight?b.outerHeight():b.height(),e=b.offset();c&&d&&a.css({bottom:"auto",right:"auto",width:c+"px",height:d+"px"}).offset(e)},enable:function(){var a=this.options.button;a.removeClass("webuploader-pick-disable"),this.refresh()},disable:function(){var a=this.options.button;this.getRuntime().getContainer().css({top:"-99999px"}),a.addClass("webuploader-pick-disable")},destroy:function(){var b=this.options.button;e(a).off("resize",this._resizeHandler),b.removeClass("webuploader-pick-disable webuploader-pick-hover webuploader-pick")}}),f}),b("widgets/filepicker",["base","uploader","lib/filepicker","widgets/widget"],function(a,b,c){var d=a.$;return d.extend(b.options,{pick:null,accept:null}),b.register({name:"picker",init:function(a){return this.pickers=[],a.pick&&this.addBtn(a.pick)},refresh:function(){d.each(this.pickers,function(){this.refresh()})},addBtn:function(b){var e=this,f=e.options,g=f.accept,h=[];if(b)return d.isPlainObject(b)||(b={id:b}),d(b.id).each(function(){var i,j,k;k=a.Deferred(),i=d.extend({},b,{accept:d.isPlainObject(g)?[g]:g,swf:f.swf,runtimeOrder:f.runtimeOrder,id:this}),j=new c(i),j.once("ready",k.resolve),j.on("select",function(a){e.owner.request("add-file",[a])}),j.on("dialogopen",function(){e.owner.trigger("dialogOpen",j.button)}),j.init(),e.pickers.push(j),h.push(k.promise())}),a.when.apply(a,h)},disable:function(){d.each(this.pickers,function(){this.disable()})},enable:function(){d.each(this.pickers,function(){this.enable()})},destroy:function(){d.each(this.pickers,function(){this.destroy()}),this.pickers=null}})}),b("lib/image",["base","runtime/client","lib/blob"],function(a,b,c){function e(a){this.options=d.extend({},e.options,a),b.call(this,"Image"),this.on("load",function(){this._info=this.exec("info"),this._meta=this.exec("meta")})}var d=a.$;return e.options={quality:90,crop:!1,preserveHeaders:!1,allowMagnify:!1},a.inherits(b,{constructor:e,info:function(a){return a?(this._info=a,this):this._info},meta:function(a){return a?(this._meta=a,this):this._meta},loadFromBlob:function(a){var b=this,c=a.getRuid();this.connectRuntime(c,function(){b.exec("init",b.options),b.exec("loadFromBlob",a)})},resize:function(){var b=a.slice(arguments);return this.exec.apply(this,["resize"].concat(b))},crop:function(){var b=a.slice(arguments);return this.exec.apply(this,["crop"].concat(b))},getAsDataUrl:function(a){return this.exec("getAsDataUrl",a)},getAsBlob:function(a){var b=this.exec("getAsBlob",a);return new c(this.getRuid(),b)}}),e}),b("widgets/image",["base","uploader","lib/image","widgets/widget"],function(a,b,c){var e,d=a.$;return e=function(a){var b=0,c=[],d=function(){for(var d;c.length&&a>b;)d=c.shift(),b+=d[0],d[1]()};return function(a,e,f){c.push([e,f]),a.once("destroy",function(){b-=e,setTimeout(d,1)}),setTimeout(d,1)}}(5242880),d.extend(b.options,{thumb:{width:110,height:110,quality:70,allowMagnify:!0,crop:!0,preserveHeaders:!1,type:"image/jpeg"},compress:{width:1600,height:1600,quality:90,allowMagnify:!1,crop:!1,preserveHeaders:!0}}),b.register({name:"image",makeThumb:function(a,b,f,g){var h,i;return a=this.request("get-file",a),a.type.match(/^image/)?(h=d.extend({},this.options.thumb),d.isPlainObject(f)&&(h=d.extend(h,f),f=null),f=f||h.width,g=g||h.height,i=new c(h),i.once("load",function(){a._info=a._info||i.info(),a._meta=a._meta||i.meta(),1>=f&&f>0&&(f=a._info.width*f),1>=g&&g>0&&(g=a._info.height*g),i.resize(f,g)}),i.once("complete",function(){b(!1,i.getAsDataUrl(h.type)),i.destroy()}),i.once("error",function(a){b(a||!0),i.destroy()}),e(i,a.source.size,function(){a._info&&i.info(a._info),a._meta&&i.meta(a._meta),i.loadFromBlob(a.source)}),void 0):(b(!0),void 0)},beforeSendFile:function(b){var h,i,e=this.options.compress||this.options.resize,f=e&&e.compressSize||0,g=e&&e.noCompressIfLarger||!1;return b=this.request("get-file",b),!e||!~"image/jpeg,image/jpg".indexOf(b.type)||b.size<f||b._compressed?void 0:(e=d.extend({},e),i=a.Deferred(),h=new c(e),i.always(function(){h.destroy(),h=null}),h.once("error",i.reject),h.once("load",function(){var a=e.width,c=e.height;b._info=b._info||h.info(),b._meta=b._meta||h.meta(),1>=a&&a>0&&(a=b._info.width*a),1>=c&&c>0&&(c=b._info.height*c),h.resize(a,c)}),h.once("complete",function(){var a,c;try{a=h.getAsBlob(e.type),c=b.size,(!g||a.size<c)&&(b.source=a,b.size=a.size,b.trigger("resize",a.size,c)),b._compressed=!0,i.resolve()}catch(d){i.resolve()}}),b._info&&h.info(b._info),b._meta&&h.meta(b._meta),h.loadFromBlob(b.source),i.promise())}})}),b("file",["base","mediator"],function(a,b){function h(){return d+e++}function i(a){this.name=a.name||"Untitled",this.size=a.size||0,this.type=a.type||"application/octet-stream",this.lastModifiedDate=a.lastModifiedDate||1*new Date,this.id=h(),this.ext=f.exec(this.name)?RegExp.$1:"",this.statusText="",g[this.id]=i.Status.INITED,this.source=a,this.loaded=0,this.on("error",function(a){this.setStatus(i.Status.ERROR,a)})}var c=a.$,d="WU_FILE_",e=0,f=/\.([^.]+)$/,g={};return c.extend(i.prototype,{setStatus:function(a,b){var c=g[this.id];"undefined"!=typeof b&&(this.statusText=b),a!==c&&(g[this.id]=a,this.trigger("statuschange",a,c))},getStatus:function(){return g[this.id]},getSource:function(){return this.source},destroy:function(){this.off(),delete g[this.id]}}),b.installTo(i.prototype),i.Status={INITED:"inited",QUEUED:"queued",PROGRESS:"progress",ERROR:"error",COMPLETE:"complete",CANCELLED:"cancelled",INTERRUPT:"interrupt",INVALID:"invalid"},i}),b("queue",["base","mediator","file"],function(a,b,c){function f(){this.stats={numOfQueue:0,numOfSuccess:0,numOfCancel:0,numOfProgress:0,numOfUploadFailed:0,numOfInvalid:0,numofDeleted:0,numofInterrupt:0},this._queue=[],this._map={}}var d=a.$,e=c.Status;return d.extend(f.prototype,{append:function(a){return this._queue.push(a),this._fileAdded(a),this},prepend:function(a){return this._queue.unshift(a),this._fileAdded(a),this},getFile:function(a){return"string"!=typeof a?a:this._map[a]},fetch:function(a){var c,d,b=this._queue.length;for(a=a||e.QUEUED,c=0;b>c;c++)if(d=this._queue[c],a===d.getStatus())return d;return null},sort:function(a){"function"==typeof a&&this._queue.sort(a)},getFiles:function(){for(var f,a=[].slice.call(arguments,0),b=[],c=0,e=this._queue.length;e>c;c++)f=this._queue[c],(!a.length||~d.inArray(f.getStatus(),a))&&b.push(f);return b},removeFile:function(a){var c=this._map[a.id];c&&(delete this._map[a.id],a.destroy(),this.stats.numofDeleted++)},_fileAdded:function(a){var b=this,c=this._map[a.id];c||(this._map[a.id]=a,a.on("statuschange",function(a,c){b._onFileStatusChange(a,c)}))},_onFileStatusChange:function(a,b){var c=this.stats;switch(b){case e.PROGRESS:c.numOfProgress--;break;case e.QUEUED:c.numOfQueue--;break;case e.ERROR:c.numOfUploadFailed--;break;case e.INVALID:c.numOfInvalid--;break;case e.INTERRUPT:c.numofInterrupt--}switch(a){case e.QUEUED:c.numOfQueue++;break;case e.PROGRESS:c.numOfProgress++;break;case e.ERROR:c.numOfUploadFailed++;break;case e.COMPLETE:c.numOfSuccess++;break;case e.CANCELLED:c.numOfCancel++;break;case e.INVALID:c.numOfInvalid++;break;case e.INTERRUPT:c.numofInterrupt++}}}),b.installTo(f.prototype),f}),b("widgets/queue",["base","uploader","queue","file","lib/file","runtime/client","widgets/widget"],function(a,b,c,d,e,f){var g=a.$,h=/\.\w+$/,i=d.Status;return b.register({name:"queue",init:function(b){var e,h,i,j,k,l,m,d=this;if(g.isPlainObject(b.accept)&&(b.accept=[b.accept]),b.accept){for(k=[],i=0,h=b.accept.length;h>i;i++)j=b.accept[i].extensions,j&&k.push(j);k.length&&(l="\\."+k.join(",").replace(/,/g,"$|\\.").replace(/\*/g,".*")+"$"),d.accept=new RegExp(l,"i")}return d.queue=new c,d.stats=d.queue.stats,"html5"===this.request("predict-runtime-type")?(e=a.Deferred(),this.placeholder=m=new f("Placeholder"),m.connectRuntime({runtimeOrder:"html5"},function(){d._ruid=m.getRuid(),e.resolve()}),e.promise()):void 0},_wrapFile:function(a){if(!(a instanceof d)){if(!(a instanceof e)){if(!this._ruid)throw new Error("Can't add external files.");a=new e(this._ruid,a)}a=new d(a)}return a},acceptFile:function(a){var b=!a||!a.size||this.accept&&h.exec(a.name)&&!this.accept.test(a.name);return!b},_addFile:function(a){var b=this;return a=b._wrapFile(a),b.owner.trigger("beforeFileQueued",a)?b.acceptFile(a)?(b.queue.append(a),b.owner.trigger("fileQueued",a),a):(b.owner.trigger("error","Q_TYPE_DENIED",a),void 0):void 0},getFile:function(a){return this.queue.getFile(a)},addFile:function(a){var b=this;a.length||(a=[a]),a=g.map(a,function(a){return b._addFile(a)}),a.length&&(b.owner.trigger("filesQueued",a),b.options.auto&&setTimeout(function(){b.request("start-upload")},20))},getStats:function(){return this.stats},removeFile:function(a,b){var c=this;a=a.id?a:c.queue.getFile(a),this.request("cancel-file",a),b&&this.queue.removeFile(a)},getFiles:function(){return this.queue.getFiles.apply(this.queue,arguments)},fetchFile:function(){return this.queue.fetch.apply(this.queue,arguments)},retry:function(a,b){var d,e,f,c=this;if(a)return a=a.id?a:c.queue.getFile(a),a.setStatus(i.QUEUED),b||c.request("start-upload"),void 0;for(d=c.queue.getFiles(i.ERROR),e=0,f=d.length;f>e;e++)a=d[e],a.setStatus(i.QUEUED);c.request("start-upload")},sortFiles:function(){return this.queue.sort.apply(this.queue,arguments)},reset:function(){this.owner.trigger("reset"),this.queue=new c,this.stats=this.queue.stats},destroy:function(){this.reset(),this.placeholder&&this.placeholder.destroy()}})}),b("widgets/runtime",["uploader","runtime/runtime","widgets/widget"],function(a,b){return a.support=function(){return b.hasRuntime.apply(b,arguments)},a.register({name:"runtime",init:function(){if(!this.predictRuntimeType())throw Error("Runtime Error")},predictRuntimeType:function(){var d,e,a=this.options.runtimeOrder||b.orders,c=this.type;if(!c)for(a=a.split(/\s*,\s*/g),d=0,e=a.length;e>d;d++)if(b.hasRuntime(a[d])){this.type=c=a[d];break}return c}})}),b("lib/transport",["base","runtime/client","mediator"],function(a,b,c){function e(a){var c=this;a=c.options=d.extend(!0,{},e.options,a||{}),b.call(this,"Transport"),this._blob=null,this._formData=a.formData||{},this._headers=a.headers||{},this.on("progress",this._timeout),this.on("load error",function(){c.trigger("progress",1),clearTimeout(c._timer)})}var d=a.$;return e.options={server:"",method:"POST",withCredentials:!1,fileVal:"file",timeout:12e4,formData:{},headers:{},sendAsBinary:!1},d.extend(e.prototype,{appendBlob:function(a,b,c){var d=this,e=d.options;d.getRuid()&&d.disconnectRuntime(),d.connectRuntime(b.ruid,function(){d.exec("init")}),d._blob=b,e.fileVal=a||e.fileVal,e.filename=c||e.filename},append:function(a,b){"object"==typeof a?d.extend(this._formData,a):this._formData[a]=b},setRequestHeader:function(a,b){"object"==typeof a?d.extend(this._headers,a):this._headers[a]=b},send:function(a){this.exec("send",a),this._timeout()},abort:function(){return clearTimeout(this._timer),this.exec("abort")},destroy:function(){this.trigger("destroy"),this.off(),this.exec("destroy"),this.disconnectRuntime()},getResponse:function(){return this.exec("getResponse")},getResponseAsJson:function(){return this.exec("getResponseAsJson")},getStatus:function(){return this.exec("getStatus")},_timeout:function(){var a=this,b=a.options.timeout;b&&(clearTimeout(a._timer),a._timer=setTimeout(function(){a.abort(),a.trigger("error","timeout")},b))}}),c.installTo(e.prototype),e}),b("widgets/upload",["base","uploader","file","lib/transport","widgets/widget"],function(a,b,c,d){function h(a,b){var i,j,c=[],d=a.source,e=d.size,f=b?Math.ceil(e/b):1,g=0,h=0;for(j={file:a,has:function(){return!!c.length},shift:function(){return c.shift()},unshift:function(a){c.unshift(a)}};f>h;)i=Math.min(b,e-g),c.push({file:a,start:g,end:b?g+i:e,total:e,chunks:f,chunk:h++,cuted:j}),g+=i;return a.blocks=c.concat(),a.remaning=c.length,j}var e=a.$,f=a.isPromise,g=c.Status;e.extend(b.options,{prepareNextFile:!1,chunked:!1,chunkSize:5242880,chunkRetry:2,threads:3,formData:{}}),b.register({name:"upload",init:function(){var b=this.owner,c=this;this.runing=!1,this.progress=!1,b.on("startUpload",function(){c.progress=!0}).on("uploadFinished",function(){c.progress=!1}),this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this.__tick=a.bindFn(this._tick,this),b.on("uploadComplete",function(a){a.blocks&&e.each(a.blocks,function(a,b){b.transport&&(b.transport.abort(),b.transport.destroy()),delete b.transport}),delete a.blocks,delete a.remaning})},reset:function(){this.request("stop-upload",!0),this.runing=!1,this.pool=[],this.stack=[],this.pending=[],this.remaning=0,this._trigged=!1,this._promise=null},startUpload:function(b){var d,c=this;return e.each(c.request("get-files",g.INVALID),function(){c.request("remove-file",this)}),b?(b=b.id?b:c.request("get-file",b),b.getStatus()===g.INTERRUPT?(b.setStatus(g.QUEUED),e.each(c.pool,function(a,c){c.file===b&&(c.transport&&c.transport.send(),b.setStatus(g.PROGRESS))})):b.getStatus()!==g.PROGRESS&&b.setStatus(g.QUEUED)):e.each(c.request("get-files",[g.INITED]),function(){this.setStatus(g.QUEUED)}),c.runing?a.nextTick(c.__tick):(c.runing=!0,d=[],b||e.each(c.pool,function(a,b){var e=b.file;e.getStatus()===g.INTERRUPT&&(c._trigged=!1,d.push(e),b.transport&&b.transport.send())}),e.each(d,function(){this.setStatus(g.PROGRESS)}),b||e.each(c.request("get-files",g.INTERRUPT),function(){this.setStatus(g.PROGRESS)}),c._trigged=!1,a.nextTick(c.__tick),c.owner.trigger("startUpload"),void 0)},stopUpload:function(b,c){var f,d=this;if(b===!0&&(c=b,b=null),d.runing!==!1){if(b){if(b=b.id?b:d.request("get-file",b),b.getStatus()!==g.PROGRESS&&b.getStatus()!==g.QUEUED)return;return b.setStatus(g.INTERRUPT),e.each(d.pool,function(a,c){return c.file===b?(f=c,!1):void 0}),f.transport&&f.transport.abort(),c&&(d._putback(f),d._popBlock(f)),a.nextTick(d.__tick)}d.runing=!1,this._promise&&this._promise.file&&this._promise.file.setStatus(g.INTERRUPT),c&&e.each(d.pool,function(a,b){b.transport&&b.transport.abort(),b.file.setStatus(g.INTERRUPT)}),d.owner.trigger("stopUpload")}},cancelFile:function(a){a=a.id?a:this.request("get-file",a),a.blocks&&e.each(a.blocks,function(a,b){var c=b.transport;c&&(c.abort(),c.destroy(),delete b.transport)}),a.setStatus(g.CANCELLED),this.owner.trigger("fileDequeued",a)},isInProgress:function(){return!!this.progress},_getStats:function(){return this.request("get-stats")},skipFile:function(a,b){a=a.id?a:this.request("get-file",a),a.setStatus(b||g.COMPLETE),a.skipped=!0,a.blocks&&e.each(a.blocks,function(a,b){var c=b.transport;c&&(c.abort(),c.destroy(),delete b.transport)}),this.owner.trigger("uploadSkip",a)},_tick:function(){var d,e,b=this,c=b.options;return b._promise?b._promise.always(b.__tick):(b.pool.length<c.threads&&(e=b._nextBlock())?(b._trigged=!1,d=function(c){b._promise=null,c&&c.file&&b._startSend(c),a.nextTick(b.__tick)},b._promise=f(e)?e.always(d):d(e)):b.remaning||b._getStats().numOfQueue||b._getStats().numofInterrupt||(b.runing=!1,b._trigged||a.nextTick(function(){b.owner.trigger("uploadFinished")}),b._trigged=!0),void 0)},_putback:function(a){var b;a.cuted.unshift(a),b=this.stack.indexOf(a.cuted),~b||this.stack.unshift(a.cuted)},_getStack:function(){for(var b,a=0;b=this.stack[a++];){if(b.has()&&b.file.getStatus()===g.PROGRESS)return b;(!b.has()||b.file.getStatus()!==g.PROGRESS&&b.file.getStatus()!==g.INTERRUPT)&&this.stack.splice(--a,1)}return null},_nextBlock:function(){var c,d,e,g,a=this,b=a.options;return(c=this._getStack())?(b.prepareNextFile&&!a.pending.length&&a._prepareNextFile(),c.shift()):a.runing?(!a.pending.length&&a._getStats().numOfQueue&&a._prepareNextFile(),d=a.pending.shift(),e=function(d){return d?(c=h(d,b.chunked?b.chunkSize:0),a.stack.push(c),c.shift()):null},f(d)?(g=d.file,d=d[d.pipe?"pipe":"then"](e),d.file=g,d):e(d)):void 0},_prepareNextFile:function(){var d,a=this,b=a.request("fetch-file"),c=a.pending;b&&(d=a.request("before-send-file",b,function(){return b.getStatus()===g.PROGRESS||b.getStatus()===g.INTERRUPT?b:a._finishFile(b)}),a.owner.trigger("uploadStart",b),b.setStatus(g.PROGRESS),d.file=b,d.done(function(){var a=e.inArray(d,c);~a&&c.splice(a,1,b)}),d.fail(function(c){b.setStatus(g.ERROR,c),a.owner.trigger("uploadError",b,c),a.owner.trigger("uploadComplete",b)}),c.push(d))},_popBlock:function(a){var b=e.inArray(a,this.pool);this.pool.splice(b,1),a.file.remaning--,this.remaning--},_startSend:function(b){var e,c=this,d=b.file;return d.getStatus()!==g.PROGRESS?(d.getStatus()===g.INTERRUPT&&c._putback(b),void 0):(c.pool.push(b),c.remaning++,b.blob=1===b.chunks?d.source:d.source.slice(b.start,b.end),e=c.request("before-send",b,function(){d.getStatus()===g.PROGRESS?c._doSend(b):(c._popBlock(b),a.nextTick(c.__tick))}),e.fail(function(){1===d.remaning?c._finishFile(d).always(function(){b.percentage=1,c._popBlock(b),c.owner.trigger("uploadComplete",d),a.nextTick(c.__tick)}):(b.percentage=1,c.updateFileProgress(d),c._popBlock(b),a.nextTick(c.__tick))}),void 0)},_doSend:function(b){var m,n,c=this,f=c.owner,h=c.options,i=b.file,j=new d(h),k=e.extend({},h.formData),l=e.extend({},h.headers);b.transport=j,j.on("destroy",function(){delete b.transport,c._popBlock(b),a.nextTick(c.__tick)}),j.on("progress",function(a){b.percentage=a,c.updateFileProgress(i)}),m=function(a){var c;return n=j.getResponseAsJson()||{},n._raw=j.getResponse(),c=function(b){a=b},f.trigger("uploadAccept",b,n,c)||(a=a||"server"),a},j.on("error",function(a,c){b.retried=b.retried||0,b.chunks>1&&~"http,abort".indexOf(a)&&b.retried<h.chunkRetry?(b.retried++,j.send()):(c||"server"!==a||(a=m(a)),i.setStatus(g.ERROR,a),f.trigger("uploadError",i,a),f.trigger("uploadComplete",i))}),j.on("load",function(){var a;return(a=m())?(j.trigger("error",a,!0),void 0):(1===i.remaning?c._finishFile(i,n):j.destroy(),void 0)}),k=e.extend(k,{id:i.id,name:i.name,type:i.type,lastModifiedDate:i.lastModifiedDate,size:i.size}),b.chunks>1&&e.extend(k,{chunks:b.chunks,chunk:b.chunk}),f.trigger("uploadBeforeSend",b,k,l),j.appendBlob(h.fileVal,b.blob,i.name),j.append(k),j.setRequestHeader(l),j.send()
},_finishFile:function(a,b,c){var d=this.owner;return d.request("after-send-file",arguments,function(){a.setStatus(g.COMPLETE),d.trigger("uploadSuccess",a,b,c)}).fail(function(b){a.getStatus()===g.PROGRESS&&a.setStatus(g.ERROR,b),d.trigger("uploadError",a,b)}).always(function(){d.trigger("uploadComplete",a)})},updateFileProgress:function(a){var b=0,c=0;a.blocks&&(e.each(a.blocks,function(a,b){c+=(b.percentage||0)*(b.end-b.start)}),b=c/a.size,this.owner.trigger("uploadProgress",a,b||0))}})}),b("widgets/validator",["base","uploader","file","widgets/widget"],function(a,b,c){var f,d=a.$,e={};return f={addValidator:function(a,b){e[a]=b},removeValidator:function(a){delete e[a]}},b.register({name:"validator",init:function(){var b=this;a.nextTick(function(){d.each(e,function(){this.call(b.owner)})})}}),f.addValidator("fileNumLimit",function(){var a=this,b=a.options,c=0,d=parseInt(b.fileNumLimit,10),e=!0;d&&(a.on("beforeFileQueued",function(a){return c>=d&&e&&(e=!1,this.trigger("error","Q_EXCEED_NUM_LIMIT",d,a),setTimeout(function(){e=!0},1)),c>=d?!1:!0}),a.on("fileQueued",function(){c++}),a.on("fileDequeued",function(){c--}),a.on("reset",function(){c=0}))}),f.addValidator("fileSizeLimit",function(){var a=this,b=a.options,c=0,d=parseInt(b.fileSizeLimit,10),e=!0;d&&(a.on("beforeFileQueued",function(a){var b=c+a.size>d;return b&&e&&(e=!1,this.trigger("error","Q_EXCEED_SIZE_LIMIT",d,a),setTimeout(function(){e=!0},1)),b?!1:!0}),a.on("fileQueued",function(a){c+=a.size}),a.on("fileDequeued",function(a){c-=a.size}),a.on("reset",function(){c=0}))}),f.addValidator("fileSingleSizeLimit",function(){var a=this,b=a.options,d=b.fileSingleSizeLimit;d&&a.on("beforeFileQueued",function(a){return a.size>d?(a.setStatus(c.Status.INVALID,"exceed_size"),this.trigger("error","F_EXCEED_SIZE",d,a),!1):void 0})}),f.addValidator("duplicate",function(){function d(a){for(var e,b=0,c=0,d=a.length;d>c;c++)e=a.charCodeAt(c),b=e+(b<<6)+(b<<16)-b;return b}var a=this,b=a.options,c={};b.duplicate||(a.on("beforeFileQueued",function(a){var b=a.__hash||(a.__hash=d(a.name+a.size+a.lastModifiedDate));return c[b]?(this.trigger("error","F_DUPLICATE",a),!1):void 0}),a.on("fileQueued",function(a){var b=a.__hash;b&&(c[b]=!0)}),a.on("fileDequeued",function(a){var b=a.__hash;b&&delete c[b]}),a.on("reset",function(){c={}}))}),f}),b("lib/md5",["runtime/client","mediator"],function(a,b){function c(){a.call(this,"Md5")}return b.installTo(c.prototype),c.prototype.loadFromBlob=function(a){var b=this;b.getRuid()&&b.disconnectRuntime(),b.connectRuntime(a.ruid,function(){b.exec("init"),b.exec("loadFromBlob",a)})},c.prototype.getResult=function(){return this.exec("getResult")},c}),b("widgets/md5",["base","uploader","lib/md5","lib/blob","widgets/widget"],function(a,b,c,d){return b.register({name:"md5",md5File:function(b,e,f){var g=new c,h=a.Deferred(),i=b instanceof d?b:this.request("get-file",b).source;return g.on("progress load",function(a){a=a||{},h.notify(a.total?a.loaded/a.total:1)}),g.on("complete",function(){h.resolve(g.getResult())}),g.on("error",function(a){h.reject(a)}),arguments.length>1&&(e=e||0,f=f||0,0>e&&(e=i.size+e),0>f&&(f=i.size+f),f=Math.min(f,i.size),i=i.slice(e,f)),g.loadFromBlob(i),h.promise()}})}),b("runtime/compbase",[],function(){function a(a,b){this.owner=a,this.options=a.options,this.getRuntime=function(){return b},this.getRuid=function(){return b.uid},this.trigger=function(){return a.trigger.apply(a,arguments)}}return a}),b("runtime/html5/runtime",["base","runtime/runtime","runtime/compbase"],function(b,c,d){function g(){var a={},d=this,g=this.destroy;c.apply(d,arguments),d.type=e,d.exec=function(c,e){var j,g=this,h=g.uid,i=b.slice(arguments,2);return f[c]&&(j=a[h]=a[h]||new f[c](g,d),j[e])?j[e].apply(j,i):void 0},d.destroy=function(){return g&&g.apply(this,arguments)}}var e="html5",f={};return b.inherits(c,{constructor:g,init:function(){var a=this;setTimeout(function(){a.trigger("ready")},1)}}),g.register=function(a,c){var e=f[a]=b.inherits(d,c);return e},a.Blob&&a.FileReader&&a.DataView&&c.addRuntime(e,g),g}),b("runtime/html5/blob",["runtime/html5/runtime","lib/blob"],function(a,b){return a.register("Blob",{slice:function(a,c){var d=this.owner.source,e=d.slice||d.webkitSlice||d.mozSlice;return d=e.call(d,a,c),new b(this.getRuid(),d)}})}),b("runtime/html5/dnd",["base","runtime/html5/runtime","lib/file"],function(a,b,c){var d=a.$,e="webuploader-dnd-";return b.register("DragAndDrop",{init:function(){var b=this.elem=this.options.container;this.dragEnterHandler=a.bindFn(this._dragEnterHandler,this),this.dragOverHandler=a.bindFn(this._dragOverHandler,this),this.dragLeaveHandler=a.bindFn(this._dragLeaveHandler,this),this.dropHandler=a.bindFn(this._dropHandler,this),this.dndOver=!1,b.on("dragenter",this.dragEnterHandler),b.on("dragover",this.dragOverHandler),b.on("dragleave",this.dragLeaveHandler),b.on("drop",this.dropHandler),this.options.disableGlobalDnd&&(d(document).on("dragover",this.dragOverHandler),d(document).on("drop",this.dropHandler))},_dragEnterHandler:function(a){var d,b=this,c=b._denied||!1;return a=a.originalEvent||a,b.dndOver||(b.dndOver=!0,d=a.dataTransfer.items,d&&d.length&&(b._denied=c=!b.trigger("accept",d)),b.elem.addClass(e+"over"),b.elem[c?"addClass":"removeClass"](e+"denied")),a.dataTransfer.dropEffect=c?"none":"copy",!1},_dragOverHandler:function(a){var b=this.elem.parent().get(0);return b&&!d.contains(b,a.currentTarget)?!1:(clearTimeout(this._leaveTimer),this._dragEnterHandler.call(this,a),!1)},_dragLeaveHandler:function(){var b,a=this;return b=function(){a.dndOver=!1,a.elem.removeClass(e+"over "+e+"denied")},clearTimeout(a._leaveTimer),a._leaveTimer=setTimeout(b,100),!1},_dropHandler:function(a){var h,i,b=this,f=b.getRuid(),g=b.elem.parent().get(0);if(g&&!d.contains(g,a.currentTarget))return!1;a=a.originalEvent||a,h=a.dataTransfer;try{i=h.getData("text/html")}catch(j){}return b.dndOver=!1,b.elem.removeClass(e+"over"),i?void 0:(b._getTansferFiles(h,function(a){b.trigger("drop",d.map(a,function(a){return new c(f,a)}))}),!1)},_getTansferFiles:function(b,c){var f,g,h,i,j,k,l,d=[],e=[];for(f=b.items,g=b.files,l=!(!f||!f[0].webkitGetAsEntry),j=0,k=g.length;k>j;j++)h=g[j],i=f&&f[j],l&&i.webkitGetAsEntry().isDirectory?e.push(this._traverseDirectoryTree(i.webkitGetAsEntry(),d)):d.push(h);a.when.apply(a,e).done(function(){d.length&&c(d)})},_traverseDirectoryTree:function(b,c){var d=a.Deferred(),e=this;return b.isFile?b.file(function(a){c.push(a),d.resolve()}):b.isDirectory&&b.createReader().readEntries(function(b){var i,f=b.length,g=[],h=[];for(i=0;f>i;i++)g.push(e._traverseDirectoryTree(b[i],h));a.when.apply(a,g).then(function(){c.push.apply(c,h),d.resolve()},d.reject)}),d.promise()},destroy:function(){var a=this.elem;a&&(a.off("dragenter",this.dragEnterHandler),a.off("dragover",this.dragOverHandler),a.off("dragleave",this.dragLeaveHandler),a.off("drop",this.dropHandler),this.options.disableGlobalDnd&&(d(document).off("dragover",this.dragOverHandler),d(document).off("drop",this.dropHandler)))}})}),b("runtime/html5/filepaste",["base","runtime/html5/runtime","lib/file"],function(a,b,c){return b.register("FilePaste",{init:function(){var e,f,g,h,b=this.options,c=this.elem=b.container,d=".*";if(b.accept){for(e=[],f=0,g=b.accept.length;g>f;f++)h=b.accept[f].mimeTypes,h&&e.push(h);e.length&&(d=e.join(","),d=d.replace(/,/g,"|").replace(/\*/g,".*"))}this.accept=d=new RegExp(d,"i"),this.hander=a.bindFn(this._pasteHander,this),c.on("paste",this.hander)},_pasteHander:function(a){var e,f,g,h,i,b=[],d=this.getRuid();for(a=a.originalEvent||a,e=a.clipboardData.items,h=0,i=e.length;i>h;h++)f=e[h],"file"===f.kind&&(g=f.getAsFile())&&b.push(new c(d,g));b.length&&(a.preventDefault(),a.stopPropagation(),this.trigger("paste",b))},destroy:function(){this.elem.off("paste",this.hander)}})}),b("runtime/html5/filepicker",["base","runtime/html5/runtime"],function(a,b){var c=a.$;return b.register("FilePicker",{init:function(){var h,i,j,k,a=this.getRuntime().getContainer(),b=this,d=b.owner,e=b.options,f=this.label=c(document.createElement("label")),g=this.input=c(document.createElement("input"));if(g.attr("type","file"),g.attr("capture","camera"),g.attr("name",e.name),g.addClass("webuploader-element-invisible"),f.on("click",function(a){g.trigger("click"),a.stopPropagation(),d.trigger("dialogopen")}),f.css({opacity:0,width:"100%",height:"100%",display:"block",cursor:"pointer",background:"#ffffff"}),e.multiple&&g.attr("multiple","multiple"),e.accept&&e.accept.length>0){for(h=[],i=0,j=e.accept.length;j>i;i++)h.push(e.accept[i].mimeTypes);g.attr("accept",h.join(","))}a.append(g),a.append(f),k=function(a){d.trigger(a.type)},g.on("change",function(a){var f,e=arguments.callee;b.files=a.target.files,f=this.cloneNode(!0),f.value=null,this.parentNode.replaceChild(f,this),g.off(),g=c(f).on("change",e).on("mouseenter mouseleave",k),d.trigger("change")}),f.on("mouseenter mouseleave",k)},getFiles:function(){return this.files},destroy:function(){this.input.off(),this.label.off()}})}),b("runtime/html5/util",["base"],function(b){var c=a.createObjectURL&&a||a.URL&&URL.revokeObjectURL&&URL||a.webkitURL,d=b.noop,e=d;return c&&(d=function(){return c.createObjectURL.apply(c,arguments)},e=function(){return c.revokeObjectURL.apply(c,arguments)}),{createObjectURL:d,revokeObjectURL:e,dataURL2Blob:function(a){var b,c,d,e,f,g;for(g=a.split(","),b=~g[0].indexOf("base64")?atob(g[1]):decodeURIComponent(g[1]),d=new ArrayBuffer(b.length),c=new Uint8Array(d),e=0;e<b.length;e++)c[e]=b.charCodeAt(e);return f=g[0].split(":")[1].split(";")[0],this.arrayBufferToBlob(d,f)},dataURL2ArrayBuffer:function(a){var b,c,d,e;for(e=a.split(","),b=~e[0].indexOf("base64")?atob(e[1]):decodeURIComponent(e[1]),c=new Uint8Array(b.length),d=0;d<b.length;d++)c[d]=b.charCodeAt(d);return c.buffer},arrayBufferToBlob:function(b,c){var e,d=a.BlobBuilder||a.WebKitBlobBuilder;return d?(e=new d,e.append(b),e.getBlob(c)):new Blob([b],c?{type:c}:{})},canvasToDataUrl:function(a,b,c){return a.toDataURL(b,c/100)},parseMeta:function(a,b){b(!1,{})},updateImageHead:function(a){return a}}}),b("runtime/html5/imagemeta",["runtime/html5/util"],function(a){var b;return b={parsers:{65505:[]},maxMetaDataSize:262144,parse:function(a,b){var c=this,d=new FileReader;d.onload=function(){b(!1,c._parse(this.result)),d=d.onload=d.onerror=null},d.onerror=function(a){b(a.message),d=d.onload=d.onerror=null},a=a.slice(0,c.maxMetaDataSize),d.readAsArrayBuffer(a.getSource())},_parse:function(a,c){if(!(a.byteLength<6)){var i,j,k,l,d=new DataView(a),e=2,f=d.byteLength-4,g=e,h={};if(65496===d.getUint16(0)){for(;f>e&&(i=d.getUint16(e),i>=65504&&65519>=i||65534===i)&&(j=d.getUint16(e+2)+2,!(e+j>d.byteLength));){if(k=b.parsers[i],!c&&k)for(l=0;l<k.length;l+=1)k[l].call(b,d,e,j,h);e+=j,g=e}g>6&&(h.imageHead=a.slice?a.slice(2,g):new Uint8Array(a).subarray(2,g))}return h}},updateImageHead:function(a,b){var d,e,f,c=this._parse(a,!0);return f=2,c.imageHead&&(f=2+c.imageHead.byteLength),e=a.slice?a.slice(f):new Uint8Array(a).subarray(f),d=new Uint8Array(b.byteLength+2+e.byteLength),d[0]=255,d[1]=216,d.set(new Uint8Array(b),2),d.set(new Uint8Array(e),b.byteLength+2),d.buffer}},a.parseMeta=function(){return b.parse.apply(b,arguments)},a.updateImageHead=function(){return b.updateImageHead.apply(b,arguments)},b}),b("runtime/html5/imagemeta/exif",["base","runtime/html5/imagemeta"],function(a,b){var c={};return c.ExifMap=function(){return this},c.ExifMap.prototype.map={Orientation:274},c.ExifMap.prototype.get=function(a){return this[a]||this[this.map[a]]},c.exifTagTypes={1:{getValue:function(a,b){return a.getUint8(b)},size:1},2:{getValue:function(a,b){return String.fromCharCode(a.getUint8(b))},size:1,ascii:!0},3:{getValue:function(a,b,c){return a.getUint16(b,c)},size:2},4:{getValue:function(a,b,c){return a.getUint32(b,c)},size:4},5:{getValue:function(a,b,c){return a.getUint32(b,c)/a.getUint32(b+4,c)},size:8},9:{getValue:function(a,b,c){return a.getInt32(b,c)},size:4},10:{getValue:function(a,b,c){return a.getInt32(b,c)/a.getInt32(b+4,c)},size:8}},c.exifTagTypes[7]=c.exifTagTypes[1],c.getExifValue=function(b,d,e,f,g,h){var j,k,l,m,n,o,i=c.exifTagTypes[f];if(!i)return a.log("Invalid Exif data: Invalid tag type."),void 0;if(j=i.size*g,k=j>4?d+b.getUint32(e+8,h):e+8,k+j>b.byteLength)return a.log("Invalid Exif data: Invalid data offset."),void 0;if(1===g)return i.getValue(b,k,h);for(l=[],m=0;g>m;m+=1)l[m]=i.getValue(b,k+m*i.size,h);if(i.ascii){for(n="",m=0;m<l.length&&(o=l[m],"\0"!==o);m+=1)n+=o;return n}return l},c.parseExifTag=function(a,b,d,e,f){var g=a.getUint16(d,e);f.exif[g]=c.getExifValue(a,b,d,a.getUint16(d+2,e),a.getUint32(d+4,e),e)},c.parseExifTags=function(b,c,d,e,f){var g,h,i;if(d+6>b.byteLength)return a.log("Invalid Exif data: Invalid directory offset."),void 0;if(g=b.getUint16(d,e),h=d+2+12*g,h+4>b.byteLength)return a.log("Invalid Exif data: Invalid directory size."),void 0;for(i=0;g>i;i+=1)this.parseExifTag(b,c,d+2+12*i,e,f);return b.getUint32(h,e)},c.parseExifData=function(b,d,e,f){var h,i,g=d+10;if(1165519206===b.getUint32(d+4)){if(g+8>b.byteLength)return a.log("Invalid Exif data: Invalid segment size."),void 0;if(0!==b.getUint16(d+8))return a.log("Invalid Exif data: Missing byte alignment offset."),void 0;switch(b.getUint16(g)){case 18761:h=!0;break;case 19789:h=!1;break;default:return a.log("Invalid Exif data: Invalid byte alignment marker."),void 0}if(42!==b.getUint16(g+2,h))return a.log("Invalid Exif data: Missing TIFF marker."),void 0;i=b.getUint32(g+4,h),f.exif=new c.ExifMap,i=c.parseExifTags(b,g,g+i,h,f)}},b.parsers[65505].push(c.parseExifData),c}),b("runtime/html5/jpegencoder",[],function(){function d(a){function I(a){var c,i,j,k,l,m,n,o,p,b=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99];for(c=0;64>c;c++)i=d((b[c]*a+50)/100),1>i?i=1:i>255&&(i=255),e[z[c]]=i;for(j=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],k=0;64>k;k++)l=d((j[k]*a+50)/100),1>l?l=1:l>255&&(l=255),f[z[k]]=l;for(m=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],n=0,o=0;8>o;o++)for(p=0;8>p;p++)g[n]=1/(8*e[z[n]]*m[o]*m[p]),h[n]=1/(8*f[z[n]]*m[o]*m[p]),n++}function J(a,b){var f,g,c=0,d=0,e=new Array;for(f=1;16>=f;f++){for(g=1;g<=a[f];g++)e[b[d]]=[],e[b[d]][0]=c,e[b[d]][1]=f,d++,c++;c*=2}return e}function K(){i=J(A,B),j=J(E,F),k=J(C,D),l=J(G,H)}function L(){var c,d,e,a=1,b=2;for(c=1;15>=c;c++){for(d=a;b>d;d++)n[32767+d]=c,m[32767+d]=[],m[32767+d][1]=c,m[32767+d][0]=d;for(e=-(b-1);-a>=e;e++)n[32767+e]=c,m[32767+e]=[],m[32767+e][1]=c,m[32767+e][0]=b-1+e;a<<=1,b<<=1}}function M(){for(var a=0;256>a;a++)x[a]=19595*a,x[a+256>>0]=38470*a,x[a+512>>0]=7471*a+32768,x[a+768>>0]=-11059*a,x[a+1024>>0]=-21709*a,x[a+1280>>0]=32768*a+8421375,x[a+1536>>0]=-27439*a,x[a+1792>>0]=-5329*a}function N(a){for(var b=a[0],c=a[1]-1;c>=0;)b&1<<c&&(r|=1<<s),c--,s--,0>s&&(255==r?(O(255),O(0)):O(r),s=7,r=0)}function O(a){q.push(w[a])}function P(a){O(255&a>>8),O(255&a)}function Q(a,b){var c,d,e,f,g,h,i,j,l,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,k=0,m=8,n=64;for(l=0;m>l;++l)c=a[k],d=a[k+1],e=a[k+2],f=a[k+3],g=a[k+4],h=a[k+5],i=a[k+6],j=a[k+7],p=c+j,q=c-j,r=d+i,s=d-i,t=e+h,u=e-h,v=f+g,w=f-g,x=p+v,y=p-v,z=r+t,A=r-t,a[k]=x+z,a[k+4]=x-z,B=.707106781*(A+y),a[k+2]=y+B,a[k+6]=y-B,x=w+u,z=u+s,A=s+q,C=.382683433*(x-A),D=.5411961*x+C,E=1.306562965*A+C,F=.707106781*z,G=q+F,H=q-F,a[k+5]=H+D,a[k+3]=H-D,a[k+1]=G+E,a[k+7]=G-E,k+=8;for(k=0,l=0;m>l;++l)c=a[k],d=a[k+8],e=a[k+16],f=a[k+24],g=a[k+32],h=a[k+40],i=a[k+48],j=a[k+56],I=c+j,J=c-j,K=d+i,L=d-i,M=e+h,N=e-h,O=f+g,P=f-g,Q=I+O,R=I-O,S=K+M,T=K-M,a[k]=Q+S,a[k+32]=Q-S,U=.707106781*(T+R),a[k+16]=R+U,a[k+48]=R-U,Q=P+N,S=N+L,T=L+J,V=.382683433*(Q-T),W=.5411961*Q+V,X=1.306562965*T+V,Y=.707106781*S,Z=J+Y,$=J-Y,a[k+40]=$+W,a[k+24]=$-W,a[k+8]=Z+X,a[k+56]=Z-X,k++;for(l=0;n>l;++l)_=a[l]*b[l],o[l]=_>0?0|_+.5:0|_-.5;return o}function R(){P(65504),P(16),O(74),O(70),O(73),O(70),O(0),O(1),O(1),O(0),P(1),P(1),O(0),O(0)}function S(a,b){P(65472),P(17),O(8),P(b),P(a),O(3),O(1),O(17),O(0),O(2),O(17),O(1),O(3),O(17),O(1)}function T(){var a,b;for(P(65499),P(132),O(0),a=0;64>a;a++)O(e[a]);for(O(1),b=0;64>b;b++)O(f[b])}function U(){var a,b,c,d,e,f,g,h;for(P(65476),P(418),O(0),a=0;16>a;a++)O(A[a+1]);for(b=0;11>=b;b++)O(B[b]);for(O(16),c=0;16>c;c++)O(C[c+1]);for(d=0;161>=d;d++)O(D[d]);for(O(1),e=0;16>e;e++)O(E[e+1]);for(f=0;11>=f;f++)O(F[f]);for(O(17),g=0;16>g;g++)O(G[g+1]);for(h=0;161>=h;h++)O(H[h])}function V(){P(65498),P(12),O(3),O(1),O(0),O(2),O(17),O(3),O(17),O(0),O(63),O(0)}function W(a,b,c,d,e){var h,o,q,r,s,t,u,v,w,f=e[0],g=e[240],i=16,j=63,k=64,l=Q(a,b);for(o=0;k>o;++o)p[z[o]]=l[o];for(q=p[0]-c,c=p[0],0==q?N(d[0]):(h=32767+q,N(d[n[h]]),N(m[h])),r=63;r>0&&0==p[r];r--);if(0==r)return N(f),c;for(s=1;r>=s;){for(u=s;0==p[s]&&r>=s;++s);if(v=s-u,v>=i){for(t=v>>4,w=1;t>=w;++w)N(g);v=15&v}h=32767+p[s],N(e[(v<<4)+n[h]]),N(m[h]),s++}return r!=j&&N(f),c}function X(){var b,a=String.fromCharCode;for(b=0;256>b;b++)w[b]=a(b)}function Y(a){if(0>=a&&(a=1),a>100&&(a=100),y!=a){var b=0;b=50>a?Math.floor(5e3/a):Math.floor(200-2*a),I(b),y=a}}function Z(){a||(a=50),X(),K(),L(),M(),Y(a)}var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H;Math.round,d=Math.floor,e=new Array(64),f=new Array(64),g=new Array(64),h=new Array(64),m=new Array(65535),n=new Array(65535),o=new Array(64),p=new Array(64),q=[],r=0,s=7,t=new Array(64),u=new Array(64),v=new Array(64),w=new Array(256),x=new Array(2048),z=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],A=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],B=[0,1,2,3,4,5,6,7,8,9,10,11],C=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],D=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],E=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],F=[0,1,2,3,4,5,6,7,8,9,10,11],G=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],H=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250],this.encode=function(a,b){var c,d,e,f,m,n,o,w,y,z,A,B,C,D,E,F,G,H,I;for(b&&Y(b),q=new Array,r=0,s=7,P(65496),R(),T(),S(a.width,a.height),U(),V(),c=0,d=0,e=0,r=0,s=7,this.encode.displayName="_encode_",f=a.data,m=a.width,n=a.height,o=4*m,y=0;n>y;){for(w=0;o>w;){for(C=o*y+w,D=C,E=-1,F=0,G=0;64>G;G++)F=G>>3,E=4*(7&G),D=C+F*o+E,y+F>=n&&(D-=o*(y+1+F-n)),w+E>=o&&(D-=w+E-o+4),z=f[D++],A=f[D++],B=f[D++],t[G]=(x[z]+x[A+256>>0]+x[B+512>>0]>>16)-128,u[G]=(x[z+768>>0]+x[A+1024>>0]+x[B+1280>>0]>>16)-128,v[G]=(x[z+1280>>0]+x[A+1536>>0]+x[B+1792>>0]>>16)-128;c=W(t,g,c,i,k),d=W(u,h,d,j,l),e=W(v,h,e,j,l),w+=32}y+=8}return s>=0&&(H=[],H[1]=s+1,H[0]=(1<<s+1)-1,N(H)),P(65497),I="data:image/jpeg;base64,"+btoa(q.join("")),q=[],I},Z()}return d.encode=function(a,b){var c=new d(b);return c.encode(a)},d}),b("runtime/html5/androidpatch",["runtime/html5/util","runtime/html5/jpegencoder","base"],function(a,b,c){var e,d=a.canvasToDataUrl;a.canvasToDataUrl=function(a,f,g){var h,i,j,k,l;return c.os.android?("image/jpeg"===f&&"undefined"==typeof e&&(k=d.apply(null,arguments),l=k.split(","),k=~l[0].indexOf("base64")?atob(l[1]):decodeURIComponent(l[1]),k=k.substring(0,2),e=255===k.charCodeAt(0)&&216===k.charCodeAt(1)),"image/jpeg"!==f||e?d.apply(null,arguments):(i=a.width,j=a.height,h=a.getContext("2d"),b.encode(h.getImageData(0,0,i,j),g))):d.apply(null,arguments)}}),b("runtime/html5/image",["base","runtime/html5/runtime","runtime/html5/util"],function(a,b,c){var d="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D";return b.register("Image",{modified:!1,init:function(){var a=this,b=new Image;b.onload=function(){a._info={type:a.type,width:this.width,height:this.height},a._metas||"image/jpeg"!==a.type?a.owner.trigger("load"):c.parseMeta(a._blob,function(b,c){a._metas=c,a.owner.trigger("load")})},b.onerror=function(){a.owner.trigger("error")},a._img=b},loadFromBlob:function(a){var b=this,d=b._img;b._blob=a,b.type=a.type,d.src=c.createObjectURL(a.getSource()),b.owner.once("load",function(){c.revokeObjectURL(d.src)})},resize:function(a,b){var c=this._canvas||(this._canvas=document.createElement("canvas"));this._resize(this._img,c,a,b),this._blob=null,this.modified=!0,this.owner.trigger("complete","resize")},crop:function(a,b,c,d,e){var f=this._canvas||(this._canvas=document.createElement("canvas")),g=this.options,h=this._img,i=h.naturalWidth,j=h.naturalHeight,k=this.getOrientation();e=e||1,f.width=c,f.height=d,g.preserveHeaders||this._rotate2Orientaion(f,k),this._renderImageToCanvas(f,h,-a,-b,i*e,j*e),this._blob=null,this.modified=!0,this.owner.trigger("complete","crop")},getAsBlob:function(a){var e,b=this._blob,d=this.options;if(a=a||this.type,this.modified||this.type!==a){if(e=this._canvas,"image/jpeg"===a){if(b=c.canvasToDataUrl(e,a,d.quality),d.preserveHeaders&&this._metas&&this._metas.imageHead)return b=c.dataURL2ArrayBuffer(b),b=c.updateImageHead(b,this._metas.imageHead),b=c.arrayBufferToBlob(b,a)}else b=c.canvasToDataUrl(e,a);b=c.dataURL2Blob(b)}return b},getAsDataUrl:function(a){var b=this.options;return a=a||this.type,"image/jpeg"===a?c.canvasToDataUrl(this._canvas,a,b.quality):this._canvas.toDataURL(a)},getOrientation:function(){return this._metas&&this._metas.exif&&this._metas.exif.get("Orientation")||1},info:function(a){return a?(this._info=a,this):this._info},meta:function(a){return a?(this._metas=a,this):this._metas},destroy:function(){var a=this._canvas;this._img.onload=null,a&&(a.getContext("2d").clearRect(0,0,a.width,a.height),a.width=a.height=0,this._canvas=null),this._img.src=d,this._img=this._blob=null},_resize:function(a,b,c,d){var i,j,k,l,m,e=this.options,f=a.width,g=a.height,h=this.getOrientation();~[5,6,7,8].indexOf(h)&&(c^=d,d^=c,c^=d),i=Math[e.crop?"max":"min"](c/f,d/g),e.allowMagnify||(i=Math.min(1,i)),j=f*i,k=g*i,e.crop?(b.width=c,b.height=d):(b.width=j,b.height=k),l=(b.width-j)/2,m=(b.height-k)/2,e.preserveHeaders||this._rotate2Orientaion(b,h),this._renderImageToCanvas(b,a,l,m,j,k)},_rotate2Orientaion:function(a,b){var c=a.width,d=a.height,e=a.getContext("2d");switch(b){case 5:case 6:case 7:case 8:a.width=d,a.height=c}switch(b){case 2:e.translate(c,0),e.scale(-1,1);break;case 3:e.translate(c,d),e.rotate(Math.PI);break;case 4:e.translate(0,d),e.scale(1,-1);break;case 5:e.rotate(.5*Math.PI),e.scale(1,-1);break;case 6:e.rotate(.5*Math.PI),e.translate(0,-d);break;case 7:e.rotate(.5*Math.PI),e.translate(c,-d),e.scale(-1,1);break;case 8:e.rotate(-.5*Math.PI),e.translate(-c,0)}},_renderImageToCanvas:function(){function b(a,b,c){var i,j,k,d=document.createElement("canvas"),e=d.getContext("2d"),f=0,g=c,h=c;for(d.width=1,d.height=c,e.drawImage(a,0,0),i=e.getImageData(0,0,1,c).data;h>f;)j=i[4*(h-1)+3],0===j?g=h:f=h,h=g+f>>1;return k=h/c,0===k?1:k}function c(a){var d,e,b=a.naturalWidth,c=a.naturalHeight;return b*c>1048576?(d=document.createElement("canvas"),d.width=d.height=1,e=d.getContext("2d"),e.drawImage(a,-b+1,0),0===e.getImageData(0,0,1,1).data[3]):!1}return a.os.ios?a.os.ios>=7?function(a,c,d,e,f,g){var h=c.naturalWidth,i=c.naturalHeight,j=b(c,h,i);return a.getContext("2d").drawImage(c,0,0,h*j,i*j,d,e,f,g)}:function(a,d,e,f,g,h){var q,r,s,t,u,v,w,i=d.naturalWidth,j=d.naturalHeight,k=a.getContext("2d"),l=c(d),m="image/jpeg"===this.type,n=1024,o=0,p=0;for(l&&(i/=2,j/=2),k.save(),q=document.createElement("canvas"),q.width=q.height=n,r=q.getContext("2d"),s=m?b(d,i,j):1,t=Math.ceil(n*g/i),u=Math.ceil(n*h/j/s);j>o;){for(v=0,w=0;i>v;)r.clearRect(0,0,n,n),r.drawImage(d,-v,-o),k.drawImage(q,0,0,n,n,e+w,f+p,t,u),v+=n,w+=t;o+=n,p+=u}k.restore(),q=r=null}:function(b){var c=a.slice(arguments,1),d=b.getContext("2d");d.drawImage.apply(d,c)}}()})}),b("runtime/html5/transport",["base","runtime/html5/runtime"],function(a,b){var c=a.noop,d=a.$;return b.register("Transport",{init:function(){this._status=0,this._response=null},send:function(){var h,i,j,b=this.owner,c=this.options,e=this._initAjax(),f=b._blob,g=c.server;c.sendAsBinary?(g+=(/\?/.test(g)?"&":"?")+d.param(b._formData),i=f.getSource()):(h=new FormData,d.each(b._formData,function(a,b){h.append(a,b)}),h.append(c.fileVal,f.getSource(),c.filename||b._formData.name||"")),c.withCredentials&&"withCredentials"in e?(e.open(c.method,g,!0),e.withCredentials=!0):e.open(c.method,g),this._setRequestHeader(e,c.headers),i?(e.overrideMimeType&&e.overrideMimeType("application/octet-stream"),a.os.android?(j=new FileReader,j.onload=function(){e.send(this.result),j=j.onload=null},j.readAsArrayBuffer(i)):e.send(i)):e.send(h)},getResponse:function(){return this._response},getResponseAsJson:function(){return this._parseJson(this._response)},getStatus:function(){return this._status},abort:function(){var a=this._xhr;a&&(a.upload.onprogress=c,a.onreadystatechange=c,a.abort(),this._xhr=a=null)},destroy:function(){this.abort()},_initAjax:function(){var a=this,b=new XMLHttpRequest,d=this.options;return!d.withCredentials||"withCredentials"in b||"undefined"==typeof XDomainRequest||(b=new XDomainRequest),b.upload.onprogress=function(b){var c=0;return b.lengthComputable&&(c=b.loaded/b.total),a.trigger("progress",c)},b.onreadystatechange=function(){return 4===b.readyState?(b.upload.onprogress=c,b.onreadystatechange=c,a._xhr=null,a._status=b.status,b.status>=200&&b.status<300?(a._response=b.responseText,a.trigger("load")):b.status>=500&&b.status<600?(a._response=b.responseText,a.trigger("error","server")):a.trigger("error",a._status?"http":"abort")):void 0},a._xhr=b,b},_setRequestHeader:function(a,b){d.each(b,function(b,c){a.setRequestHeader(b,c)})},_parseJson:function(a){var b;try{b=JSON.parse(a)}catch(c){b={}}return b}})}),b("runtime/html5/md5",["runtime/html5/runtime"],function(a){var b=function(a,b){return 4294967295&a+b},c=function(a,c,d,e,f,g){return c=b(b(c,a),b(e,g)),b(c<<f|c>>>32-f,d)},d=function(a,b,d,e,f,g,h){return c(b&d|~b&e,a,b,f,g,h)},e=function(a,b,d,e,f,g,h){return c(b&e|d&~e,a,b,f,g,h)},f=function(a,b,d,e,f,g,h){return c(b^d^e,a,b,f,g,h)},g=function(a,b,d,e,f,g,h){return c(d^(b|~e),a,b,f,g,h)},h=function(a,c){var h=a[0],i=a[1],j=a[2],k=a[3];h=d(h,i,j,k,c[0],7,-680876936),k=d(k,h,i,j,c[1],12,-389564586),j=d(j,k,h,i,c[2],17,606105819),i=d(i,j,k,h,c[3],22,-1044525330),h=d(h,i,j,k,c[4],7,-176418897),k=d(k,h,i,j,c[5],12,1200080426),j=d(j,k,h,i,c[6],17,-1473231341),i=d(i,j,k,h,c[7],22,-45705983),h=d(h,i,j,k,c[8],7,1770035416),k=d(k,h,i,j,c[9],12,-1958414417),j=d(j,k,h,i,c[10],17,-42063),i=d(i,j,k,h,c[11],22,-1990404162),h=d(h,i,j,k,c[12],7,1804603682),k=d(k,h,i,j,c[13],12,-40341101),j=d(j,k,h,i,c[14],17,-1502002290),i=d(i,j,k,h,c[15],22,1236535329),h=e(h,i,j,k,c[1],5,-165796510),k=e(k,h,i,j,c[6],9,-1069501632),j=e(j,k,h,i,c[11],14,643717713),i=e(i,j,k,h,c[0],20,-373897302),h=e(h,i,j,k,c[5],5,-701558691),k=e(k,h,i,j,c[10],9,38016083),j=e(j,k,h,i,c[15],14,-660478335),i=e(i,j,k,h,c[4],20,-405537848),h=e(h,i,j,k,c[9],5,568446438),k=e(k,h,i,j,c[14],9,-1019803690),j=e(j,k,h,i,c[3],14,-187363961),i=e(i,j,k,h,c[8],20,1163531501),h=e(h,i,j,k,c[13],5,-1444681467),k=e(k,h,i,j,c[2],9,-51403784),j=e(j,k,h,i,c[7],14,1735328473),i=e(i,j,k,h,c[12],20,-1926607734),h=f(h,i,j,k,c[5],4,-378558),k=f(k,h,i,j,c[8],11,-2022574463),j=f(j,k,h,i,c[11],16,1839030562),i=f(i,j,k,h,c[14],23,-35309556),h=f(h,i,j,k,c[1],4,-1530992060),k=f(k,h,i,j,c[4],11,1272893353),j=f(j,k,h,i,c[7],16,-155497632),i=f(i,j,k,h,c[10],23,-1094730640),h=f(h,i,j,k,c[13],4,681279174),k=f(k,h,i,j,c[0],11,-358537222),j=f(j,k,h,i,c[3],16,-722521979),i=f(i,j,k,h,c[6],23,76029189),h=f(h,i,j,k,c[9],4,-640364487),k=f(k,h,i,j,c[12],11,-421815835),j=f(j,k,h,i,c[15],16,530742520),i=f(i,j,k,h,c[2],23,-995338651),h=g(h,i,j,k,c[0],6,-198630844),k=g(k,h,i,j,c[7],10,1126891415),j=g(j,k,h,i,c[14],15,-1416354905),i=g(i,j,k,h,c[5],21,-57434055),h=g(h,i,j,k,c[12],6,1700485571),k=g(k,h,i,j,c[3],10,-1894986606),j=g(j,k,h,i,c[10],15,-1051523),i=g(i,j,k,h,c[1],21,-2054922799),h=g(h,i,j,k,c[8],6,1873313359),k=g(k,h,i,j,c[15],10,-30611744),j=g(j,k,h,i,c[6],15,-1560198380),i=g(i,j,k,h,c[13],21,1309151649),h=g(h,i,j,k,c[4],6,-145523070),k=g(k,h,i,j,c[11],10,-1120210379),j=g(j,k,h,i,c[2],15,718787259),i=g(i,j,k,h,c[9],21,-343485551),a[0]=b(h,a[0]),a[1]=b(i,a[1]),a[2]=b(j,a[2]),a[3]=b(k,a[3])},i=function(a){var c,b=[];for(c=0;64>c;c+=4)b[c>>2]=a.charCodeAt(c)+(a.charCodeAt(c+1)<<8)+(a.charCodeAt(c+2)<<16)+(a.charCodeAt(c+3)<<24);return b},j=function(a){var c,b=[];for(c=0;64>c;c+=4)b[c>>2]=a[c]+(a[c+1]<<8)+(a[c+2]<<16)+(a[c+3]<<24);return b},k=function(a){var d,e,f,g,j,k,b=a.length,c=[1732584193,-271733879,-1732584194,271733878];for(d=64;b>=d;d+=64)h(c,i(a.substring(d-64,d)));for(a=a.substring(d-64),e=a.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],d=0;e>d;d+=1)f[d>>2]|=a.charCodeAt(d)<<(d%4<<3);if(f[d>>2]|=128<<(d%4<<3),d>55)for(h(c,f),d=0;16>d;d+=1)f[d]=0;return g=8*b,g=g.toString(16).match(/(.*?)(.{0,8})$/),j=parseInt(g[2],16),k=parseInt(g[1],16)||0,f[14]=j,f[15]=k,h(c,f),c},l=function(a){var d,e,f,g,i,k,b=a.length,c=[1732584193,-271733879,-1732584194,271733878];for(d=64;b>=d;d+=64)h(c,j(a.subarray(d-64,d)));for(a=b>d-64?a.subarray(d-64):new Uint8Array(0),e=a.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],d=0;e>d;d+=1)f[d>>2]|=a[d]<<(d%4<<3);if(f[d>>2]|=128<<(d%4<<3),d>55)for(h(c,f),d=0;16>d;d+=1)f[d]=0;return g=8*b,g=g.toString(16).match(/(.*?)(.{0,8})$/),i=parseInt(g[2],16),k=parseInt(g[1],16)||0,f[14]=i,f[15]=k,h(c,f),c},m=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],n=function(a){var c,b="";for(c=0;4>c;c+=1)b+=m[15&a>>8*c+4]+m[15&a>>8*c];return b},o=function(a){var b;for(b=0;b<a.length;b+=1)a[b]=n(a[b]);return a.join("")},p=function(a){return o(k(a))},q=function(){this.reset()};return"5d41402abc4b2a76b9719d911017c592"!==p("hello")&&(b=function(a,b){var c=(65535&a)+(65535&b),d=(a>>16)+(b>>16)+(c>>16);return d<<16|65535&c}),q.prototype.append=function(a){return/[\u0080-\uFFFF]/.test(a)&&(a=unescape(encodeURIComponent(a))),this.appendBinary(a),this},q.prototype.appendBinary=function(a){this._buff+=a,this._length+=a.length;var c,b=this._buff.length;for(c=64;b>=c;c+=64)h(this._state,i(this._buff.substring(c-64,c)));return this._buff=this._buff.substr(c-64),this},q.prototype.end=function(a){var d,f,b=this._buff,c=b.length,e=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(d=0;c>d;d+=1)e[d>>2]|=b.charCodeAt(d)<<(d%4<<3);return this._finish(e,c),f=a?this._state:o(this._state),this.reset(),f},q.prototype._finish=function(a,b){var d,e,f,c=b;if(a[c>>2]|=128<<(c%4<<3),c>55)for(h(this._state,a),c=0;16>c;c+=1)a[c]=0;d=8*this._length,d=d.toString(16).match(/(.*?)(.{0,8})$/),e=parseInt(d[2],16),f=parseInt(d[1],16)||0,a[14]=e,a[15]=f,h(this._state,a)},q.prototype.reset=function(){return this._buff="",this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},q.prototype.destroy=function(){delete this._state,delete this._buff,delete this._length},q.hash=function(a,b){/[\u0080-\uFFFF]/.test(a)&&(a=unescape(encodeURIComponent(a)));
var c=k(a);return b?c:o(c)},q.hashBinary=function(a,b){var c=k(a);return b?c:o(c)},q.ArrayBuffer=function(){this.reset()},q.ArrayBuffer.prototype.append=function(a){var d,b=this._concatArrayBuffer(this._buff,a),c=b.length;for(this._length+=a.byteLength,d=64;c>=d;d+=64)h(this._state,j(b.subarray(d-64,d)));return this._buff=c>d-64?b.subarray(d-64):new Uint8Array(0),this},q.ArrayBuffer.prototype.end=function(a){var e,f,b=this._buff,c=b.length,d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;c>e;e+=1)d[e>>2]|=b[e]<<(e%4<<3);return this._finish(d,c),f=a?this._state:o(this._state),this.reset(),f},q.ArrayBuffer.prototype._finish=q.prototype._finish,q.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._state=[1732584193,-271733879,-1732584194,271733878],this},q.ArrayBuffer.prototype.destroy=q.prototype.destroy,q.ArrayBuffer.prototype._concatArrayBuffer=function(a,b){var c=a.length,d=new Uint8Array(c+b.byteLength);return d.set(a),d.set(new Uint8Array(b),c),d},q.ArrayBuffer.hash=function(a,b){var c=l(new Uint8Array(a));return b?c:o(c)},a.register("Md5",{init:function(){},loadFromBlob:function(a){var j,k,b=a.getSource(),c=2097152,d=Math.ceil(b.size/c),e=0,f=this.owner,g=new q.ArrayBuffer,h=this,i=b.mozSlice||b.webkitSlice||b.slice;k=new FileReader,j=function(){var l,m;l=e*c,m=Math.min(l+c,b.size),k.onload=function(b){g.append(b.target.result),f.trigger("progress",{total:a.size,loaded:m})},k.onloadend=function(){k.onloadend=k.onload=null,++e<d?setTimeout(j,1):setTimeout(function(){f.trigger("load"),h.result=g.end(),j=a=b=g=null,f.trigger("complete")},50)},k.readAsArrayBuffer(i.call(b,l,m))},j()},getResult:function(){return this.result}})}),b("runtime/flash/runtime",["base","runtime/runtime","runtime/compbase"],function(b,c,d){function h(){var a;try{a=navigator.plugins["Shockwave Flash"],a=a.description}catch(b){try{a=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(c){a="0.0"}}return a=a.match(/\d+/g),parseFloat(a[0]+"."+a[1],10)}function i(){function k(a,b){var d,f,c=a.type||a;d=c.split("::"),f=d[0],c=d[1],"Ready"===c&&f===i.uid?i.trigger("ready"):e[f]&&e[f].trigger(c.toLowerCase(),a,b)}var d={},e={},h=this.destroy,i=this,j=b.guid("webuploader_");c.apply(i,arguments),i.type=f,i.exec=function(a,c){var k,f=this,h=f.uid,j=b.slice(arguments,2);return e[h]=f,g[a]&&(d[h]||(d[h]=new g[a](f,i)),k=d[h],k[c])?k[c].apply(k,j):i.flashExec.apply(f,arguments)},a[j]=function(){var a=arguments;setTimeout(function(){k.apply(null,a)},1)},this.jsreciver=j,this.destroy=function(){return h&&h.apply(this,arguments)},this.flashExec=function(a,c){var d=i.getFlash(),e=b.slice(arguments,2);return d.exec(this.uid,a,c,e)}}var e=b.$,f="flash",g={};return b.inherits(c,{constructor:i,init:function(){var d,a=this.getContainer(),c=this.options;a.css({position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),d='<object id="'+this.uid+'" type="application/'+'x-shockwave-flash" data="'+c.swf+'" ',b.browser.ie&&(d+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),d+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+c.swf+'" />'+'<param name="flashvars" value="uid='+this.uid+"&jsreciver="+this.jsreciver+'" />'+'<param name="wmode" value="transparent" />'+'<param name="allowscriptaccess" value="always" />'+"</object>",a.html(d)},getFlash:function(){return this._flash?this._flash:(this._flash=e("#"+this.uid).get(0),this._flash)}}),i.register=function(a,c){return c=g[a]=b.inherits(d,e.extend({flashExec:function(){var a=this.owner,b=this.getRuntime();return b.flashExec.apply(a,arguments)}},c))},h()>=11.4&&c.addRuntime(f,i),i}),b("runtime/flash/filepicker",["base","runtime/flash/runtime"],function(a,b){var c=a.$;return b.register("FilePicker",{init:function(a){var d,e,b=c.extend({},a);for(d=b.accept&&b.accept.length,e=0;d>e;e++)b.accept[e].title||(b.accept[e].title="Files");delete b.button,delete b.id,delete b.container,this.flashExec("FilePicker","init",b)},destroy:function(){this.flashExec("FilePicker","destroy")}})}),b("runtime/flash/image",["runtime/flash/runtime"],function(a){return a.register("Image",{loadFromBlob:function(a){var b=this.owner;b.info()&&this.flashExec("Image","info",b.info()),b.meta()&&this.flashExec("Image","meta",b.meta()),this.flashExec("Image","loadFromBlob",a.uid)}})}),b("runtime/flash/transport",["base","runtime/flash/runtime","runtime/client"],function(b,c,d){var e=b.$;return c.register("Transport",{init:function(){this._status=0,this._response=null,this._responseJson=null},send:function(){var g,a=this.owner,b=this.options,c=this._initAjax(),d=a._blob,f=b.server;c.connectRuntime(d.ruid),b.sendAsBinary?(f+=(/\?/.test(f)?"&":"?")+e.param(a._formData),g=d.uid):(e.each(a._formData,function(a,b){c.exec("append",a,b)}),c.exec("appendBlob",b.fileVal,d.uid,b.filename||a._formData.name||"")),this._setRequestHeader(c,b.headers),c.exec("send",{method:b.method,url:f,forceURLStream:b.forceURLStream,mimeType:"application/octet-stream"},g)},getStatus:function(){return this._status},getResponse:function(){return this._response||""},getResponseAsJson:function(){return this._responseJson},abort:function(){var a=this._xhr;a&&(a.exec("abort"),a.destroy(),this._xhr=a=null)},destroy:function(){this.abort()},_initAjax:function(){var b=this,c=new d("XMLHttpRequest");return c.on("uploadprogress progress",function(a){var c=a.loaded/a.total;return c=Math.min(1,Math.max(0,c)),b.trigger("progress",c)}),c.on("load",function(){var g,d=c.exec("getStatus"),e=!1,f="";return c.off(),b._xhr=null,d>=200&&300>d?e=!0:d>=500&&600>d?(e=!0,f="server"):f="http",e&&(b._response=c.exec("getResponse"),b._response=decodeURIComponent(b._response),g=function(b){try{return a.JSON&&a.JSON.parse?JSON.parse(b):new Function("return "+b).call()}catch(c){return{}}},b._responseJson=b._response?g(b._response):{}),c.destroy(),c=null,f?b.trigger("error",f):b.trigger("load")}),c.on("error",function(){c.off(),b._xhr=null,b.trigger("error","http")}),b._xhr=c,c},_setRequestHeader:function(a,b){e.each(b,function(b,c){a.exec("setRequestHeader",b,c)})}})}),b("runtime/flash/blob",["runtime/flash/runtime","lib/blob"],function(a,b){return a.register("Blob",{slice:function(a,c){var d=this.flashExec("Blob","slice",a,c);return new b(this.getRuid(),d)}})}),b("runtime/flash/md5",["runtime/flash/runtime"],function(a){return a.register("Md5",{init:function(){},loadFromBlob:function(a){return this.flashExec("Md5","loadFromBlob",a.uid)}})}),b("preset/all",["base","widgets/filednd","widgets/filepaste","widgets/filepicker","widgets/image","widgets/queue","widgets/runtime","widgets/upload","widgets/validator","widgets/md5","runtime/html5/blob","runtime/html5/dnd","runtime/html5/filepaste","runtime/html5/filepicker","runtime/html5/imagemeta/exif","runtime/html5/androidpatch","runtime/html5/image","runtime/html5/transport","runtime/html5/md5","runtime/flash/filepicker","runtime/flash/image","runtime/flash/transport","runtime/flash/blob","runtime/flash/md5"],function(a){return a}),b("widgets/log",["base","uploader","widgets/widget"],function(a,b){function h(a){var b=c.extend({},g,a),e=d.replace(/^(.*)\?/,"$1"+c.param(b)),f=new Image;f.src=e}var g,c=a.$,d=" http://static.tieba.baidu.com/tb/pms/img/st.gif??",e=(location.hostname||location.host||"protected").toLowerCase(),f=e&&/baidu/i.exec(e);if(f)return g={dv:3,master:"webuploader",online:/test/.exec(e)?0:1,module:"",product:e,type:0},b.register({name:"log",init:function(){var a=this.owner,b=0,c=0;a.on("error",function(a){h({type:2,c_error_code:a})}).on("uploadError",function(a,b){h({type:2,c_error_code:"UPLOAD_ERROR",c_reason:""+b})}).on("uploadComplete",function(a){b++,c+=a.size}).on("uploadFinished",function(){h({c_count:b,c_size:c}),b=c=0}),h({c_usage:1})}})}),b("webuploader",["preset/all","widgets/log"],function(a){return a}),c("webuploader")});