!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e:e(jQuery,window,document)}(function(e){var t,o,a,n,i,r,l,s,c,d,u,f,m,h,p,g,v,x,S,_,w,C,b,y,B,T,k,M,O,I,D,E,W,R,A,L,z,P,H,U,F,q,Y,X,j,N,V,Q,G,J,K,Z,$,ee,te,oe,ae;oe="function"==typeof define&&define.amd,ae="undefined"!=typeof module&&module.exports,oe||(ae?require("jquery-mousewheel")(e):e.event.special.mousewheel||e("head").append(decodeURI("%3Cscript src=../fw/include/jquery-mCustomScrollbar/jquery.mousewheel.min.js%3E%3C/script%3E"))),o="mCustomScrollbar",a={setTop:0,setLeft:0,axis:"y",scrollbarPosition:"inside",scrollInertia:950,autoDraggerLength:!0,alwaysShowScrollbar:0,snapOffset:0,mouseWheel:{enable:!0,scrollAmount:"auto",axis:"y",deltaFactor:"auto",disableOver:["select","option","keygen","datalist","textarea"]},scrollButtons:{scrollType:"stepless",scrollAmount:"auto"},keyboard:{enable:!0,scrollType:"stepless",scrollAmount:"auto"},contentTouchScroll:25,documentTouchScroll:!0,advanced:{autoScrollOnFocus:"input,textarea,select,button,datalist,keygen,a[tabindex],area,object,[contenteditable='true']",updateOnContentResize:!0,updateOnImageLoad:"auto",autoUpdateTimeout:60},theme:"light",callbacks:{onTotalScrollOffset:0,onTotalScrollBackOffset:0,alwaysTriggerOffsets:!0}},n=0,i={},r=window.attachEvent&&!window.addEventListener?1:0,l=!1,s=["mCSB_dragger_onDrag","mCSB_scrollTools_onDrag","mCS_img_loaded","mCS_disabled","mCS_destroyed","mCS_no_scrollbar","mCS-autoHide","mCS-dir-rtl","mCS_no_scrollbar_y","mCS_no_scrollbar_x","mCS_y_hidden","mCS_x_hidden","mCSB_draggerContainer","mCSB_buttonUp","mCSB_buttonDown","mCSB_buttonLeft","mCSB_buttonRight"],c={init:function(t){var t=e.extend(!0,{},a,t),o=d.call(this);if(t.live){var r=t.liveSelector||this.selector||".mCustomScrollbar",l=e(r);if("off"===t.live)return void f(r);i[r]=setTimeout(function(){l.mCustomScrollbar(t),"once"===t.live&&l.length&&f(r)},500)}else f(r);return t.setWidth=t.set_width?t.set_width:t.setWidth,t.setHeight=t.set_height?t.set_height:t.setHeight,t.axis=t.horizontalScroll?"x":m(t.axis),t.scrollInertia=t.scrollInertia>0&&t.scrollInertia<17?17:t.scrollInertia,"object"!=typeof t.mouseWheel&&1==t.mouseWheel&&(t.mouseWheel={enable:!0,scrollAmount:"auto",axis:"y",preventDefault:!1,deltaFactor:"auto",normalizeDelta:!1,invert:!1}),t.mouseWheel.scrollAmount=t.mouseWheelPixels?t.mouseWheelPixels:t.mouseWheel.scrollAmount,t.mouseWheel.normalizeDelta=t.advanced.normalizeMouseWheelDelta?t.advanced.normalizeMouseWheelDelta:t.mouseWheel.normalizeDelta,t.scrollButtons.scrollType=h(t.scrollButtons.scrollType),u(t),e(o).each(function(){var o=e(this);if(!o.data("mCS")){o.data("mCS",{idx:++n,opt:t,scrollRatio:{y:null,x:null},overflowed:null,contentReset:{y:null,x:null},bindEvents:!1,tweenRunning:!1,sequential:{},langDir:o.css("direction"),cbOffsets:null,trigger:null,poll:{size:{o:0,n:0},img:{o:0,n:0},change:{o:0,n:0}}});var a=o.data("mCS"),i=a.opt,r=o.data("mcs-axis"),l=o.data("mcs-scrollbar-position"),d=o.data("mcs-theme");r&&(i.axis=r),l&&(i.scrollbarPosition=l),d&&(i.theme=d,u(i)),p.call(this),a&&i.callbacks.onCreate&&"function"==typeof i.callbacks.onCreate&&i.callbacks.onCreate.call(this),e("#mCSB_"+a.idx+"_container img:not(."+s[2]+")").addClass(s[2]),c.update.call(null,o)}})},update:function(t,o){var a=t||d.call(this);return e(a).each(function(){var t=e(this);if(t.data("mCS")){var a=t.data("mCS"),n=a.opt,i=e("#mCSB_"+a.idx+"_container"),r=e("#mCSB_"+a.idx),l=[e("#mCSB_"+a.idx+"_dragger_vertical"),e("#mCSB_"+a.idx+"_dragger_horizontal")];if(!i.length)return;a.tweenRunning&&j(t),o&&a&&n.callbacks.onBeforeUpdate&&"function"==typeof n.callbacks.onBeforeUpdate&&n.callbacks.onBeforeUpdate.call(this),t.hasClass(s[3])&&t.removeClass(s[3]),t.hasClass(s[4])&&t.removeClass(s[4]),r.css("max-height","none"),r.height()!==t.height()&&r.css("max-height",t.height()),v.call(this),"y"===n.axis||n.advanced.autoExpandHorizontalScroll||i.css("width",g(i)),a.overflowed=C.call(this),T.call(this),n.autoDraggerLength&&S.call(this),_.call(this),y.call(this);var c=[Math.abs(i[0].offsetTop),Math.abs(i[0].offsetLeft)];"x"!==n.axis&&(a.overflowed[0]?l[0].height()>l[0].parent().height()?b.call(this):(N(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}),a.contentReset.y=null):(b.call(this),"y"===n.axis?B.call(this):"yx"===n.axis&&a.overflowed[1]&&N(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}))),"y"!==n.axis&&(a.overflowed[1]?l[1].width()>l[1].parent().width()?b.call(this):(N(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}),a.contentReset.x=null):(b.call(this),"x"===n.axis?B.call(this):"yx"===n.axis&&a.overflowed[0]&&N(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}))),o&&a&&(2===o&&n.callbacks.onImageLoad&&"function"==typeof n.callbacks.onImageLoad?n.callbacks.onImageLoad.call(this):3===o&&n.callbacks.onSelectorChange&&"function"==typeof n.callbacks.onSelectorChange?n.callbacks.onSelectorChange.call(this):n.callbacks.onUpdate&&"function"==typeof n.callbacks.onUpdate&&n.callbacks.onUpdate.call(this)),X.call(this)}})},scrollTo:function(t,o){if(void 0!==t&&null!=t){var a=d.call(this);return e(a).each(function(){var a=e(this);if(a.data("mCS")){var n=a.data("mCS"),i=n.opt,r={trigger:"external",scrollInertia:i.scrollInertia,scrollEasing:"mcsEaseInOut",moveDragger:!1,timeout:60,callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},l=e.extend(!0,{},r,o),s=q.call(this,t),c=l.scrollInertia>0&&l.scrollInertia<17?17:l.scrollInertia;s[0]=Y.call(this,s[0],"y"),s[1]=Y.call(this,s[1],"x"),l.moveDragger&&(s[0]*=n.scrollRatio.y,s[1]*=n.scrollRatio.x),l.dur=te()?0:c,setTimeout(function(){null!==s[0]&&void 0!==s[0]&&"x"!==i.axis&&n.overflowed[0]&&(l.dir="y",l.overwrite="all",N(a,s[0].toString(),l)),null!==s[1]&&void 0!==s[1]&&"y"!==i.axis&&n.overflowed[1]&&(l.dir="x",l.overwrite="none",N(a,s[1].toString(),l))},l.timeout)}})}},stop:function(){var t=d.call(this);return e(t).each(function(){var t=e(this);t.data("mCS")&&j(t)})},disable:function(t){var o=d.call(this);return e(o).each(function(){var o=e(this);o.data("mCS")&&(o.data("mCS"),X.call(this,"remove"),B.call(this),t&&b.call(this),T.call(this,!0),o.addClass(s[3]))})},destroy:function(){var t=d.call(this);return e(t).each(function(){var a=e(this);if(a.data("mCS")){var n=a.data("mCS"),i=n.opt,r=e("#mCSB_"+n.idx),l=e("#mCSB_"+n.idx+"_container"),c=e(".mCSB_"+n.idx+"_scrollbar");i.live&&f(i.liveSelector||e(t).selector),X.call(this,"remove"),B.call(this),b.call(this),a.removeData("mCS"),J(this,"mcs"),c.remove(),l.find("img."+s[2]).removeClass(s[2]),r.replaceWith(l.contents()),a.removeClass(o+" _mCS_"+n.idx+" "+s[6]+" "+s[7]+" "+s[5]+" "+s[3]).addClass(s[4])}})}},d=function(){return"object"!=typeof e(this)||e(this).length<1?".mCustomScrollbar":this},u=function(t){t.autoDraggerLength=!(e.inArray(t.theme,["rounded","rounded-dark","rounded-dots","rounded-dots-dark"])>-1)&&t.autoDraggerLength,t.autoExpandScrollbar=!(e.inArray(t.theme,["rounded-dots","rounded-dots-dark","3d","3d-dark","3d-thick","3d-thick-dark","inset","inset-dark","inset-2","inset-2-dark","inset-3","inset-3-dark"])>-1)&&t.autoExpandScrollbar,t.scrollButtons.enable=!(e.inArray(t.theme,["minimal","minimal-dark"])>-1)&&t.scrollButtons.enable,t.autoHideScrollbar=e.inArray(t.theme,["minimal","minimal-dark"])>-1||t.autoHideScrollbar,t.scrollbarPosition=e.inArray(t.theme,["minimal","minimal-dark"])>-1?"outside":t.scrollbarPosition},f=function(e){i[e]&&(clearTimeout(i[e]),J(i,e))},m=function(e){return"yx"===e||"xy"===e||"auto"===e?"yx":"x"===e||"horizontal"===e?"x":"y"},h=function(e){return"stepped"===e||"pixels"===e||"step"===e||"click"===e?"stepped":"stepless"},p=function(){var t=e(this),a=t.data("mCS"),n=a.opt,i=n.autoExpandScrollbar?" "+s[1]+"_expand":"",r=["<div id='mCSB_"+a.idx+"_scrollbar_vertical' class='mCSB_scrollTools mCSB_"+a.idx+"_scrollbar mCS-"+n.theme+" mCSB_scrollTools_vertical"+i+"'><div class='"+s[12]+"'><div id='mCSB_"+a.idx+"_dragger_vertical' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>","<div id='mCSB_"+a.idx+"_scrollbar_horizontal' class='mCSB_scrollTools mCSB_"+a.idx+"_scrollbar mCS-"+n.theme+" mCSB_scrollTools_horizontal"+i+"'><div class='"+s[12]+"'><div id='mCSB_"+a.idx+"_dragger_horizontal' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>"],l="yx"===n.axis?"mCSB_vertical_horizontal":"x"===n.axis?"mCSB_horizontal":"mCSB_vertical",c="yx"===n.axis?r[0]+r[1]:"x"===n.axis?r[1]:r[0],d="yx"===n.axis?"<div id='mCSB_"+a.idx+"_container_wrapper' class='mCSB_container_wrapper' />":"",u=n.autoHideScrollbar?" "+s[6]:"",f="x"!==n.axis&&"rtl"===a.langDir?" "+s[7]:"";n.setWidth&&t.css("width",n.setWidth),n.setHeight&&t.css("height",n.setHeight),n.setLeft="y"!==n.axis&&"rtl"===a.langDir?"989999px":n.setLeft,t.addClass(o+" _mCS_"+a.idx+u+f).wrapInner("<div id='mCSB_"+a.idx+"' class='mCustomScrollBox mCS-"+n.theme+" "+l+"'><div id='mCSB_"+a.idx+"_container' class='mCSB_container' style='position:relative; top:"+n.setTop+"; left:"+n.setLeft+";' dir='"+a.langDir+"' /></div>");var m=e("#mCSB_"+a.idx),h=e("#mCSB_"+a.idx+"_container");"y"===n.axis||n.advanced.autoExpandHorizontalScroll||h.css("width",g(h)),"outside"===n.scrollbarPosition?("static"===t.css("position")&&t.css("position","relative"),t.css("overflow","visible"),m.addClass("mCSB_outside").after(c)):(m.addClass("mCSB_inside").append(c),h.wrap(d)),x.call(this);var p=[e("#mCSB_"+a.idx+"_dragger_vertical"),e("#mCSB_"+a.idx+"_dragger_horizontal")];p[0].css("min-height",p[0].height()),p[1].css("min-width",p[1].width())},g=function(t){var o=[t[0].scrollWidth,Math.max.apply(Math,t.children().map(function(){return e(this).outerWidth(!0)}).get())],a=t.parent().width();return o[0]>a?o[0]:o[1]>a?o[1]:"100%"},v=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n=e("#mCSB_"+o.idx+"_container");if(a.advanced.autoExpandHorizontalScroll&&"y"!==a.axis){n.css({width:"auto","min-width":0,"overflow-x":"scroll"});var i=Math.ceil(n[0].scrollWidth);3===a.advanced.autoExpandHorizontalScroll||2!==a.advanced.autoExpandHorizontalScroll&&i>n.parent().width()?n.css({width:i,"min-width":"100%","overflow-x":"inherit"}):n.css({"overflow-x":"inherit",position:"absolute"}).wrap("<div class='mCSB_h_wrapper' style='position:relative; left:0; width:999999px;' />").css({width:Math.ceil(n[0].getBoundingClientRect().right+.4)-Math.floor(n[0].getBoundingClientRect().left),"min-width":"100%",position:"relative"}).unwrap()}},x=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n=e(".mCSB_"+o.idx+"_scrollbar:first"),i=$(a.scrollButtons.tabindex)?"tabindex='"+a.scrollButtons.tabindex+"'":"",r=["<a href='#' class='"+s[13]+"' "+i+" />","<a href='#' class='"+s[14]+"' "+i+" />","<a href='#' class='"+s[15]+"' "+i+" />","<a href='#' class='"+s[16]+"' "+i+" />"],l=["x"===a.axis?r[2]:r[0],"x"===a.axis?r[3]:r[1],r[2],r[3]];a.scrollButtons.enable&&n.prepend(l[0]).append(l[1]).next(".mCSB_scrollTools").prepend(l[2]).append(l[3])},S=function(){var t=e(this),o=t.data("mCS"),a=e("#mCSB_"+o.idx),n=e("#mCSB_"+o.idx+"_container"),i=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")],l=[a.height()/n.outerHeight(!1),a.width()/n.outerWidth(!1)],s=[parseInt(i[0].css("min-height")),Math.round(l[0]*i[0].parent().height()),parseInt(i[1].css("min-width")),Math.round(l[1]*i[1].parent().width())],c=r&&s[1]<s[0]?s[0]:s[1],d=r&&s[3]<s[2]?s[2]:s[3];i[0].css({height:c,"max-height":i[0].parent().height()-10}).find(".mCSB_dragger_bar").css({"line-height":s[0]+"px"}),i[1].css({width:d,"max-width":i[1].parent().width()-10})},_=function(){var t=e(this),o=t.data("mCS"),a=e("#mCSB_"+o.idx),n=e("#mCSB_"+o.idx+"_container"),i=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")],r=[n.outerHeight(!1)-a.height(),n.outerWidth(!1)-a.width()],l=[r[0]/(i[0].parent().height()-i[0].height()),r[1]/(i[1].parent().width()-i[1].width())];o.scrollRatio={y:l[0],x:l[1]}},w=function(e,t,o){var a=o?s[0]+"_expanded":"",n=e.closest(".mCSB_scrollTools");"active"===t?(e.toggleClass(s[0]+" "+a),n.toggleClass(s[1]),e[0]._draggable=e[0]._draggable?0:1):e[0]._draggable||("hide"===t?(e.removeClass(s[0]),n.removeClass(s[1])):(e.addClass(s[0]),n.addClass(s[1])))},C=function(){var t=e(this),o=t.data("mCS"),a=e("#mCSB_"+o.idx),n=e("#mCSB_"+o.idx+"_container"),i=null==o.overflowed?n.height():n.outerHeight(!1),r=null==o.overflowed?n.width():n.outerWidth(!1),l=n[0].scrollHeight,s=n[0].scrollWidth;return l>i&&(i=l),s>r&&(r=s),[i>a.height(),r>a.width()]},b=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n=e("#mCSB_"+o.idx),i=e("#mCSB_"+o.idx+"_container"),r=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")];if(j(t),("x"!==a.axis&&!o.overflowed[0]||"y"===a.axis&&o.overflowed[0])&&(r[0].add(i).css("top",0),N(t,"_resetY")),"y"!==a.axis&&!o.overflowed[1]||"x"===a.axis&&o.overflowed[1]){var l=dx=0;"rtl"===o.langDir&&(l=n.width()-i.outerWidth(!1),dx=Math.abs(l/o.scrollRatio.x)),i.css("left",l),r[1].css("left",dx),N(t,"_resetX")}},y=function(){var t=e(this),o=t.data("mCS"),a=o.opt;if(!o.bindEvents){var n;if(M.call(this),a.contentTouchScroll&&O.call(this),I.call(this),a.mouseWheel.enable)!function o(){n=setTimeout(function(){e.event.special.mousewheel?(clearTimeout(n),D.call(t[0])):o()},100)}();L.call(this),P.call(this),a.advanced.autoScrollOnFocus&&z.call(this),a.scrollButtons.enable&&H.call(this),a.keyboard.enable&&U.call(this),o.bindEvents=!0}},B=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n="mCS_"+o.idx,i=".mCSB_"+o.idx+"_scrollbar",r=e("#mCSB_"+o.idx+",#mCSB_"+o.idx+"_container,#mCSB_"+o.idx+"_container_wrapper,"+i+" ."+s[12]+",#mCSB_"+o.idx+"_dragger_vertical,#mCSB_"+o.idx+"_dragger_horizontal,"+i+">a"),l=e("#mCSB_"+o.idx+"_container");a.advanced.releaseDraggableSelectors&&r.add(e(a.advanced.releaseDraggableSelectors)),a.advanced.extraDraggableSelectors&&r.add(e(a.advanced.extraDraggableSelectors)),o.bindEvents&&(e(document).add(e(!W()||top.document)).unbind("."+n),r.each(function(){e(this).unbind("."+n)}),clearTimeout(t[0]._focusTimeout),J(t[0],"_focusTimeout"),clearTimeout(o.sequential.step),J(o.sequential,"step"),clearTimeout(l[0].onCompleteTimeout),J(l[0],"onCompleteTimeout"),o.bindEvents=!1)},T=function(t){var o=e(this),a=o.data("mCS"),n=a.opt,i=e("#mCSB_"+a.idx+"_container_wrapper"),r=i.length?i:e("#mCSB_"+a.idx+"_container"),l=[e("#mCSB_"+a.idx+"_scrollbar_vertical"),e("#mCSB_"+a.idx+"_scrollbar_horizontal")],c=[l[0].find(".mCSB_dragger"),l[1].find(".mCSB_dragger")];"x"!==n.axis&&(a.overflowed[0]&&!t?(l[0].add(c[0]).add(l[0].children("a")).css("display","block"),r.removeClass(s[8]+" "+s[10])):(n.alwaysShowScrollbar?(2!==n.alwaysShowScrollbar&&c[0].css("display","none"),r.removeClass(s[10])):(l[0].css("display","none"),r.addClass(s[10])),r.addClass(s[8]))),"y"!==n.axis&&(a.overflowed[1]&&!t?(l[1].add(c[1]).add(l[1].children("a")).css("display","block"),r.removeClass(s[9]+" "+s[11])):(n.alwaysShowScrollbar?(2!==n.alwaysShowScrollbar&&c[1].css("display","none"),r.removeClass(s[11])):(l[1].css("display","none"),r.addClass(s[11])),r.addClass(s[9]))),a.overflowed[0]||a.overflowed[1]?o.removeClass(s[5]):o.addClass(s[5])},k=function(t){var o=t.type,a=t.target.ownerDocument!==document&&null!==frameElement?[e(frameElement).offset().top,e(frameElement).offset().left]:null,n=W()&&t.target.ownerDocument!==top.document&&null!==frameElement?[e(t.view.frameElement).offset().top,e(t.view.frameElement).offset().left]:[0,0];switch(o){case"pointerdown":case"MSPointerDown":case"pointermove":case"MSPointerMove":case"pointerup":case"MSPointerUp":return a?[t.originalEvent.pageY-a[0]+n[0],t.originalEvent.pageX-a[1]+n[1],!1]:[t.originalEvent.pageY,t.originalEvent.pageX,!1];case"touchstart":case"touchmove":case"touchend":var i=t.originalEvent.touches[0]||t.originalEvent.changedTouches[0],r=t.originalEvent.touches.length||t.originalEvent.changedTouches.length;return t.target.ownerDocument!==document?[i.screenY,i.screenX,r>1]:[i.pageY,i.pageX,r>1];default:return a?[t.pageY-a[0]+n[0],t.pageX-a[1]+n[1],!1]:[t.pageY,t.pageX,!1]}},M=function(){var t,o,a,n=e(this),i=n.data("mCS"),s=i.opt,c="mCS_"+i.idx,d=["mCSB_"+i.idx+"_dragger_vertical","mCSB_"+i.idx+"_dragger_horizontal"],u=e("#mCSB_"+i.idx+"_container"),f=e("#"+d[0]+",#"+d[1]),m=s.advanced.releaseDraggableSelectors?f.add(e(s.advanced.releaseDraggableSelectors)):f,h=s.advanced.extraDraggableSelectors?e(!W()||top.document).add(e(s.advanced.extraDraggableSelectors)):e(!W()||top.document);function p(e,o,a,r){if(u[0].idleTimer=s.scrollInertia<233?250:0,t.attr("id")===d[1])var l="x",c=(t[0].offsetLeft-o+r)*i.scrollRatio.x;else var l="y",c=(t[0].offsetTop-e+a)*i.scrollRatio.y;N(n,c.toString(),{dir:l,drag:!0})}f.bind("contextmenu."+c,function(e){e.preventDefault()}).bind("mousedown."+c+" touchstart."+c+" pointerdown."+c+" MSPointerDown."+c,function(i){if(i.stopImmediatePropagation(),i.preventDefault(),K(i)){l=!0,r&&(document.onselectstart=function(){return!1}),R.call(u,!1),j(n);var c=(t=e(this)).offset(),d=k(i)[0]-c.top,f=k(i)[1]-c.left,m=t.height()+c.top,h=t.width()+c.left;d<m&&d>0&&f<h&&f>0&&(o=d,a=f),w(t,"active",s.autoExpandScrollbar)}}).bind("touchmove."+c,function(e){e.stopImmediatePropagation(),e.preventDefault();var n=t.offset(),i=k(e)[0]-n.top,r=k(e)[1]-n.left;p(o,a,i,r)}),e(document).add(h).bind("mousemove."+c+" pointermove."+c+" MSPointerMove."+c,function(e){if(t){var n=t.offset(),i=k(e)[0]-n.top,r=k(e)[1]-n.left;if(o===i&&a===r)return;p(o,a,i,r)}}).add(m).bind("mouseup."+c+" touchend."+c+" pointerup."+c+" MSPointerUp."+c,function(e){t&&(w(t,"active",s.autoExpandScrollbar),t=null),l=!1,r&&(document.onselectstart=null),R.call(u,!0)})},O=function(){var o,a,n,i,r,s,c,d,u,f,m,h,p,g,v=e(this),x=v.data("mCS"),S=x.opt,_="mCS_"+x.idx,w=e("#mCSB_"+x.idx),C=e("#mCSB_"+x.idx+"_container"),b=[e("#mCSB_"+x.idx+"_dragger_vertical"),e("#mCSB_"+x.idx+"_dragger_horizontal")],y=[],B=[],T=0,M="yx"===S.axis?"none":"all",O=[],I=C.find("iframe"),D=["touchstart."+_+" pointerdown."+_+" MSPointerDown."+_,"touchmove."+_+" pointermove."+_+" MSPointerMove."+_,"touchend."+_+" pointerup."+_+" MSPointerUp."+_],E=void 0!==document.body.style.touchAction&&""!==document.body.style.touchAction;function R(e){if(!Z(e)||l||k(e)[2])t=0;else{t=1,p=0,g=0,o=1,v.removeClass("mCS_touch_action");var i=C.offset();a=k(e)[0]-i.top,n=k(e)[1]-i.left,O=[k(e)[0],k(e)[1]]}}function A(e){if(Z(e)&&!l&&!k(e)[2]&&(S.documentTouchScroll||e.preventDefault(),e.stopImmediatePropagation(),(!g||p)&&o)){c=Q();var t=w.offset(),i=k(e)[0]-t.top,r=k(e)[1]-t.left;if(y.push(i),B.push(r),O[2]=Math.abs(k(e)[0]-O[0]),O[3]=Math.abs(k(e)[1]-O[1]),x.overflowed[0])var s=b[0].parent().height()-b[0].height(),d=a-i>0&&i-a>-s*x.scrollRatio.y&&(2*O[3]<O[2]||"yx"===S.axis);if(x.overflowed[1])var u=b[1].parent().width()-b[1].width(),f=n-r>0&&r-n>-u*x.scrollRatio.x&&(2*O[2]<O[3]||"yx"===S.axis);d||f?(E||e.preventDefault(),p=1):(g=1,v.addClass("mCS_touch_action")),E&&e.preventDefault(),m="yx"===S.axis?[a-i,n-r]:"x"===S.axis?[null,n-r]:[a-i,null],C[0].idleTimer=250,x.overflowed[0]&&H(m[0],T,"mcsLinearOut","y","all",!0),x.overflowed[1]&&H(m[1],T,"mcsLinearOut","x",M,!0)}}function L(e){if(!Z(e)||l||k(e)[2])t=0;else{t=1,e.stopImmediatePropagation(),j(v),s=Q();var o=w.offset();i=k(e)[0]-o.top,r=k(e)[1]-o.left,y=[],B=[]}}function z(e){if(Z(e)&&!l&&!k(e)[2]){o=0,e.stopImmediatePropagation(),p=0,g=0,d=Q();var t=w.offset(),a=k(e)[0]-t.top,n=k(e)[1]-t.left;if(!(d-c>30)){var v=(f=1e3/(d-s))<2.5,_=v?[y[y.length-2],B[B.length-2]]:[0,0];u=v?[a-_[0],n-_[1]]:[a-i,n-r];var b=[Math.abs(u[0]),Math.abs(u[1])];f=v?[Math.abs(u[0]/4),Math.abs(u[1]/4)]:[f,f];var T=[Math.abs(C[0].offsetTop)-u[0]*P(b[0]/f[0],f[0]),Math.abs(C[0].offsetLeft)-u[1]*P(b[1]/f[1],f[1])];m="yx"===S.axis?[T[0],T[1]]:"x"===S.axis?[null,T[1]]:[T[0],null],h=[4*b[0]+S.scrollInertia,4*b[1]+S.scrollInertia];var O=parseInt(S.contentTouchScroll)||0;m[0]=b[0]>O?m[0]:0,m[1]=b[1]>O?m[1]:0,x.overflowed[0]&&H(m[0],h[0],"mcsEaseOut","y",M,!1),x.overflowed[1]&&H(m[1],h[1],"mcsEaseOut","x",M,!1)}}}function P(e,t){var o=[1.5*t,2*t,t/1.5,t/2];return e>90?t>4?o[0]:o[3]:e>60?t>3?o[3]:o[2]:e>30?t>8?o[1]:t>6?o[0]:t>4?t:o[2]:t>8?t:o[3]}function H(e,t,o,a,n,i){e&&N(v,e.toString(),{dur:t,scrollEasing:o,dir:a,overwrite:n,drag:i})}C.bind(D[0],function(e){R(e)}).bind(D[1],function(e){A(e)}),w.bind(D[0],function(e){L(e)}).bind(D[2],function(e){z(e)}),I.length&&I.each(function(){e(this).bind("load",function(){W(this)&&e(this.contentDocument||this.contentWindow.document).bind(D[0],function(e){R(e),L(e)}).bind(D[1],function(e){A(e)}).bind(D[2],function(e){z(e)})})})},I=function(){var o,a=e(this),n=a.data("mCS"),i=n.opt,r=n.sequential,s="mCS_"+n.idx,c=e("#mCSB_"+n.idx+"_container"),d=c.parent();function u(e,t,n){r.type=n&&o?"stepped":"stepless",r.scrollAmount=10,F(a,e,t,"mcsLinearOut",n?60:null)}c.bind("mousedown."+s,function(e){t||o||(o=1,l=!0)}).add(document).bind("mousemove."+s,function(e){if(!t&&o&&(window.getSelection?window.getSelection().toString():document.selection&&"Control"!=document.selection.type&&document.selection.createRange().text)){var a=c.offset(),l=k(e)[0]-a.top+c[0].offsetTop,s=k(e)[1]-a.left+c[0].offsetLeft;l>0&&l<d.height()&&s>0&&s<d.width()?r.step&&u("off",null,"stepped"):("x"!==i.axis&&n.overflowed[0]&&(l<0?u("on",38):l>d.height()&&u("on",40)),"y"!==i.axis&&n.overflowed[1]&&(s<0?u("on",37):s>d.width()&&u("on",39)))}}).bind("mouseup."+s+" dragend."+s,function(e){t||(o&&(o=0,u("off",null)),l=!1)})},D=function(){if(e(this).data("mCS")){var t=e(this),o=t.data("mCS"),a=o.opt,n="mCS_"+o.idx,i=e("#mCSB_"+o.idx),l=[e("#mCSB_"+o.idx+"_dragger_vertical"),e("#mCSB_"+o.idx+"_dragger_horizontal")],s=e("#mCSB_"+o.idx+"_container").find("iframe");s.length&&s.each(function(){e(this).bind("load",function(){W(this)&&e(this.contentDocument||this.contentWindow.document).bind("mousewheel."+n,function(e,t){c(e,t)})})}),i.bind("mousewheel."+n,function(e,t){c(e,t)})}function c(n,s){if(j(t),!A(t,n.target)){var c="auto"!==a.mouseWheel.deltaFactor?parseInt(a.mouseWheel.deltaFactor):r&&n.deltaFactor<100?100:n.deltaFactor||100,d=a.scrollInertia;if("x"===a.axis||"x"===a.mouseWheel.axis)var u="x",f=[Math.round(c*o.scrollRatio.x),parseInt(a.mouseWheel.scrollAmount)],m="auto"!==a.mouseWheel.scrollAmount?f[1]:f[0]>=i.width()?.9*i.width():f[0],h=Math.abs(e("#mCSB_"+o.idx+"_container")[0].offsetLeft),p=l[1][0].offsetLeft,g=l[1].parent().width()-l[1].width(),v="y"===a.mouseWheel.axis?n.deltaY||s:n.deltaX;else var u="y",f=[Math.round(c*o.scrollRatio.y),parseInt(a.mouseWheel.scrollAmount)],m="auto"!==a.mouseWheel.scrollAmount?f[1]:f[0]>=i.height()?.9*i.height():f[0],h=Math.abs(e("#mCSB_"+o.idx+"_container")[0].offsetTop),p=l[0][0].offsetTop,g=l[0].parent().height()-l[0].height(),v=n.deltaY||s;"y"===u&&!o.overflowed[0]||"x"===u&&!o.overflowed[1]||((a.mouseWheel.invert||n.webkitDirectionInvertedFromDevice)&&(v=-v),a.mouseWheel.normalizeDelta&&(v=v<0?-1:1),(v>0&&0!==p||v<0&&p!==g||a.mouseWheel.preventDefault)&&(n.stopImmediatePropagation(),n.preventDefault()),n.deltaFactor<5&&!a.mouseWheel.normalizeDelta&&(m=n.deltaFactor,d=17),N(t,(h-v*m).toString(),{dir:u,dur:d}))}}},E=new Object,W=function(t){var o=!1,a=!1,n=null;if(void 0===t?a="#empty":void 0!==e(t).attr("id")&&(a=e(t).attr("id")),!1!==a&&void 0!==E[a])return E[a];if(t){try{var i=t.contentDocument||t.contentWindow.document;n=i.body.innerHTML}catch(e){}o=null!==n}else{try{var i=top.document;n=i.body.innerHTML}catch(e){}o=null!==n}return!1!==a&&(E[a]=o),o},R=function(e){var t=this.find("iframe");if(t.length){var o=e?"auto":"none";t.css("pointer-events",o)}},A=function(t,o){var a=o.nodeName.toLowerCase(),n=t.data("mCS").opt.mouseWheel.disableOver;return e.inArray(a,n)>-1&&!(e.inArray(a,["select","textarea"])>-1&&!e(o).is(":focus"))},L=function(){var t,o=e(this),a=o.data("mCS"),n="mCS_"+a.idx,i=e("#mCSB_"+a.idx+"_container"),r=i.parent(),c=e(".mCSB_"+a.idx+"_scrollbar ."+s[12]);c.bind("mousedown."+n+" touchstart."+n+" pointerdown."+n+" MSPointerDown."+n,function(o){l=!0,e(o.target).hasClass("mCSB_dragger")||(t=1)}).bind("touchend."+n+" pointerup."+n+" MSPointerUp."+n,function(e){l=!1}).bind("click."+n,function(n){if(t&&(t=0,e(n.target).hasClass(s[12])||e(n.target).hasClass("mCSB_draggerRail"))){j(o);var l=e(this),c=l.find(".mCSB_dragger");if(l.parent(".mCSB_scrollTools_horizontal").length>0){if(!a.overflowed[1])return;var d="x",u=n.pageX>c.offset().left?-1:1,f=Math.abs(i[0].offsetLeft)-u*(.9*r.width())}else{if(!a.overflowed[0])return;var d="y",u=n.pageY>c.offset().top?-1:1,f=Math.abs(i[0].offsetTop)-u*(.9*r.height())}N(o,f.toString(),{dir:d,scrollEasing:"mcsEaseInOut"})}})},z=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n="mCS_"+o.idx,i=e("#mCSB_"+o.idx+"_container"),r=i.parent();i.bind("focusin."+n,function(o){var n=e(document.activeElement),l=i.find(".mCustomScrollBox").length;n.is(a.advanced.autoScrollOnFocus)&&(j(t),clearTimeout(t[0]._focusTimeout),t[0]._focusTimer=l?17*l:0,t[0]._focusTimeout=setTimeout(function(){var e=[ee(n)[0],ee(n)[1]],o=[i[0].offsetTop,i[0].offsetLeft],l=[o[0]+e[0]>=0&&o[0]+e[0]<r.height()-n.outerHeight(!1),o[1]+e[1]>=0&&o[0]+e[1]<r.width()-n.outerWidth(!1)],s="yx"!==a.axis||l[0]||l[1]?"all":"none";"x"===a.axis||l[0]||N(t,e[0].toString(),{dir:"y",scrollEasing:"mcsEaseInOut",overwrite:s,dur:0}),"y"===a.axis||l[1]||N(t,e[1].toString(),{dir:"x",scrollEasing:"mcsEaseInOut",overwrite:s,dur:0})},t[0]._focusTimer))})},P=function(){var t=e(this),o=t.data("mCS"),a="mCS_"+o.idx,n=e("#mCSB_"+o.idx+"_container").parent();n.bind("scroll."+a,function(t){0===n.scrollTop()&&0===n.scrollLeft()||e(".mCSB_"+o.idx+"_scrollbar").css("visibility","hidden")})},H=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n=o.sequential,i="mCS_"+o.idx,r=".mCSB_"+o.idx+"_scrollbar",s=e(r+">a");s.bind("contextmenu."+i,function(e){e.preventDefault()}).bind("mousedown."+i+" touchstart."+i+" pointerdown."+i+" MSPointerDown."+i+" mouseup."+i+" touchend."+i+" pointerup."+i+" MSPointerUp."+i+" mouseout."+i+" pointerout."+i+" MSPointerOut."+i+" click."+i,function(i){if(i.preventDefault(),K(i)){var r=e(this).attr("class");switch(n.type=a.scrollButtons.scrollType,i.type){case"mousedown":case"touchstart":case"pointerdown":case"MSPointerDown":if("stepped"===n.type)return;l=!0,o.tweenRunning=!1,s("on",r);break;case"mouseup":case"touchend":case"pointerup":case"MSPointerUp":case"mouseout":case"pointerout":case"MSPointerOut":if("stepped"===n.type)return;l=!1,n.dir&&s("off",r);break;case"click":if("stepped"!==n.type||o.tweenRunning)return;s("on",r)}}function s(e,o){n.scrollAmount=a.scrollButtons.scrollAmount,F(t,e,o)}})},U=function(){var t=e(this),o=t.data("mCS"),a=o.opt,n=o.sequential,i="mCS_"+o.idx,r=e("#mCSB_"+o.idx),l=e("#mCSB_"+o.idx+"_container"),s=l.parent(),c="input,textarea,select,datalist,keygen,[contenteditable='true']",d=l.find("iframe"),u=["blur."+i+" keydown."+i+" keyup."+i];function f(i){switch(i.type){case"blur":o.tweenRunning&&n.dir&&h("off",null);break;case"keydown":case"keyup":var r=i.keyCode?i.keyCode:i.which,d="on";if("x"!==a.axis&&(38===r||40===r)||"y"!==a.axis&&(37===r||39===r)){if((38===r||40===r)&&!o.overflowed[0]||(37===r||39===r)&&!o.overflowed[1])return;"keyup"===i.type&&(d="off"),e(document.activeElement).is(c)||(i.preventDefault(),i.stopImmediatePropagation(),h(d,r))}else if(33===r||34===r){if((o.overflowed[0]||o.overflowed[1])&&(i.preventDefault(),i.stopImmediatePropagation()),"keyup"===i.type){j(t);var u=34===r?-1:1;if("x"===a.axis||"yx"===a.axis&&o.overflowed[1]&&!o.overflowed[0])var f="x",m=Math.abs(l[0].offsetLeft)-u*(.9*s.width());else var f="y",m=Math.abs(l[0].offsetTop)-u*(.9*s.height());N(t,m.toString(),{dir:f,scrollEasing:"mcsEaseInOut"})}}else if((35===r||36===r)&&!e(document.activeElement).is(c)&&((o.overflowed[0]||o.overflowed[1])&&(i.preventDefault(),i.stopImmediatePropagation()),"keyup"===i.type)){if("x"===a.axis||"yx"===a.axis&&o.overflowed[1]&&!o.overflowed[0])var f="x",m=35===r?Math.abs(s.width()-l.outerWidth(!1)):0;else var f="y",m=35===r?Math.abs(s.height()-l.outerHeight(!1)):0;N(t,m.toString(),{dir:f,scrollEasing:"mcsEaseInOut"})}}function h(e,i){n.type=a.keyboard.scrollType,n.scrollAmount=a.keyboard.scrollAmount,"stepped"===n.type&&o.tweenRunning||F(t,e,i)}}d.length&&d.each(function(){e(this).bind("load",function(){W(this)&&e(this.contentDocument||this.contentWindow.document).bind(u[0],function(e){f(e)})})}),r.attr("tabindex","0").bind(u[0],function(e){f(e)})},F=function(t,o,a,n,i){var r=t.data("mCS"),l=r.opt,c=r.sequential,d=e("#mCSB_"+r.idx+"_container"),u="stepped"===c.type,f=l.scrollInertia<26?26:l.scrollInertia,m=l.scrollInertia<1?17:l.scrollInertia;switch(o){case"on":if(c.dir=[a===s[16]||a===s[15]||39===a||37===a?"x":"y",a===s[13]||a===s[15]||38===a||37===a?-1:1],j(t),$(a)&&"stepped"===c.type)return;h(u);break;case"off":clearTimeout(c.step),J(c,"step"),j(t),(u||r.tweenRunning&&c.dir)&&h(!0)}function h(e){l.snapAmount&&(c.scrollAmount=l.snapAmount instanceof Array?"x"===c.dir[0]?l.snapAmount[1]:l.snapAmount[0]:l.snapAmount);var o="stepped"!==c.type,a=i||(e?o?f/1.5:m:1e3/60),s=e?o?7.5:40:2.5,u=[Math.abs(d[0].offsetTop),Math.abs(d[0].offsetLeft)],p=[r.scrollRatio.y>10?10:r.scrollRatio.y,r.scrollRatio.x>10?10:r.scrollRatio.x],g="x"===c.dir[0]?u[1]+c.dir[1]*(p[1]*s):u[0]+c.dir[1]*(p[0]*s),v="x"===c.dir[0]?u[1]+c.dir[1]*parseInt(c.scrollAmount):u[0]+c.dir[1]*parseInt(c.scrollAmount),x="auto"!==c.scrollAmount?v:g,S=n||(e?o?"mcsLinearOut":"mcsEaseInOut":"mcsLinear"),_=!!e;e&&a<17&&(x="x"===c.dir[0]?u[1]:u[0]),N(t,x.toString(),{dir:c.dir[0],scrollEasing:S,dur:a,onComplete:_}),e?c.dir=!1:(clearTimeout(c.step),c.step=setTimeout(function(){h()},a))}},q=function(t){var o=e(this).data("mCS").opt,a=[];return"function"==typeof t&&(t=t()),t instanceof Array?a=t.length>1?[t[0],t[1]]:"x"===o.axis?[null,t[0]]:[t[0],null]:(a[0]=t.y?t.y:t.x||"x"===o.axis?null:t,a[1]=t.x?t.x:t.y||"y"===o.axis?null:t),"function"==typeof a[0]&&(a[0]=a[0]()),"function"==typeof a[1]&&(a[1]=a[1]()),a},Y=function(t,o){if(null!=t&&void 0!==t){var a=e(this),n=a.data("mCS"),i=n.opt,r=e("#mCSB_"+n.idx+"_container"),l=r.parent(),s=typeof t;o||(o="x"===i.axis?"x":"y");var d="x"===o?r.outerWidth(!1)-l.width():r.outerHeight(!1)-l.height(),u="x"===o?r[0].offsetLeft:r[0].offsetTop,f="x"===o?"left":"top";switch(s){case"function":return t();case"object":var m=t.jquery?t:e(t);if(!m.length)return;return"x"===o?ee(m)[1]:ee(m)[0];case"string":case"number":if($(t))return Math.abs(t);if(-1!==t.indexOf("%"))return Math.abs(d*parseInt(t)/100);if(-1!==t.indexOf("-="))return Math.abs(u-parseInt(t.split("-=")[1]));if(-1!==t.indexOf("+=")){var h=u+parseInt(t.split("+=")[1]);return h>=0?0:Math.abs(h)}if(-1!==t.indexOf("px")&&$(t.split("px")[0]))return Math.abs(t.split("px")[0]);if("top"===t||"left"===t)return 0;if("bottom"===t)return Math.abs(l.height()-r.outerHeight(!1));if("right"===t)return Math.abs(l.width()-r.outerWidth(!1));if("first"===t||"last"===t){var m=r.find(":"+t);return"x"===o?ee(m)[1]:ee(m)[0]}return e(t).length?"x"===o?ee(e(t))[1]:ee(e(t))[0]:(r.css(f,t),void c.update.call(null,a[0]))}}},X=function(t){var o=e(this),a=o.data("mCS"),n=a.opt,i=e("#mCSB_"+a.idx+"_container");if(t)return clearTimeout(i[0].autoUpdate),void J(i[0],"autoUpdate");function r(e){clearTimeout(i[0].autoUpdate),c.update.call(null,o[0],e)}!function t(){clearTimeout(i[0].autoUpdate),0!==o.parents("html").length?i[0].autoUpdate=setTimeout(function(){return n.advanced.updateOnSelectorChange&&(a.poll.change.n=function(){!0===n.advanced.updateOnSelectorChange&&(n.advanced.updateOnSelectorChange="*");var e=0,t=i.find(n.advanced.updateOnSelectorChange);return n.advanced.updateOnSelectorChange&&t.length>0&&t.each(function(){e+=this.offsetHeight+this.offsetWidth}),e}(),a.poll.change.n!==a.poll.change.o)?(a.poll.change.o=a.poll.change.n,void r(3)):n.advanced.updateOnContentResize&&(a.poll.size.n=o[0].scrollHeight+o[0].scrollWidth+i[0].offsetHeight+o[0].offsetHeight+o[0].offsetWidth,a.poll.size.n!==a.poll.size.o)?(a.poll.size.o=a.poll.size.n,void r(1)):!n.advanced.updateOnImageLoad||"auto"===n.advanced.updateOnImageLoad&&"y"===n.axis||(a.poll.img.n=i.find("img").length,a.poll.img.n===a.poll.img.o)?void((n.advanced.updateOnSelectorChange||n.advanced.updateOnContentResize||n.advanced.updateOnImageLoad)&&t()):(a.poll.img.o=a.poll.img.n,void i.find("img").each(function(){!function(t){if(e(t).hasClass(s[2]))r();else{var o,a,n=new Image;n.onload=(o=n,a=function(){this.onload=null,e(t).addClass(s[2]),r(2)},function(){return a.apply(o,arguments)}),n.src=t.src}}(this)}))},n.advanced.autoUpdateTimeout):o=null}()},j=function(t){var o=t.data("mCS"),a=e("#mCSB_"+o.idx+"_container,#mCSB_"+o.idx+"_container_wrapper,#mCSB_"+o.idx+"_dragger_vertical,#mCSB_"+o.idx+"_dragger_horizontal");a.each(function(){G.call(this)})},N=function(t,o,a){var n=t.data("mCS"),i=n.opt,r={trigger:"internal",dir:"y",scrollEasing:"mcsEaseOut",drag:!1,dur:i.scrollInertia,overwrite:"all",callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},a=e.extend(r,a),l=[a.dur,a.drag?0:a.dur],s=e("#mCSB_"+n.idx),c=e("#mCSB_"+n.idx+"_container"),d=c.parent(),u=i.callbacks.onTotalScrollOffset?q.call(t,i.callbacks.onTotalScrollOffset):[0,0],f=i.callbacks.onTotalScrollBackOffset?q.call(t,i.callbacks.onTotalScrollBackOffset):[0,0];if(n.trigger=a.trigger,0===d.scrollTop()&&0===d.scrollLeft()||(e(".mCSB_"+n.idx+"_scrollbar").css("visibility","visible"),d.scrollTop(0).scrollLeft(0)),"_resetY"!==o||n.contentReset.y||(y("onOverflowYNone")&&i.callbacks.onOverflowYNone.call(t[0]),n.contentReset.y=1),"_resetX"!==o||n.contentReset.x||(y("onOverflowXNone")&&i.callbacks.onOverflowXNone.call(t[0]),n.contentReset.x=1),"_resetY"!==o&&"_resetX"!==o){if(!n.contentReset.y&&t[0].mcs||!n.overflowed[0]||(y("onOverflowY")&&i.callbacks.onOverflowY.call(t[0]),n.contentReset.x=null),!n.contentReset.x&&t[0].mcs||!n.overflowed[1]||(y("onOverflowX")&&i.callbacks.onOverflowX.call(t[0]),n.contentReset.x=null),i.snapAmount){var m=i.snapAmount instanceof Array?"x"===a.dir?i.snapAmount[1]:i.snapAmount[0]:i.snapAmount;o=function(e,t,o){return Math.round(e/t)*t-o}(o,m,i.snapOffset)}switch(a.dir){case"x":var h=e("#mCSB_"+n.idx+"_dragger_horizontal"),p="left",g=c[0].offsetLeft,v=[s.width()-c.outerWidth(!1),h.parent().width()-h.width()],x=[o,0===o?0:o/n.scrollRatio.x],S=u[1],_=f[1],C=S>0?S/n.scrollRatio.x:0,b=_>0?_/n.scrollRatio.x:0;break;case"y":var h=e("#mCSB_"+n.idx+"_dragger_vertical"),p="top",g=c[0].offsetTop,v=[s.height()-c.outerHeight(!1),h.parent().height()-h.height()],x=[o,0===o?0:o/n.scrollRatio.y],S=u[0],_=f[0],C=S>0?S/n.scrollRatio.y:0,b=_>0?_/n.scrollRatio.y:0}x[1]<0||0===x[0]&&0===x[1]?x=[0,0]:x[1]>=v[1]?x=[v[0],v[1]]:x[0]=-x[0],t[0].mcs||(B(),y("onInit")&&i.callbacks.onInit.call(t[0])),clearTimeout(c[0].onCompleteTimeout),V(h[0],p,Math.round(x[1]),l[1],a.scrollEasing),!n.tweenRunning&&(0===g&&x[0]>=0||g===v[0]&&x[0]<=v[0])||V(c[0],p,Math.round(x[0]),l[0],a.scrollEasing,a.overwrite,{onStart:function(){a.callbacks&&a.onStart&&!n.tweenRunning&&(y("onScrollStart")&&(B(),i.callbacks.onScrollStart.call(t[0])),n.tweenRunning=!0,w(h),n.cbOffsets=[i.callbacks.alwaysTriggerOffsets||g>=v[0]+S,i.callbacks.alwaysTriggerOffsets||g<=-_])},onUpdate:function(){a.callbacks&&a.onUpdate&&y("whileScrolling")&&(B(),i.callbacks.whileScrolling.call(t[0]))},onComplete:function(){if(a.callbacks&&a.onComplete){"yx"===i.axis&&clearTimeout(c[0].onCompleteTimeout);var e=c[0].idleTimer||0;c[0].onCompleteTimeout=setTimeout(function(){y("onScroll")&&(B(),i.callbacks.onScroll.call(t[0])),y("onTotalScroll")&&x[1]>=v[1]-C&&n.cbOffsets[0]&&(B(),i.callbacks.onTotalScroll.call(t[0])),y("onTotalScrollBack")&&x[1]<=b&&n.cbOffsets[1]&&(B(),i.callbacks.onTotalScrollBack.call(t[0])),n.tweenRunning=!1,c[0].idleTimer=0,w(h,"hide")},e)}}})}function y(e){return n&&i.callbacks[e]&&"function"==typeof i.callbacks[e]}function B(){var e=[c[0].offsetTop,c[0].offsetLeft],o=[h[0].offsetTop,h[0].offsetLeft],n=[c.outerHeight(!1),c.outerWidth(!1)],i=[s.height(),s.width()];t[0].mcs={content:c,top:e[0],left:e[1],draggerTop:o[0],draggerLeft:o[1],topPct:Math.round(100*Math.abs(e[0])/(Math.abs(n[0])-i[0])),leftPct:Math.round(100*Math.abs(e[1])/(Math.abs(n[1])-i[1])),direction:a.dir}}},V=function(e,t,o,a,n,i,r){e._mTween||(e._mTween={top:{},left:{}});var l,s,r=r||{},c=r.onStart||function(){},d=r.onUpdate||function(){},u=r.onComplete||function(){},f=Q(),m=0,h=e.offsetTop,p=e.style,g=e._mTween[t];"left"===t&&(h=e.offsetLeft);var v=o-h;function x(){g.stop||(m||c.call(),m=Q()-f,S(),m>=g.time&&(g.time=m>g.time?m+l-(m-g.time):m+l-1,g.time<m+1&&(g.time=m+1)),g.time<a?g.id=s(x):u.call())}function S(){a>0?(g.currVal=function(e,t,o,a,n){switch(n){case"linear":case"mcsLinear":return o*e/a+t;case"mcsLinearOut":return e/=a,e--,o*Math.sqrt(1-e*e)+t;case"easeInOutSmooth":return(e/=a/2)<1?o/2*e*e+t:-o/2*(--e*(e-2)-1)+t;case"easeInOutStrong":return(e/=a/2)<1?o/2*Math.pow(2,10*(e-1))+t:(e--,o/2*(2-Math.pow(2,-10*e))+t);case"easeInOut":case"mcsEaseInOut":return(e/=a/2)<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t;case"easeOutSmooth":return e/=a,-o*(--e*e*e*e-1)+t;case"easeOutStrong":return o*(1-Math.pow(2,-10*e/a))+t;case"easeOut":case"mcsEaseOut":default:var i=(e/=a)*e,r=i*e;return t+o*(.499999999999997*r*i+-2.5*i*i+5.5*r+-6.5*i+4*e)}}(g.time,h,v,a,n),p[t]=Math.round(g.currVal)+"px"):p[t]=o+"px",d.call()}g.stop=0,"none"!==i&&null!=g.id&&(window.requestAnimationFrame?window.cancelAnimationFrame(g.id):clearTimeout(g.id),g.id=null),l=1e3/60,g.time=m+l,s=window.requestAnimationFrame?window.requestAnimationFrame:function(e){return S(),setTimeout(e,.01)},g.id=s(x)},Q=function(){return window.performance&&window.performance.now?window.performance.now():window.performance&&window.performance.webkitNow?window.performance.webkitNow():Date.now?Date.now():(new Date).getTime()},G=function(){var e=this;e._mTween||(e._mTween={top:{},left:{}});for(var t=["top","left"],o=0;o<t.length;o++){var a=t[o];e._mTween[a].id&&(window.requestAnimationFrame?window.cancelAnimationFrame(e._mTween[a].id):clearTimeout(e._mTween[a].id),e._mTween[a].id=null,e._mTween[a].stop=1)}},J=function(e,t){try{delete e[t]}catch(o){e[t]=null}},K=function(e){return!(e.which&&1!==e.which)},Z=function(e){var t=e.originalEvent.pointerType;return!(t&&"touch"!==t&&2!==t)},$=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},ee=function(e){var t=e.parents(".mCSB_container");return[e.offset().top-t.offset().top,e.offset().left-t.offset().left]},te=function(){var e=function(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}();return!!e&&document[e]},e.fn[o]=function(t){return c[t]?c[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):c.init.apply(this,arguments)},e[o]=function(t){return c[t]?c[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):c.init.apply(this,arguments)},e[o].defaults=a,window[o]=!0,e(window).bind("load",function(){e(".mCustomScrollbar")[o](),e.extend(e.expr[":"],{mcsInView:e.expr[":"].mcsInView||function(t){var o,a,n=e(t),i=n.parents(".mCSB_container");if(i.length)return o=i.parent(),(a=[i[0].offsetTop,i[0].offsetLeft])[0]+ee(n)[0]>=0&&a[0]+ee(n)[0]<o.height()-n.outerHeight(!1)&&a[1]+ee(n)[1]>=0&&a[1]+ee(n)[1]<o.width()-n.outerWidth(!1)},mcsInSight:e.expr[":"].mcsInSight||function(t,o,a){var n,i,r,l,s=e(t),c=s.parents(".mCSB_container"),d="exact"===a[3]?[[1,0],[1,0]]:[[.9,.1],[.6,.4]];if(c.length)return n=[s.outerHeight(!1),s.outerWidth(!1)],r=[c[0].offsetTop+ee(s)[0],c[0].offsetLeft+ee(s)[1]],i=[c.parent()[0].offsetHeight,c.parent()[0].offsetWidth],r[0]-i[0]*(l=[n[0]<i[0]?d[0]:d[1],n[1]<i[1]?d[0]:d[1]])[0][0]<0&&r[0]+n[0]-i[0]*l[0][1]>=0&&r[1]-i[1]*l[1][0]<0&&r[1]+n[1]-i[1]*l[1][1]>=0},mcsOverflow:e.expr[":"].mcsOverflow||function(t){var o=e(t).data("mCS");if(o)return o.overflowed[0]||o.overflowed[1]}})})});