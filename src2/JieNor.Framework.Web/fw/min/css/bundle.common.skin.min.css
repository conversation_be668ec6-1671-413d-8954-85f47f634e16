@charset "UTF-8";

/*按钮样式*/

.btn-default {
  color: #fff;
  padding: 0 10px;
  border-radius: 4px !important;
  vertical-align: middle;
  background-color: #009FE8;
}

.btn-default:hover {
  background-color: #33B2ED;
}

.btn-active {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
}

.btn-active:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

a {
  color: #009FE8;
}

.p {
  font-size: 12pt;
}

.red-font {
  color: #FF313B !important;
}

.green-font {
  color: #0EC57B !important;
}

.body {
  font-size: 12px;
}

.font-active {
  color: #009FE8 !important;
}

html,
body {
  background: #EBF1F8;
}

html .page-content,
body .page-content {
  background: #EBF1F8;
}

html .page-content > .row > .col-md-12,
body .page-content > .row > .col-md-12 {
  padding-right: 0;
  padding-left: 0px;
}

html .page-content .tab-content .tab-pane,
body .page-content .tab-content .tab-pane {
  background: #fff;
  padding: 10px;
}

html .page-content .tab-content .tab-pane .text-right,
body .page-content .tab-content .tab-pane .text-right {
  text-align: right !important;
}

html .page-content .tab-content .tab-pane .text-left,
body .page-content .tab-content .tab-pane .text-left {
  text-align: left !important;
}

html .page-content .tab-content .tab-pane .text-center,
body .page-content .tab-content .tab-pane .text-center {
  text-align: center !important;
}

html .page-content .tab-content .tab-pane > .row,
body .page-content .tab-content .tab-pane > .row {
  margin: 0;
}

html .page-content .tab-content .tab-pane > .row > .col-md-12,
body .page-content .tab-content .tab-pane > .row > .col-md-12 {
  padding: 0;
}

html .page-content .tab-content .tab-pane.first-content,
body .page-content .tab-content .tab-pane.first-content {
  padding: 0px;
  background: #EBF1F8;
}

html .page-content .tab-content .tab-pane.first-content > .row .col-md-12,
body .page-content .tab-content .tab-pane.first-content > .row .col-md-12 {
  padding-left: 0;
}

html .page-container > .page-content-wrapper > .page-content > .row #tab-content,
body .page-container > .page-content-wrapper > .page-content > .row #tab-content {
  border: 0;
  padding: 10px;
  background: #EBF1F8;
}

.changeStatus {
  background: #009FE8 !important;
}

.yellow-casablanca > .portlet-title > .tools > a.collapse,
.yellow-casablanca > .portlet-title > .tools a.expand {
  background-image: url("../../images/all-icon.png") !important;
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
  float: right;
  margin-top: 5px;
}

.yellow-casablanca > .portlet-title > .tools > a.collapse.collapse,
.yellow-casablanca > .portlet-title > .tools a.expand.collapse {
  background-position: -5px -10px;
}

.yellow-casablanca > .portlet-title > .tools > a.collapse.expand,
.yellow-casablanca > .portlet-title > .tools a.expand.expand {
  background-position: -22px -10px;
}

.yellow-casablanca > .portlet-title > .tools .portlet-title-btn {
  border-radius: 3px !important;
  color: #009FE8;
  font-size: 13px;
  background-color: #fff;
  border: 1px solid #009FE8;
  padding: 5px 14px;
  margin-right: 5px;
  float: right;
}

.yellow-casablanca > .portlet-title > .tools .portlet-title-btn:hover {
  background: #33B2ED;
  border-color: #33B2ED;
  color: #fff;
}

.yellow-casablanca > .portlet-title > .tools > span {
  line-height: 28px;
}

.save-btn {
  font-size: 14px;
  background: #009FE8;
}

.save-btn:hover {
  background: #33B2ED;
}

.save-btn2 {
  font-size: 14px;
  color: #009FE8;
  border: 1px #009FE8 solid !important;
  padding: 0px 15px;
  line-height: 28px;
  outline: none;
}

.save-btn2:hover {
  background: #33B2ED !important;
  border-color: #33B2ED !important;
}

.btn-primary {
  background-color: #009FE8;
  border-color: #009FE8;
}

.btn-primary:hover {
  background-color: #33B2ED;
  border-color: #33B2ED;
}

.icon-upload {
  background-color: #009FE8;
}

.icon-upload:hover {
  background-color: #33B2ED;
}

.portlet .checkbox-list {
  padding-top: 0;
}

/*单选辅助资料*/

/*单选辅助资料*/

.select2-container-active {
  border: 1px solid #009FE8 !important;
  outline: none;
  border-radius: 4px;
  -webkit-box-shadow: #009FE8;
  box-shadow: #009FE8;
}

.select2-container-active.select2-dropdown-open .select2-choice,
.select2-container-active.select2-dropdown-open .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .select2-container-active.select2-dropdown-open button,
.select2-container-active.select2-dropdown-open .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .select2-container-active.select2-dropdown-open button {
  border: 0 !important;
  background-image: none;
}

.select2-drop .select2-search input {
  border: 1px solid #009FE8;
}

.select2-drop .select2-results {
  padding: 0;
}

.select2-drop .select2-results li:hover {
  background: #DDEBFA;
  color: #009FE8;
}

.select2-drop .select2-results li.select2-disabled .select2-result-label {
  cursor: not-allowed;
}

.select2-drop .select2-results .select2-highlighted {
  background: #DDEBFA;
  color: #009FE8;
}

.select2-container {
  border: 1px solid #D6DEE9 !important;
  border-radius: 4px !important;
}

.select2-container .select2-choice,
.select2-container .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .select2-container button,
.select2-container .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .select2-container button {
  border: 0 !important;
  border-radius: 4px !important;
}

.select2-container .select2-choice abbr,
.select2-container .tab-pane .page-search-wrrap .page-search-pane button abbr,
.tab-pane .page-search-wrrap .page-search-pane .select2-container button abbr,
.select2-container .layui-layer-content .page-search-wrrap .page-search-pane button abbr,
.layui-layer-content .page-search-wrrap .page-search-pane .select2-container button abbr {
  background: url("../../images/select-renove-icon.png") right top no-repeat;
}

.select2-container .select2-container-disabled {
  border: 0;
}

.select2-container-active {
  border: 1px solid #009FE8 !important;
}

.select2-drop-active {
  border: 0 !important;
  margin-top: 0px;
  border-radius: 4px !important;
  box-shadow: 0px 0px 9px 0px rgba(110, 132, 156, 0.3) !important;
}

.select2-drop-active .select2-search {
  padding: 12px 8px 8px 8px;
}

.select2-drop-active .select2-search input {
  border: 1px solid #d6dee9;
  border-radius: 4px !important;
}

.select2-drop-active .select2-results {
  padding: 0;
}

.select2-drop-active .select2-results .select2-result-label {
  min-height: 30px;
  color: #333;
  line-height: 25px;
}

.select2-drop-active .select2-results .select2-highlighted {
  color: #009FE8;
  background: #DDEBFA;
}

.form-control.input-sm .select2-choice,
.form-control.input-sm .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .form-control.input-sm button,
.form-control.input-sm .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .form-control.input-sm button {
  padding: 3px 10px 3px 10px;
}

/*多选辅助资料*/

.select2-container-multi {
  border: 1px #D6DEE9 solid;
}

.select2-container-multi .select2-choices {
  background: none;
  border: 0 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
  background: #DDEBFA;
  color: #009FE8;
  border: 1px solid #009FE8;
}

.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -4px -26px !important;
}

.select2-container-multi.select2-container-active {
  border: 0 !important;
}

.select2-drop-active {
  border-color: #009FE8;
}

/*附件*/

.layui-layer .y-nav-attach .form .form-body {
  border: 2px dashed #E3E3E3;
  border-radius: 4px !important;
  min-height: 100px;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene {
  border: none;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene li {
  float: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-up.add {
  height: 80px;
  background: #F1F4F6;
  border: 1px solid #E3E3E3;
  border-radius: 4px !important;
  margin-bottom: 10px;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-up.add .webuploader-pick {
  width: 80px;
  height: 80px;
  background-size: 80px 80px;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic {
  border-bottom: 1px solid #E3E3E3;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .imgWrap img {
  width: auto;
  height: auto;
  max-width: 16px;
  max-height: 18px;
  display: inline-block;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic a.title {
  font-size: 14px;
  width: auto;
  height: 25px;
  line-height: 25px;
  vertical-align: middle;
  position: relative;
  background-color: #fff;
  color: #000;
  display: inline-block !important;
  padding-left: 10px;
  text-decoration: none;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .w-panel {
  display: block !important;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .w-panel .cancel {
  width: 10px;
  height: 10px;
  background: url("../../images/attach-delete-icon.png") no-repeat;
  background-size: contain;
  opacity: 1;
  top: 15px;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .progress-bg {
  background: #E3E3E3;
  border-radius: 2px !important;
  width: 90%;
  height: 4px;
  float: left;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .progress-bg .progress {
  height: 4px;
  position: absolute;
  background: #0EC57B;
  border-radius: 2px !important;
}

.layui-layer .y-nav-attach .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .progress-percent {
  color: #0EC57B;
}

.layui-layer .layui-layer-btn a {
  opacity: 1;
}

.layui-layer .layui-layer-btn a.layui-layer-btn0 {
  color: #fff;
  background: #009FE8 !important;
  border: 1px solid #009FE8 !important;
}

.layui-layer .layui-layer-btn a.layui-layer-btn0:hover {
  background-color: #33B2ED;
}

.layui-layer .layui-layer-title {
  background-color: #009FE8 !important;
}

.layui-layer .layui-layer-content .form .form-body .uploader-start .uploader-style .queueList .w-quene .w-pic .imgWrap img {
  width: auto;
  height: auto;
  max-width: 45px;
  max-height: 45px;
  display: inline-block;
}

form .form-group .uploader-start {
  margin-right: 10px;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene {
  border: none;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li {
  margin-right: 10px;
  margin-bottom: 10px;
  width: 80px;
  height: 80px;
  /* 圆底 */
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle {
  position: relative;
  transform: rotate(90deg);
  background-color: #0EC57B;
  left: 14px;
  top: 18px;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle .top {
  background-color: white;
  clip: rect(0px, auto, 24px, auto);
  transform: rotate(0deg);
  background-color: #ccc;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle .bottom {
  /* 下半部分 */
  background-color: white;
  clip: rect(24px, auto, auto, auto);
  background-color: #ccc;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle .center {
  /* 中心白 */
  width: 40px;
  height: 40px;
  line-height: 40px;
  left: 4px;
  top: 4px;
  text-align: center;
  transform: rotate(-90deg);
  border-radius: 50%;
  background-color: #FFFFFF;
  font-size: 10px;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle,
form .form-group .uploader-start .uploader-style .queueList .w-quene li .circle div {
  position: absolute;
  width: 48px;
  height: 48px;
  border-radius: 50% !important;
  color: #0EC57B;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene .w-up.add .webuploader-pick {
  width: 80px;
  height: 80px;
  background-size: 80px 80px;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene .w-pic .imgWrap img {
  width: auto;
  height: auto;
  max-width: 80px;
  max-height: 80px;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene .w-pic .circle-progress {
  position: absolute !important;
  top: 0px;
  left: 0px;
  display: block;
  width: 40px;
  height: 40px;
  margin: 16px;
  color: #0EC57B;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene .w-pic a.cancel {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -230px -137px !important;
  width: 22px;
  height: 22px;
  background-size: contain;
  top: -1px;
  right: -5px;
  opacity: 1;
}

form .form-group .uploader-start .uploader-style .queueList .w-quene .w-pic a.down {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -205px -137px !important;
  width: 22px;
  height: 22px;
  background-size: contain;
  top: -1px;
  left: 0px;
  opacity: 1;
}

.layui-layer-setwin .layui-layer-min {
  display: none !important;
}

.layui-layer-setwin .layui-layer-max:hover {
  background-position: -32px -40px !important;
  opacity: .7 !important;
}

.layui-layer-setwin .layui-layer-maxmin:hover {
  background-position: -65px -40px !important;
  opacity: .7 !important;
}

.uploader-show {
  border: none;
}

.uploader-show .img-show li {
  margin-right: 10px;
  margin-bottom: 10px;
}

/*单选基础资料*/

.bd-select-table .clearfix,
.dyncharparse .clearfix {
  background: #ecf0f2;
  border-top: 1px solid #E3E3E3;
}

.bd-select-table .clearfix .form-control,
.dyncharparse .clearfix .form-control {
  border-radius: 0px;
}

.bd-select-table .clearfix .form-control .btn-primary,
.dyncharparse .clearfix .form-control .btn-primary {
  background-color: #009FE8;
  color: #ffffff;
}

.bd-select-table .clearfix .pull-left select.form-control,
.dyncharparse .clearfix .pull-left select.form-control {
  width: 100px;
}

.bd-select-table .clearfix .pull-right select.form-control,
.dyncharparse .clearfix .pull-right select.form-control {
  width: 50px;
}

.bd-select-table .ui-widget,
.dyncharparse .ui-widget {
  border: 0;
}

.bd-select-table .ui-widget .slick-header-columns,
.dyncharparse .ui-widget .slick-header-columns {
  height: 32px;
}

.bd-select-table .ui-widget .slick-header-columns .slick-header-column,
.dyncharparse .ui-widget .slick-header-columns .slick-header-column {
  height: 32px;
  line-height: 32px;
}

.bd-select-table .ui-widget .slick-header-columns .slick-header-column:last-child,
.dyncharparse .ui-widget .slick-header-columns .slick-header-column:last-child {
  border: none;
}

.bd-select-table .ui-widget .slick-header-columns .slick-header-column .slick-column-name,
.dyncharparse .ui-widget .slick-header-columns .slick-header-column .slick-column-name {
  line-height: 32px;
  display: inline-block;
  margin-top: -4px;
}

.bd-select-table .ui-widget .slick-cell,
.dyncharparse .ui-widget .slick-cell {
  line-height: inherit !important;
  margin-left: -1px;
}

.bd-select-table .ui-widget .slick-row,
.dyncharparse .ui-widget .slick-row {
  line-height: 30px;
}

.dyncharparse {
  border: 1px #E3E3E3 solid;
}

/*多选基础资料*/

ul.tablewmul {
  display: flex;
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

ul.wmul {
  cursor: text;
  max-height: 70px;
  border-color: #D6DEE9;
}

ul.wmul .clear-all {
  display: inline-block;
  float: right;
  right: 5px;
  margin-top: 8px;
}

ul.wmul .clear-all:active {
  text-decoration: none;
}

ul.wmul .bdselectmult-search-choice {
  color: #009FE8;
  border: 1px solid #009FE8;
}

ul.wmul .bdselectmult-search-choice a {
  opacity: 0.9;
  color: #009FE8;
  top: 4px;
  text-decoration: none !important;
  filter: alpha(opacity=90);
}

ul.wmul .bdselectmult-search-choice a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}

ul.wmul .bdselectmult-search-field {
  padding: 0;
  margin: 0;
  background-color: #fff;
  border: none;
}

ul.wmul .bdselectmult-search-field .bdselectmult-input {
  width: 80px;
  height: 32px;
  padding: 5px;
  margin: 1px 0;
  font-family: sans-serif;
  font-size: 100%;
  color: #666;
  outline: 0;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent !important;
}

.selectmulti-confirm-panel {
  height: 24px;
  line-height: 20px;
  padding: 2px 10px;
}

.selectmulti-confirm-panel .selectmulti-confirm {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
  height: 24px;
}

.selectmulti-confirm-panel .selectmulti-confirm:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

.input-group-selectbtn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: #fff !important;
  padding: 6px 15px !important;
}

/*按钮样式*/

.btn-default {
  color: #fff;
  padding: 0 10px;
  border-radius: 4px !important;
  vertical-align: middle;
  background-color: #009FE8;
}

.btn-default:hover {
  background-color: #33B2ED;
}

.btn-active {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
}

.btn-active:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

/*地区选择*/

.city-picker-dropdown .city-select-wrap {
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
}

.city-picker-dropdown .city-select-tab {
  background: #F2F6FA;
  border-radius: 4px 4px 0px 0px;
  border-bottom: 1px solid #D6DEE9;
}

.city-picker-dropdown .city-select-tab a {
  background: #F2F6FA;
  color: #333333;
  border-left: none;
  border-bottom: 1px solid #D6DEE9;
}

.city-picker-dropdown .city-select-tab a.active {
  background: #fff;
  border-left: 1px solid #D6DEE9;
  border-right: 1px solid #D6DEE9;
  border-bottom: 1px solid #fff;
}

.city-picker-dropdown .city-select-content .city-select a {
  margin-bottom: 2px;
}

.city-picker-dropdown .city-select-content .city-select a:hover {
  color: #009FE8;
  background: #F1F8FF;
}

.city-picker-dropdown .city-select-content .city-select a.active {
  background: #009FE8;
}

.city-picker-dropdown .city-select-content .city-select a.active:hover {
  color: #ffffff;
  background: #009FE8;
  opacity: 0.9;
}

.housetype-picker-dropdown .housetype-select-wrap {
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
}

.housetype-picker-dropdown .housetype-select-tab {
  background: #F2F6FA;
  border-radius: 4px 4px 0px 0px;
  border-bottom: 1px solid #D6DEE9;
}

.housetype-picker-dropdown .housetype-select-tab a {
  background: #F2F6FA;
  color: #333333;
  border-left: none;
  border-bottom: 1px solid #D6DEE9;
}

.housetype-picker-dropdown .housetype-select-tab a.active {
  background: #fff;
  border-left: 1px solid #D6DEE9;
  border-right: 1px solid #D6DEE9;
  border-bottom: 1px solid #fff;
}

.housetype-picker-dropdown .housetype-select-content .housetype-select a {
  margin-bottom: 2px;
}

.housetype-picker-dropdown .housetype-select-content .housetype-select a:hover {
  color: #009FE8;
  background: #F1F8FF;
}

.housetype-picker-dropdown .housetype-select-content .housetype-select a.active {
  background: #009FE8;
}

.housetype-picker-dropdown .housetype-select-content .housetype-select a.active:hover {
  color: #ffffff;
  background: #009FE8;
  opacity: 0.9;
}

.timepicker .timedropdown .time-select-wrap {
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
}

.timepicker .timedropdown .time-select-tab {
  background: #F2F6FA;
  border-radius: 4px 4px 0px 0px;
  border-bottom: 1px solid #D6DEE9;
}

.timepicker .timedropdown .time-select-tab a {
  background: #F2F6FA;
  color: #333333;
  border-left: none;
  border-bottom: 1px solid #D6DEE9;
}

.timepicker .timedropdown .time-select-tab a.active {
  background: #fff;
  border-left: 1px solid #D6DEE9;
  border-right: 1px solid #D6DEE9;
  border-bottom: 1px solid #fff;
}

.timepicker .timedropdown .items div a {
  margin-bottom: 2px;
}

.timepicker .timedropdown .items div a:hover {
  color: #009FE8;
  background: #F1F8FF;
}

.timepicker .timedropdown .items div a.active {
  background: #009FE8;
}

.timepicker .timedropdown .items div a.active:hover {
  color: #ffffff;
  background: #009FE8;
  opacity: 0.9;
}

/*日期选择控件*/

.datepicker table .active {
  background-color: #009FE8 !important;
  border: none !important;
}

.datepicker table .active:hover {
  background: #33B2ED !important;
  border: none !important;
}

.datepicker table tr td.today,
.datepicker table tr td.today.disabled,
.datepicker table tr th.today,
.datepicker table tr th.today.disabled {
  color: #009FE8;
  border-radius: 4px !important;
  border: 1px solid #009FE8;
  background: #fff;
}

.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr th.today:hover,
.datepicker table tr th.today.disabled:hover {
  border: 1px solid #009FE8 !important;
  background: #33B2ED !important;
  color: #fff !important;
}

.datepicker table tr td.today.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr th.today.active,
.datepicker table tr th.today.disabled.active {
  border: none !important;
  color: #fff;
  background: #009FE8 !important;
  text-shadow: none;
}

.datepicker table tr td.today.active:hover,
.datepicker table tr td.today.disabled.active:hover,
.datepicker table tr th.today.active:hover,
.datepicker table tr th.today.disabled.active:hover {
  border: none !important;
}

.datepicker table tr td,
.datepicker table tr td.disabled,
.datepicker table tr th,
.datepicker table tr th.disabled {
  border-radius: 4px !important;
  font-family: "微软雅黑" !important;
}

.datepicker table tr td[colspan="7"]:hover {
  color: #333333;
  background: #fff !important;
}

.datepicker table tr td[colspan="7"] span {
  text-shadow: none;
}

.datepicker table tfoot tr th.today,
.datepicker table tfoot tr th.today.disabled {
  background: #009FE8 !important;
  border: none !important;
  color: #fff !important;
}

.datepicker table tfoot .clear,
.datepicker .datetimepicker table tfoot .clear:hover,
.datetimepicker .datepicker table tfoot .clear:hover {
  background: #fff;
  color: #333333;
  border: 1px solid #cdd9e6;
  border-radius: 4px !important;
}

.datepicker table tfoot .clear:hover {
  border: 1px solid #cdd9e6;
  color: #333333;
  background: #f1f1f1 !important;
}

.datepicker table .ui-row-1tr.ui-state-highlight.ui-state-hover td {
  color: #009FE8;
}

.datepicker tfoot .clear:hover,
.datepicker .datetimepicker table tfoot .clear:hover,
.datetimepicker table .datepicker tfoot .clear:hover {
  background: #fff !important;
}

.daterangepicker .drp-buttons .cancelBtn {
  border-radius: 0 !important;
  border: 0;
}

/*日期选择控件*/

.datetimepicker table .active {
  color: #fff !important;
  background-color: #009FE8 !important;
  border-radius: 4px !important;
  text-shadow: none;
}

.datetimepicker table .active:hover {
  color: #fff !important;
  background-color: #33B2ED !important;
  border-radius: 4px !important;
  text-shadow: none !important;
}

.datetimepicker table tr td.today,
.datetimepicker table tr th.today {
  color: #009FE8;
  border-radius: 4px !important;
  border: 1px solid #009FE8 !important;
  background: #fff;
}

.datetimepicker table tr td.today:hover,
.datetimepicker table tr th.today:hover {
  background-image: none;
}

.datetimepicker table tr td.today.active {
  border: none !important;
  color: #fff;
}

.datetimepicker table tr td.today.active:hover {
  color: #ffffff !important;
  background: #33B2ED !important;
  border: none !important;
}

.datetimepicker table tr td.today.active th.today.active,
.datetimepicker table tr td.today.active th.today.disabled.active {
  border: none !important;
  color: #fff;
}

.datetimepicker table tr td.day:hover {
  color: #009FE8;
  border-radius: 4px !important;
  background: #e8f8ff;
  border: none;
}

.datetimepicker table tr td.day.disabled:hover {
  color: #999999;
  background: none !important;
}

.datetimepicker table tr span.month,
.datetimepicker table tr span.year,
.datetimepicker table tr span.year,
.datetimepicker table tr span.month {
  font-size: 13px;
}

.datetimepicker table tr span.month:hover,
.datetimepicker table tr span.year:hover,
.datetimepicker table tr span.year:hover,
.datetimepicker table tr span.month:hover {
  background: #e8f8ff;
}

.datetimepicker table tr span.month.active:hover,
.datetimepicker table tr span.year.active:hover,
.datetimepicker table tr span.year.active:hover,
.datetimepicker table tr span.month.active:hover {
  color: #fff;
  background: #33B2ED;
}

.datetimepicker table tr span.month.disabled:hover,
.datetimepicker table tr span.year.disabled:hover,
.datetimepicker table tr span.year.disabled:hover,
.datetimepicker table tr span.month.disabled:hover {
  color: #999999;
  background: none;
}

.datetimepicker table tfoot tr th.today {
  background: #009FE8 !important;
  border: none !important;
  color: #fff !important;
}

.datetimepicker table tfoot tr th.today:hover {
  background: #009FE8 !important;
  border: none !important;
  color: #fff !important;
  opacity: 0.9;
}

.datetimepicker table tfoot .clear,
.datetimepicker table tfoot .clear:hover {
  background: #fff;
  color: #666666;
  border: 1px solid #cdd9e6;
  border-radius: 4px !important;
}

.datetimepicker table .ui-row-1tr.ui-state-highlight.ui-state-hover td {
  color: #009FE8;
}

.datetimepicker .datetimepicker-hours td:hover,
.datetimepicker .datetimepicker-hours td.disabled:hover,
.datetimepicker .datetimepicker-hours th:hover,
.datetimepicker .datetimepicker-hours th.disabled:hover,
.datetimepicker .datetimepicker-minutes td:hover,
.datetimepicker .datetimepicker-minutes td.disabled:hover,
.datetimepicker .datetimepicker-minutes th:hover,
.datetimepicker .datetimepicker-minutes th.disabled:hover {
  border-radius: 4px !important;
  background: #fff !important;
  border: none;
  color: #000;
}

.datetimepicker .datetimepicker-hours td .hour:hover,
.datetimepicker .datetimepicker-hours td .minute:hover,
.datetimepicker .datetimepicker-hours td.disabled .hour:hover,
.datetimepicker .datetimepicker-hours td.disabled .minute:hover,
.datetimepicker .datetimepicker-hours th .hour:hover,
.datetimepicker .datetimepicker-hours th .minute:hover,
.datetimepicker .datetimepicker-hours th.disabled .hour:hover,
.datetimepicker .datetimepicker-hours th.disabled .minute:hover,
.datetimepicker .datetimepicker-minutes td .hour:hover,
.datetimepicker .datetimepicker-minutes td .minute:hover,
.datetimepicker .datetimepicker-minutes td.disabled .hour:hover,
.datetimepicker .datetimepicker-minutes td.disabled .minute:hover,
.datetimepicker .datetimepicker-minutes th .hour:hover,
.datetimepicker .datetimepicker-minutes th .minute:hover,
.datetimepicker .datetimepicker-minutes th.disabled .hour:hover,
.datetimepicker .datetimepicker-minutes th.disabled .minute:hover {
  color: #009FE8;
  border-radius: 4px !important;
  background: #e8f8ff !important;
  border: none;
}

.datetimepicker .datetimepicker-hours td .hour.active:hover,
.datetimepicker .datetimepicker-hours td .minute.active:hover,
.datetimepicker .datetimepicker-hours td.disabled .hour.active:hover,
.datetimepicker .datetimepicker-hours td.disabled .minute.active:hover,
.datetimepicker .datetimepicker-hours th .hour.active:hover,
.datetimepicker .datetimepicker-hours th .minute.active:hover,
.datetimepicker .datetimepicker-hours th.disabled .hour.active:hover,
.datetimepicker .datetimepicker-hours th.disabled .minute.active:hover,
.datetimepicker .datetimepicker-minutes td .hour.active:hover,
.datetimepicker .datetimepicker-minutes td .minute.active:hover,
.datetimepicker .datetimepicker-minutes td.disabled .hour.active:hover,
.datetimepicker .datetimepicker-minutes td.disabled .minute.active:hover,
.datetimepicker .datetimepicker-minutes th .hour.active:hover,
.datetimepicker .datetimepicker-minutes th .minute.active:hover,
.datetimepicker .datetimepicker-minutes th.disabled .hour.active:hover,
.datetimepicker .datetimepicker-minutes th.disabled .minute.active:hover {
  color: #fff;
  border-radius: 4px !important;
  background: #009FE8 !important;
  border: none !important;
}

#hourpick > ul li:hover,
#minutepick > ul li:hover {
  background: #009FE8 !important;
  color: #fff;
}

/*百度编辑器*/

.edui-default .edui-editor-toolbarboxouter {
  background-image: none;
  background-color: #F0F0F0;
}

/*统一控件样式*/

.form-control:focus {
  outline: none;
  border: 1px solid #009FE8 !important;
  box-shadow: none !important;
}

.form-control:focus:disabled {
  border: 1px #D6DEE9 solid;
}

.input-group .input-group-addon {
  border: 1px solid #D6DEE9 !important;
  border-left: 0 !important;
  background: #fff;
  opacity: 1;
}

.input-group .input-group-addon > i {
  font-size: 12px;
  line-height: 15px;
  color: #4C637B !important;
}

.input-group .input-group-addon:hover i {
  color: #009FE8 !important;
}

.input-group .input-group-addon:hover {
  color: #009FE8;
  background: none;
}

.input-group .input-group-addon.disabled {
  cursor: not-allowed;
  background: #f4f4f4;
}

.input-group .input-group-addon.disabled:hover {
  background: #f4f4f4;
}

.input-group .input-group-addon.disabled:hover i {
  color: #4C637B !important;
}

.input-group .input-group-addon.default-bg {
  background: #fff !important;
  color: #4C637B !important;
}

.input-group .input-group-addon.default-bg:hover {
  color: #009FE8 !important;
}

.input-group input,
.input-group .select2-container {
  border-radius: 4px 0 0 4px !important;
}

.housetype-border-bottom .col-xs-12,
.city-border-bottom .col-xs-12,
.timepicker.col-xs-12 {
  border: 1px solid #D6DEE9;
  border-radius: 4px 0 0 4px !important;
}

.summary_list {
  color: #009FE8;
}

.timepicker {
  border: 1px solid #D6DEE9;
}

.tab_title {
  min-height: 35px;
  padding: 0px 0 10px 19px;
  position: fixed;
  z-index: 101;
}

.tab_title .droplist_box {
  padding: 0;
}

.tab_title .droplist_box #tbSave {
  background: #009FE8 !important;
}

.tab_title .droplist_box #tbSave:hover {
  background: #33B2ED !important;
}

.operat-page-menu-list {
  min-height: 40px;
  display: none;
  position: relative;
  background: #fff;
  z-index: 20;
  padding: 0px 0px 5px 0;
  width: 100%;
  float: right;
}

.operat-page-menu-list .cancel-select-operat {
  position: absolute;
  cursor: pointer;
  width: 13px;
  height: 13px;
  background: url("../../images/attach-delete-icon.png") no-repeat;
  background-size: contain;
  top: 15px;
  left: 10px;
}

.page-menu-list div.droplist2_box .more_btns,
.page-menu-list div.droplist_box .droplist_top_box,
.operat-page-menu-list div.droplist2_box .more_btns,
.operat-page-menu-list div.droplist_box .droplist_top_box {
  top: 29px !important;
  padding: 0;
}

.page-menu-list div.droplist2_box .more_btns button,
.page-menu-list div.droplist_box .droplist_top_box button,
.operat-page-menu-list div.droplist2_box .more_btns button,
.operat-page-menu-list div.droplist_box .droplist_top_box button {
  height: 30px;
  min-height: 30px;
  color: #333333 !important;
  font-size: 12px;
}

.page-menu-list div.droplist2_box .more_btns button:hover,
.page-menu-list div.droplist_box .droplist_top_box button:hover,
.operat-page-menu-list div.droplist2_box .more_btns button:hover,
.operat-page-menu-list div.droplist_box .droplist_top_box button:hover {
  background: #eeeeee !important;
}

.page-menu-list div.droplist2_box .more_btns button:active,
.page-menu-list div.droplist_box .droplist_top_box button:active,
.operat-page-menu-list div.droplist2_box .more_btns button:active,
.operat-page-menu-list div.droplist_box .droplist_top_box button:active {
  border: 0 !important;
}

.page-menu-list.list-menu-btnstyle button#openSearch {
  background-position: 7px 5px !important;
}

.page-menu-list.list-menu-btnstyle button#openSearch.openBtnClcik {
  background-color: #009FE8 !important;
}

.page-menu-list.list-menu-btnstyle button#tbarrlist {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -176px -134px !important;
}

.page-menu-list.list-menu-btnstyle button#tbarrlist:hover {
  background-color: #eee !important;
}

.page-menu-list.list-menu-btnstyle button#tbrefresh {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -180px -111px !important;
}

.page-menu-list.list-menu-btnstyle button#tbrefresh:hover {
  background-color: #eee !important;
}

.droplist_box {
  min-height: 30px;
  min-width: 30px;
  float: right;
  position: inherit;
  position: relative;
  margin-left: 10px;
  margin-top: 0 !important;
  margin-right: 0px;
  border-radius: 4px !important;
  border: 1px solid #D6DEE9 !important;
  color: #4C637B;
  background: #ffffff;
}

.droplist_box > button {
  min-width: 28px;
  min-height: 28px;
  height: 28px;
  color: #16325C;
  background: #fff !important;
  border-radius: 4px !important;
  padding: 2px 6px 5px 6px !important;
  float: left;
}

.droplist_box .droplist_top_box {
  top: 30px;
  left: -1px;
  border: 1px #d6dee9 solid;
  min-width: 80px !important;
  box-shadow: none;
}

.droplist_box i {
  height: 18px;
  margin-top: 7px;
}

.droplist_box:hover {
  background: #eeeeee;
  border-color: #009FE8 !important;
}

.droplist_box:hover > button {
  background: #eeeeee !important;
}

.droplist_box[menu=save] {
  border-color: #009FE8 !important;
  background: #009FE8 !important;
}

.droplist_box[menu=save]:hover {
  background: #33B2ED !important;
  border-color: #33B2ED !important;
}

.droplist2_box {
  height: 30px;
  margin-top: 0 !important;
  margin-right: 0 !important;
  margin-left: 10px;
  border-radius: 4px !important;
  border: 1px solid #D6DEE9 !important;
  background: #fff;
}

.droplist2_box .droplist_more {
  width: 28px;
  height: 28px;
  line-height: 28px !important;
  background-position: 7px 5px !important;
  background-size: 17px 18px !important;
  border-radius: 4px !important;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -9px -367px !important;
}

.droplist2_box:hover {
  border-color: #009FE8 !important;
}

.droplist2_box:hover .more_btns {
  display: block;
}

.droplist2_box:hover .droplist_more {
  background-color: #eee !important;
}

.droplist2_box .more_btns {
  box-shadow: none;
  border: 1px solid #D6DEE9;
  min-width: 80px !important;
  right: -1px;
  z-index: 101;
}

.list-quick-search {
  /*> button{
        min-width: 28px;
        min-height: 28px;
        height: 28px;
        color: #16325C;
        background: $white-color !important;
        border-radius: $default-radius;
        padding: 2px 6px 5px 6px !important;
        border:1px solid #D6DEE9;
        &:hover{
            color:#16325C;
            background: #eeeeee !important;
            border-color:#33B2ED;
        }
        &:active,&:focus,&:visited{
            color: #16325C;
            background: $white-color !important;
            border:1px solid #D6DEE9;
        }
    }
    > button[menu=confirm]{
        color: $white-color !important;
        border-color: $def-color !important;
        background: $def-color !important;
        &:hover{
            background: $defhover-color !important;
            border-color: $defhover-color !important;
        }
    }*/
}

.list-quick-search > button {
  min-width: 28px;
  min-height: 28px;
  height: 28px;
  color: #fff !important;
  background: #009FE8 !important;
  border-radius: 4px !important;
  padding: 2px 6px 5px 6px !important;
  border: 1px solid #D6DEE9;
  border-color: #009FE8 !important;
  margin-left: 10px;
}

.list-quick-search > button:hover {
  background: #33B2ED !important;
  border-color: #33B2ED !important;
}

#quick-search {
  width: 200px;
  height: 30px;
  margin-right: 10px;
  margin-left: 0px;
  border: 1px solid #D6DEE9;
}

@media (max-width: 991px) {
  .list-menu-btnstyle {
    margin-top: 0;
  }
}

.alp-search {
  padding-top: 0;
  padding-bottom: 5px;
}

.alp-search a.btn-primary {
  margin-left: 0;
  margin-right: 5px;
}

.alp-search a.btn-primary:hover {
  background: #33B2ED;
}

.alp-search a.btn-primary:active {
  background: #33B2ED;
}

.page-menu-list-fixed {
  width: 100%;
}

@media only screen and (max-height: 800px) {
  .page-menu-list div.droplist2_box .more_btns,
  .operat-page-menu-list div.droplist2_box .more_btns {
    max-height: 60vh;
    overflow-y: scroll;
  }
}

/*滚动条样式*/

.ztree .mCSB_scrollTools .mCSB_draggerRail,
.menu-panel .mCSB_scrollTools .mCSB_draggerRail {
  width: 0px;
}

.barcodefield {
  width: auto !important;
  font-size: 14px;
  text-align: center;
  display: inline-block;
  border: solid 1px #E2E2E2;
  padding: 8px 8px 4px 8px !important;
}

.barcodefield-list {
  padding: 6px 0px !important;
  text-align: center;
  height: auto !important;
}

.bizperson {
  padding: 5px 0 10px 15px;
}

.bizperson .bizform-wrap {
  float: left;
  width: 200px;
}

.bizperson .bizform-list {
  float: left;
  width: 100%;
  margin-bottom: 15px;
}

.bizperson .bizform-list label {
  padding: 5px 0;
}

.bizperson .bizform-list label.selected {
  color: red;
}

.bizperson .bizform-list label div {
  float: left;
}

.bizperson .bizform-list label div.text {
  width: 180px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 3px;
}

.bizperson .person-wrap {
  float: left;
  width: 630px;
}

.bizperson .person-wrap .person-wrap2 {
  display: none;
}

.bizperson .person-wrap .person-wrap2.selected {
  display: inline-block;
}

.bizperson .person-list {
  float: left;
  width: 315px;
  margin-bottom: 15px;
}

.bizperson .person-list label {
  padding: 5px 0;
}

.bizperson .person-list label div {
  float: left;
}

.bizperson .person-list label div.text {
  width: 260px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 3px;
}

.bizperson .person-list > label.checkbox-inline > .checker {
  margin-top: -1px !important;
}

.bizperson .person-list .checkbox-inline + .checkbox-inline {
  margin-left: 0px;
}

.bizperson .checkbox-list label div {
  float: left;
}

.bizperson .checkbox-list label div.text {
  width: 260px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 3px;
}

.dropdownfield {
  width: 400px;
  min-height: 300px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 5px 15px;
  border: solid 1px #cecece;
  background-color: #fff;
  display: none;
  position: absolute;
  z-index: 999999999;
}

.dropdownfield dl {
  margin: 0;
  padding: 0;
}

.dropdownfield dl dd {
  height: 100%;
  overflow: hidden;
  padding-top: 10px;
  margin: 0;
}

.dropdownfield dl dd a {
  display: inline-block;
  float: left;
  padding: 3px 8px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 13px;
  margin-bottom: 8px;
  margin-right: 8px;
  color: #6D6D6D;
  cursor: pointer;
}

.dropdownfield dl dd a:hover {
  background-color: #f1f8ff;
  color: #46a4ff;
  text-decoration: none;
}

.dropdownfield dl dd a.active {
  background-color: #46a4ff;
  color: #fff;
}

.dropdownfield dl dd a.disabled {
  color: #a7a7a7;
}

.dropdownfield dl dd a.disabled-sel {
  background-color: #d5d5d5;
  color: #a7a7a7;
}

.dropdownfield dt {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  font-weight: normal;
  border-bottom: solid 2px #00AAEF;
  color: #6b8299;
}

.dynbdselect {
  padding: 15px 0 20px 25px;
}

.dynbdselect .alert {
  margin-top: 20px;
}

.dynbdselect .radio-list {
  height: 100%;
  overflow: hidden;
  padding-top: 10px;
}

.dynbdselect .radio-list label {
  float: left;
  width: 142px;
  padding: 10px 0;
}

.dynbdselect .radio-list label div {
  float: left;
}

.dynbdselect .radio-list label div.text {
  width: 110px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 3px;
}

.dynbdselect .radio-list > label.radio-inline:first-child {
  padding-left: 0px;
}

.dynbdselect .radio-list > label.radio-inline > .radio {
  margin-top: 2px;
}

.dynbdselect .radio-list > label.checkbox-inline > .checker {
  margin-top: -1px !important;
}

.dynbdselect .radio-list .checkbox-inline .checkbox,
.dynbdselect .radio-list .radio-inline .radio {
  width: 16px;
  height: 16px;
  margin-top: 0px;
  margin-bottom: 0px;
}

.dynbdselect .radio-list .checkbox-inline .checkbox input,
.dynbdselect .radio-list .radio-inline .radio input {
  margin: 0px;
  opacity: 1;
  height: 19px;
}

.dynbdselect .radio-list .checkbox-inline + .checkbox-inline {
  margin-left: 0px;
}

.dynbdselect .radio-list .radio-inline + .radio-inline {
  margin-left: 0px;
}

.dynbdselect .checkbox-list label div {
  float: left;
}

.dynbdselect .checkbox-list label div.text {
  width: 110px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 3px;
}

.dyncharparse {
  display: none;
  width: 325px;
  position: absolute;
  z-index: 999999999;
  margin: 0 !important;
}

.filterpanel .filteritem {
  background: #ECEFF3;
  padding: 10px;
  padding-bottom: 7px;
  margin: 0px;
  margin-bottom: 3px;
  padding-right: 25px;
  position: relative;
}

.filterpanel .filteritem .col-md-1,
.filterpanel .filteritem .col-md-2,
.filterpanel .filteritem .col-md-3,
.filterpanel .filteritem .col-md-4 {
  padding: 0px 5px;
  margin-bottom: 3px;
}

.filterpanel .filteritem .value-container .form-control {
  height: 35px !important;
}

.filterpanel .filteritem .fa-times-circle {
  display: none;
  font-size: 15px;
  position: absolute;
  top: 15px;
  width: 25px;
  height: 25px;
  padding-top: 5px;
  padding-left: 5px;
  cursor: pointer;
  color: #5b9bd1;
}

.filterpanel .move:hover {
  cursor: move;
}

.filterpanel .filterop {
  height: 100%;
  overflow: hidden;
  color: #5b9bd1;
  padding-left: 11px;
}

.filterpanel .filterop span {
  cursor: pointer;
  margin-right: 10px;
  float: left;
}

.filterpanel .filterop .resetfilter {
  float: right;
}

.filterpanel .filterop .acthigh input {
  margin-left: -10px;
}

.filterpanel .simple {
  height: 28px;
  line-height: 28px;
}

.filterpanel .simple span {
  margin-right: 15px;
}

.filterpanel .simple .resetfilter {
  float: left;
}

.filterpanel .simple > button {
  float: left;
  min-width: 28px;
  min-height: 28px;
  height: 28px;
  color: #fff !important;
  background: #009FE8 !important;
  border-radius: 4px !important;
  padding: 2px 6px 5px 6px !important;
  border: 1px solid #D6DEE9;
  border-color: #009FE8 !important;
}

.filterpanel .simple > button:hover {
  background: #33B2ED !important;
  border-color: #33B2ED !important;
}

.y-synergyfile {
  width: 100%;
  margin-bottom: 1px !important;
}

.y-synergyfile .btn {
  border: 1px solid #cacaca;
  width: 100% !important;
  text-align: left;
  padding: 5px 10px 5px 8px;
}

.y-synergyfile .btn img {
  width: 20px;
  height: 20px;
  float: left;
  display: none;
  margin-right: 3px;
}

.y-synergyfile .btn span {
  width: 80%;
  overflow: hidden;
  display: inline-block;
  height: 20px !important;
  line-height: 20px !important;
  float: left;
}

.y-synergyfile .btn .fa-angle-down {
  float: right;
  height: 20px !important;
  line-height: 20px !important;
}

.y-synergyfile > .dropdown-menu {
  min-width: 355px !important;
}

.y-synergyfile > .dropdown-menu:before {
  right: initial;
  left: 15px !important;
}

.y-synergyfile > .dropdown-menu:after {
  right: initial;
  left: 16px !important;
}

.y-synergyfile > .dropdown-menu li a img {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: top;
}

.jsonimage {
  height: 100%;
  overflow: hidden;
}

.jsonimage li {
  margin-right: 5px;
  float: left;
}

.jsonimage li img {
  width: 72px;
  height: 72px;
}

/*表达式字段*/

.exprcontent button {
  width: 60px;
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f7f7f7), to(#ebebeb)) !important;
  background-image: -moz-linear-gradient(top, #f7f7f7, #ebebeb) !important;
  border: 1px solid #e5e5e5;
}

.exprcontent button.large {
  width: 128px;
}

.exprcontent button:active {
  background: #fff !important;
}

.exprcontent textarea {
  outline: none;
  resize: none;
  height: 103px;
}

.exprcontent li {
  margin-top: 8px;
}

.exprcontent ul {
  margin-bottom: 8px;
}

.exprcontent .y-tree {
  float: left;
  overflow: auto;
  border-right: 1px solid #e5e5e5;
  width: 290px;
  height: 190px;
}

.exprcontent .y-btn {
  padding: 7px 14px;
  float: right;
}

.exprcontent .y-field {
  padding: 7px 14px;
}

.exprbor {
  margin-top: 8px;
}

.exprpanel .field-keybad {
  margin-bottom: 5px;
}

.exprpanel .field-keybad .field-box {
  float: left;
  width: 300px;
  height: 187px;
  overflow-x: hidden;
  overflow-y: auto;
  border: solid 1px #ccc;
}

.exprpanel .field-keybad .keybad-box {
  width: 260px;
  float: left;
  padding-left: 30px;
}

.exprpanel .field-keybad .keybad-box ul li {
  padding-bottom: 4px;
}

.exprpanel .field-keybad .keybad-box button {
  width: 50px;
}

.exprpanel .field-keybad .keybad-box ul li:nth-child(4) button {
  width: 69px;
}

.exprpanel .field-keybad .keybad-box ul li:nth-child(4) button:last-child {
  width: 67px;
}

.exprpanel .field-keybad .keybad-box ul li:nth-child(5) button {
  width: 105px;
}

.exprpanel textarea {
  height: 65px;
}

/*树形控件样式*/

.ztree {
  color: #333333;
}

.ztree li a.curSelectedNode {
  background: #fff;
  color: #009FE8 !important;
}

.ztree li a.curSelectedNode span.node_name {
  color: #009FE8;
}

.ztree li .chk {
  cursor: pointer;
}

.ztree li .checkbox_false_full,
.ztree li .checkbox_false_full_focus,
.ztree li .checkbox_true_full,
.ztree li .checkbox_true_full_focus,
.ztree li .checkbox_true_part,
.ztree li .checkbox_true_part_focus,
.ztree li .checkbox_false_part,
.ztree li .checkbox_false_part_focus {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
}

.ztree li .checkbox_false_full,
.ztree li .checkbox_false_full_focus {
  background-position: -83px -73px !important;
}

.ztree li .checkbox_true_full,
.ztree li .checkbox_true_full_focus {
  background-position: -122px -73px !important;
}

.ztree li .checkbox_true_part,
.ztree li .checkbox_true_part_focus,
.ztree li .checkbox_false_part,
.ztree li .checkbox_false_part_focus {
  background-position: -162px -73px !important;
}

.alp-search a {
  background-color: #009FE8;
  color: #ffffff;
}

.alp-tree > div,
.tree-left > div {
  border: 1px solid #E3E3E3;
}

.alp-tree > div:first-child,
.tree-left > div:first-child {
  width: calc(100% - 13px);
}

.alp-tree > div .mCustomScrollbar,
.tree-left > div .mCustomScrollbar {
  padding-right: 0px;
}

.alp-tree .ztree,
.tree-left .ztree {
  overflow: auto;
}

.alp-tree .move-cursor,
.tree-left .move-cursor {
  width: 13px;
  height: 100%;
  background-color: #fff;
  cursor: col-resize;
  border: none;
}

.alp-tree .move-cursor span,
.tree-left .move-cursor span {
  width: 1px;
  height: 50px;
  display: inline-block;
  top: 40%;
  position: absolute;
  background: #bdbdbd;
}

.alp-tree .move-cursor span.move_1,
.tree-left .move-cursor span.move_1 {
  margin-left: 3px;
}

.alp-tree .move-cursor span.move_2,
.tree-left .move-cursor span.move_2 {
  margin-left: 6px;
}

.alp-tree .move-cursor span.move_3,
.tree-left .move-cursor span.move_3 {
  margin-left: 9px;
}

.y-enum .tree-left {
  padding-right: 0px;
}

.y-enum .tree-left .portlet {
  margin-bottom: 0px;
  border: none;
}

.y-enum .tree-left .portlet .tools span {
  cursor: pointer;
  margin-left: 10px;
}

.y-enum .tree-left .portlet .form-body {
  overflow: auto;
  padding-right: 0px;
}

.y-enum .tree-right {
  background: #fff;
  padding: 10px;
  margin-bottom: 0px;
  width: 74%;
  border: none;
}

.y-enum .tree-right .enumoperation {
  height: 45px;
  padding-top: 3px;
}

.y-enum .tree-right .enumoperation button {
  height: 30px;
  line-height: 5px;
}

.y-enum .tree-right .tools .btn {
  padding: 2px 6px 5px 6px !important;
}

.y-enum .tree-right .portlet .form-body {
  min-height: 500px;
}

.y-enum .portlet.box.yellow-casablanca > .portlet-title {
  background: #FAFAFA;
  border-radius: 0 !important;
}

.y-enum .form .form-body {
  border-radius: 0 !important;
}

/*角色授权*/

.y-assignright {
  padding: 0px !important;
  margin: 0 -10px;
}

.y-assignright .portlet.box.yellow-casablanca .portlet-title > .caption {
  color: #333333;
  font-size: 16px;
}

.y-assignright .portlet.box.yellow-casablanca .portlet-title .checkbox-inline {
  padding: 4px 0px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-left: 5px;
  cursor: pointer;
}

.y-assignright .portlet.box.yellow-casablanca .portlet-title .checkbox-inline .checker {
  margin-bottom: 5px;
}

.y-assignright .portlet.box.yellow-casablanca .portlet-title .checkbox-inline .checker input {
  cursor: pointer;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box {
  width: 100%;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box tbody tr.space {
  height: 10px;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box td {
  width: 80%;
  color: #333333;
  border: 1px solid #E3E3E3;
  border-right: none;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box td:last-child {
  border-right: 1px solid #E3E3E3;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box td .checkbox-list {
  padding: 10px 20px;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box td .checkbox-list .checkbox-inline .checker {
  margin-right: 8px !important;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .first-td {
  min-width: 80px;
  width: 10%;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  background: #ECF0F2;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .first-td .checkbox-inline {
  padding: 15px 0px;
  font-size: 14px;
  display: block;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .first-td .checkbox-inline .checker input {
  cursor: pointer;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .check-td {
  min-width: 55px;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .last-td {
  background: #fff;
  min-width: 65px;
  width: 10%;
  padding: 0 5px;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .last-td a {
  color: #009FE8;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .checkbox-list .ass-col {
  width: 12.5%;
  margin-left: 0px !important;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .checkbox-list > label.checkbox-inline {
  font-size: 14px;
  padding: 2px 0 !important;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .sec-box .checkbox-list > label.checkbox-inline .check-text {
  display: inline-block;
  width: calc(100% - 25px);
  vertical-align: middle;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .bordertopbox {
  background: #fff;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .bordertopbox [opcode="permfresh"] {
  color: #475166;
  border-color: #CDD9E6 !important;
}

.y-assignright .portlet.box.yellow-casablanca .form-body .bordertopbox [opcode="permfresh"]:hover {
  color: #475166 !important;
  background: #f7f9fb !important;
}

/*数据范围*/

.datarange > li {
  padding: 0px 10px;
  border-bottom: 1px solid #E3E3E3;
}

.datarange > li:last-child {
  border-bottom: none;
}

.datarange > li:hover {
  background: #e8f8ff;
}

.datarange > li .select2-container .select2-choice,
.datarange > li .select2-container .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .datarange > li .select2-container button,
.datarange > li .select2-container .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .datarange > li .select2-container button {
  height: 30px;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
}

.datarange .datarange-head {
  padding: 8px 10px;
  background: #ECF0F2;
  font-size: 14px;
  font-weight: bold;
}

.datarange .datarange-head:hover {
  background: #ECF0F2;
}

.datarange .datarange-head > div {
  border-right: 1px solid #CDD9E6;
}

.datarange .datarange-head > div:last-child {
  border-right: none;
}

.datarange .datarange-hightlight {
  background: #F8F9FD;
}

/*字段授权*/

.y-field-permit {
  text-align: left;
  border: 1px solid #E3E3E3;
  border-bottom: none;
}

.y-field-permit .y-field-permit-title {
  background: #E3E3E3;
  border-bottom: 1px solid #E3E3E3;
}

.y-field-permit .y-field-permit-title > li {
  border-right: 1px solid #CDD9E6;
}

.y-field-permit .y-field-permit-title > li:last-child {
  border-right: none;
}

.y-field-permit .y-field-permit-content {
  padding: 0;
  height: 250px;
  overflow-y: auto;
}

.y-field-permit .y-field-permit-content > ul {
  margin: 0;
}

.y-field-permit .y-field-permit-content > ul > li {
  border-bottom: 1px solid #E3E3E3;
}

.y-field-permit .y-field-permit-content > ul > li:last-child {
  border-bottom: none;
}

.y-field-permit .y-field-permit-content > ul > li:nth-child(2n) {
  background: #F8F9FD;
}

.y-field-permit .y-field-permit-content > ul > li:hover {
  background: #e8f8ff;
}

.y-field-permit .y-field-permit-content .arrow-donw,
.y-field-permit .y-field-permit-content .arrow-up {
  width: 10px;
  height: 6px;
  cursor: pointer;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
}

.y-field-permit .y-field-permit-content .arrow-donw {
  background: url("../../include/uniform/images/arrow-down.png");
  background-repeat: no-repeat;
}

.y-field-permit .y-field-permit-content .arrow-up {
  background: url("../../include/uniform/images/arrow-up.png");
  background-repeat: no-repeat;
}

.y-field-permit ul li {
  height: 30px;
  line-height: 30px;
}

/*仪表盘样式开始*/

.tab-pane .y-widget-add-button {
  border-radius: 18px !important;
  width: 123px;
  height: 30px;
}

.tab-pane .y-widget {
  color: #666666;
  margin-top: 10px;
  min-height: 315px;
  box-shadow: 0px 1px 5px 0px rgba(21, 78, 146, 0.1);
  border-radius: 2px;
  background: #fff;
}

.tab-pane .y-widget > [class*='col-'] {
  padding-left: 0;
  padding-right: 0;
}

.tab-pane .y-widget .y-widget-title {
  min-height: 54px;
  line-height: 54px;
  border-bottom: 1px solid #E8E8E8;
  padding: 0 10px;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-item {
  font-size: 14px;
  font-weight: bold;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-button {
  text-align: right;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-button span {
  display: inline-block;
  margin-right: 5px;
  vertical-align: middle;
  cursor: pointer;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-button .y-widget-title-button-add,
.tab-pane .y-widget .y-widget-title .y-widget-title-button .y-widget-title-button-delete,
.tab-pane .y-widget .y-widget-title .y-widget-title-button .y-widget-title-button-scale {
  width: 14px;
  height: 14px;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-button .y-widget-title-button-edit {
  width: 15px;
  height: 15px;
}

.tab-pane .y-widget .y-widget-title .y-widget-title-button .y-widget-title-button-refresh {
  width: 20px;
  height: 16px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-wrrap {
  min-height: 250px;
  padding: 0 10px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-wrrap .y-widget-content-item {
  padding: 10px 0;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-notice li {
  height: 56px;
  line-height: 56px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-notice li > div {
  height: 56px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-notice li .y-widget-content-notice-new {
  display: inline-block;
  width: 27px;
  height: 12px;
  margin-left: 5px;
  vertical-align: middle;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-task li {
  padding-top: 16px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-linechart {
  padding: 10px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-linechart ul li {
  display: inline;
  margin-right: 5px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-linechart ul .active {
  color: #009FE8;
  border-bottom: 1px solid #009FE8;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-linechart select {
  width: 84px;
  height: 28px;
  border: 1px solid #e8e8e8;
  border-radius: 4px !important;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-linechart .y-widget-content-rpt {
  min-height: 180px;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-item .item-bottom-content {
  line-height: 22px;
  text-align: center;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-item .item-img {
  width: 68px;
  height: 68px;
  border-radius: 12px;
  margin: 0 auto;
}

.tab-pane .y-widget .y-widget-content .y-widget-content-item .item-num {
  width: 68px;
  height: 68px;
  color: #666666;
  font-size: 48px;
  font-family: ArialMT;
  font-weight: 400;
  text-align: center;
  margin: 0 auto;
}

.tab-pane .y-widget .y-widget-content .content-item-border {
  border-bottom: 1px dashed #E8E8E8;
  min-height: 56px;
  padding: 0 10px;
}

@media (max-width: 1280px) {
  .y-widget-content-item .item-bottom-content {
    line-height: 22px;
    text-align: center;
  }

  .y-widget-content-item .item-img,
  .y-widget-content-item .item-num {
    width: 55px !important;
    height: 55px !important;
  }
}

.sys-control {
  text-align: center;
}

.sys-control span {
  margin-right: 16px;
  cursor: pointer;
}

.sys-menuitem {
  float: left;
  font-size: 14px;
  color: #666666;
  font-family: 'Microsoft YaHei';
  text-align: center;
  position: relative;
  margin-bottom: 10px;
  padding: 0 15px;
}

.sys-menuitem:hover .sys-one-close,
.sys-menuitem:hover .sys-menu-edit,
.sys-menuitem:hover .sys-menu-close {
  display: block;
}

.sys-menu-close,
.sys-one-close,
.sys-menu-edit {
  position: absolute;
  display: none;
  width: 15px;
  height: 15px;
  background-size: 100%;
  cursor: pointer;
  border-radius: 50% !important;
  box-shadow: 0px 0px 1px 1px #4E637B;
}

.sys-menu-close,
.sys-one-close {
  right: 10px;
  top: -3px;
  background-image: url("../../images/quit_ic.png");
}

.sys-menu-edit {
  right: 10px;
  top: 17px;
  background-image: url("../../images/edit_ic.png");
}

.sys-menu-content,
.sys-menu-show {
  cursor: pointer;
  margin: 0 auto;
}

.menu-img {
  margin: 0 auto;
  width: 68px;
  height: 68px;
}

.sys-menu-content,
.sys-menu-show {
  width: 68px;
}

.menu-mes {
  padding-top: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sys-add .menu-img {
  background-image: url("../../images/add.png");
  background-position: 0px 0px !important;
}

.menu-img {
  background-image: url("../../images/dashboard/sprites.png");
  background-position: -86px -12px !important;
}

.menu-img.default {
  background-position: -12px -12px !important;
}

.menu-img[formid=ydj_leads] {
  background-position: -160px -12px !important;
}

.menu-img[formid=ydj_customerrecord] {
  background-position: -234px -12px !important;
}

.menu-img[formid=ydj_customer] {
  background-position: -308px -12px !important;
}

.menu-img[formid=bas_reception] {
  background-position: -382px -12px !important;
}

.menu-img[formid=ydj_scalerecord] {
  background-position: -456px -12px !important;
}

.menu-img[formid=ydj_designscheme] {
  background-position: -530px -12px !important;
}

.menu-img[formid=ydj_saleintention] {
  background-position: -604px -12px !important;
}

.menu-img[formid=ydj_order] {
  background-position: -678px -12px !important;
}

.menu-img[formid=ste_saleinvoice] {
  background-position: -752px -12px !important;
}

.menu-img[formid=ydj_costaccounting] {
  background-position: -826px -12px !important;
}

.menu-img[formid=ydj_order_chg] {
  background-position: -900px -12px !important;
}

.menu-img[formid=ydj_bespeak] {
  background-position: -974px -12px !important;
}

.menu-img[formid=rpt_orderdetail] {
  background-position: -1048px -12px !important;
}

.menu-img[formid=ste_goal] {
  background-position: -1122px -12px !important;
}

.menu-img[formid=sys_reportshell] {
  background-position: -1196px -12px !important;
}

.menu-img[formid=rpt_profitanalysis] {
  background-position: -1270px -12px !important;
}

.menu-img[formid=rpt_storeperformanceexecute] {
  background-position: -1344px -12px !important;
}

.menu-img[formid=rpt_orderexecute] {
  background-position: -1418px -12px !important;
}

.menu-img[formid=ydj_building] {
  background-position: -1492px -12px !important;
}

.menu-img[formid=ste_channel] {
  background-position: -1566px -12px !important;
}

.menu-img[formid=ydj_case] {
  background-position: -1640px -12px !important;
}

.menu-img[formid=ydj_suit] {
  background-position: -12px -86px !important;
}

.menu-img[formid=ydj_productbarcode] {
  background-position: -86px -86px !important;
}

.menu-img[formid=ydj_price] {
  background-position: -160px -86px !important;
}

.menu-img[formid=bas_priceadjust] {
  background-position: -234px -86px !important;
}

.menu-img[formid=pur_reqorder] {
  background-position: -308px -86px !important;
}

.menu-img[formid=pur_reqorder_chg] {
  background-position: -382px -86px !important;
}

.menu-img[formid=ydj_purchaseorder] {
  background-position: -456px -86px !important;
}

.menu-img[formid=ydj_purchaseorder_chg] {
  background-position: -530px -86px !important;
}

.menu-img[formid=rpt_purchasedetail] {
  background-position: -604px -86px !important;
}

.menu-img[formid=rpt_purchaseexecute] {
  background-position: -678px -86px !important;
}

.menu-img[formid=ydj_supplier] {
  background-position: -752px -86px !important;
}

.menu-img[formid=ydj_purpriceadjust] {
  background-position: -826px -86px !important;
}

.menu-img[formid=ydj_purchaseprice] {
  background-position: -900px -86px !important;
}

.menu-img[formid=ydj_deliveryway] {
  background-position: -974px -86px !important;
}

.menu-img[formid=stk_scheduleplatform] {
  background-position: -1048px -86px !important;
}

.menu-img[formid=stk_scheduleapply] {
  background-position: -1122px -86px !important;
}

.menu-img[formid=stk_scheduleplanbill] {
  background-position: -1196px -86px !important;
}

.menu-img[formid=pur_receiptnotice] {
  background-position: -1270px -86px !important;
}

.menu-img[formid=stk_postockin] {
  background-position: -1344px -86px !important;
}

.menu-img[formid=pur_returnnotice] {
  background-position: -1418px -86px !important;
}

.menu-img[formid=stk_postockreturn] {
  background-position: -1492px -86px !important;
}

.menu-img[formid=sal_deliverynotice] {
  background-position: -1566px -86px !important;
}

.menu-img[formid=stk_sostockout] {
  background-position: -1640px -86px !important;
}

.menu-img[formid=sal_returnnotice] {
  background-position: -12px -160px !important;
}

.menu-img[formid=stk_sostockreturn] {
  background-position: -86px -160px !important;
}

.menu-img[formid=stk_reservebill] {
  background-position: -160px -160px !important;
}

.menu-img[formid=stk_inventorytransfer] {
  background-position: -234px -160px !important;
}

.menu-img[formid=stk_inventorytransferreq] {
  background-position: -308px -160px !important;
}

.menu-img[formid=stk_otherstockinreq] {
  background-position: -382px -160px !important;
}

.menu-img[formid=stk_otherstockin] {
  background-position: -456px -160px !important;
}

.menu-img[formid=stk_otherstockoutreq] {
  background-position: -530px -160px !important;
}

.menu-img[formid=stk_otherstockout] {
  background-position: -604px -160px !important;
}

.menu-img[formid=stk_inventoryverify] {
  background-position: -678px -160px !important;
}

.menu-img[formid=stk_inventorybase] {
  background-position: -752px -160px !important;
}

.menu-img[formid=stk_inventorylist] {
  background-position: -826px -160px !important;
}

.menu-img[formid=rpt_stocksynthesize] {
  background-position: -900px -160px !important;
}

.menu-img[formid=rpt_stocksummary] {
  background-position: -974px -160px !important;
}

.menu-img[formid=rpt_stockdetail] {
  background-position: -1048px -160px !important;
}

.menu-img[formid=stk_invcloseaccount] {
  background-position: -1122px -160px !important;
}

.menu-img[formid=stk_initstockbill] {
  background-position: -1196px -160px !important;
}

.menu-img[formid=stk_invcompleteinit] {
  background-position: -1270px -160px !important;
}

.menu-img[formid=ydj_storehouse] {
  background-position: -1344px -160px !important;
}

.menu-img[formid=ydj_stockstatus] {
  background-position: -1418px -160px !important;
}

.menu-img[formid=bcm_packorder] {
  background-position: -1492px -160px !important;
}

.menu-img[formid=bcm_receptionscantask] {
  background-position: -1566px -160px !important;
}

.menu-img[formid=bcm_deliveryscantask] {
  background-position: -1640px -160px !important;
}

.menu-img[formid=bcm_scanresult] {
  background-position: -160px -234px !important;
}

.menu-img[formid=bcm_barcodemaster] {
  background-position: -234px -234px !important;
}

.menu-img[formid=ydj_vist] {
  background-position: -308px -234px !important;
}

.menu-img[formid=ste_afterfeedback] {
  background-position: -382px -234px !important;
}

.menu-img[formid=aft_manage] {
  background-position: -456px -234px !important;
}

.menu-img[formid=aft_instruction] {
  background-position: -530px -234px !important;
}

.menu-img[formid=ydj_merchantorder] {
  background-position: -604px -234px !important;
}

.menu-img[formid=ser_apply] {
  background-position: -678px -234px !important;
}

.menu-img[formid=ydj_service] {
  background-position: -752px -234px !important;
}

.menu-img[formid=ydj_servicechange] {
  background-position: -826px -234px !important;
}

.menu-img[formid=ser_servicefeed] {
  background-position: -1048px -234px !important;
}

.menu-img[formid=pay_settleorder] {
  background-position: -1344px -234px !important;
}

.menu-img[formid=coo_incomedisburserptsal] {
  background-position: -1418px -234px !important;
}

.menu-img[formid=ser_complaintrecord] {
  background-position: -1566px -234px !important;
}

.menu-img[formid=ser_reward] {
  background-position: -1640px -234px !important;
}

.menu-img[formid=ydj_master] {
  background-position: -12px -308px !important;
}

.menu-img[formid=ydj_team] {
  background-position: -86px -308px !important;
}

.menu-img[formid=ydj_dealerinfo] {
  background-position: -234px -308px !important;
}

.menu-img[formid=ydj_seritem] {
  background-position: -308px -308px !important;
}

.menu-img[formid=ydj_seritemprice] {
  background-position: -382px -308px !important;
}

.menu-img[formid=ydj_carinformation] {
  background-position: -456px -308px !important;
}

.menu-img[formid=ser_truckinfo] {
  background-position: -530px -308px !important;
}

.menu-img[formid=ydj_evaluate] {
  background-position: -678px -308px !important;
}

.sys-dashboard {
  min-height: 600px;
}

.board-container {
  min-height: 520px;
  padding: 0px;
}

.board-part {
  margin-bottom: 10px;
  padding: 0 5px;
}

.board-part:last-child {
  margin-bottom: 0px;
}

.board-part-all {
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0px 1px 5px 0px rgba(21, 78, 146, 0.1);
}

.board-part-title {
  height: 45px;
  line-height: 45px;
  font-family: SimSun;
  font-weight: bold;
  font-size: 14px;
  color: #333333;
  border-bottom: 1px solid #e8e8e8;
  cursor: all-scroll;
}

.board-part-title div.title-content {
  float: left;
  padding-left: 16px;
}

.title-button {
  float: right;
  padding-top: 12px;
  padding-right: 8px;
}

.title-button > div {
  float: right;
  line-height: 17px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  margin-right: 10px;
}

.t-close {
  background: url("../../images/all-icon.png") no-repeat;
  background-position: -345px -73px;
}

.t-refresh {
  background: url("../../images/all-icon.png") no-repeat;
  background-position: -185px -115px;
}

.t-set {
  background: url("../../images/all-icon.png") no-repeat;
  background-position: -181px -138px;
}

.t-more {
  background: url("../../images/more.png") no-repeat;
  background-position: 0px 8px;
}

.t-grid {
  position: relative;
}

.t-grid > ul.dropdown-menu {
  left: -50px;
  top: 10px;
  min-width: 65px;
  height: 116px;
}

.t-grid > ul.dropdown-menu li {
  height: 38px;
  line-height: 38px;
  text-align: center;
  color: #333333;
}

.t-grid > ul.dropdown-menu li:hover,
.t-grid > ul.dropdown-menu li.active {
  background: #e3f6ff;
  color: #009FE8;
}

.t-grid > ul.dropdown-menu::before,
.t-grid > ul.dropdown-menu::after {
  display: none !important;
}

.t-grid-a {
  display: block;
  width: 20px;
  height: 20px;
  background: url("../../images/shange.png") no-repeat;
  background-position: 1px 3px;
}

.title-button div i {
  cursor: pointer;
}

.board-part-content {
  background: #fff;
  padding: 9px;
}

.col-md-12.board-container > .board-part {
  padding: 0 3px;
}

.add-widget {
  width: 20px;
  height: 60px;
  right: 0px;
  top: 50%;
  background: #33B2ED;
  position: fixed;
  z-index: 101;
  outline: none;
  border: none;
  cursor: pointer;
  border-radius: 4px 0 0 4px !important;
  box-shadow: 0px 3px 7px 0px rgba(15, 110, 197, 0.3);
  -webkit-transition: 0.2s;
  -moz-transition: 0.2s;
  -o-transition: 0.2s;
  -ms-transition: 0.2s;
  transition: 0.2s;
}

.add-widget span {
  width: 80px;
  height: 60px;
  position: absolute;
  left: 0;
  top: 0;
}

.add-widget:hover {
  width: 55px;
  height: 55px;
  right: 10px;
  right: 10px;
  border-radius: 50% !important;
  -webkit-transition: 0.2s;
  -moz-transition: 0.2s;
  -o-transition: 0.2s;
  -ms-transition: 0.2s;
  transition: 0.2s;
}

.add-widget:hover:after {
  width: 25px;
  margin-left: -12.5px;
  -webkit-transition: 0.2s;
  -moz-transition: 0.2s;
  -o-transition: 0.2s;
  -ms-transition: 0.2s;
  transition: 0.2s;
}

.add-widget:hover:before {
  height: 25px;
  margin-top: -12.5px;
  -webkit-transition: 0.2s;
  -moz-transition: 0.2s;
  -o-transition: 0.2s;
  -ms-transition: 0.2s;
  transition: 0.2s;
}

.add-widget:after {
  content: '';
  width: 10px;
  height: 2px;
  background: #ffffff;
  display: inline-block;
  position: absolute;
  top: 50%;
  margin-top: -1px;
  left: 50%;
  margin-left: -5px;
}

.add-widget::before {
  content: '';
  width: 2px;
  height: 10px;
  background: #ffffff;
  display: inline-block;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  left: 50%;
  margin-left: -1px;
}

.blue-background-class .board-part-all {
  background-color: #dff0ff;
}

.board-part-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.board-part-content::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #c9c9c9;
}

/*过滤方案样式*/

.tab-pane div#panl,
.layui-layer-content div#panl {
  height: 50px;
  margin-bottom: 5px;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid #E8E8E8;
  overflow-y: hidden;
}

.tab-pane div#panl .panl-border,
.layui-layer-content div#panl .panl-border {
  height: 50px;
  border-bottom: none;
}

.tab-pane div#panl .tab-more-zhen,
.layui-layer-content div#panl .tab-more-zhen {
  height: 50px;
}

.tab-pane div#panl .tab-more-zhen > li,
.layui-layer-content div#panl .tab-more-zhen > li {
  height: 50px;
  border-bottom: none;
  padding-top: 10px;
}

.tab-pane div#panl .tab-more-zhen > li span,
.layui-layer-content div#panl .tab-more-zhen > li span {
  display: inline-block;
}

.tab-pane div#panl .tab-more-zhen > li .num,
.layui-layer-content div#panl .tab-more-zhen > li .num {
  border-radius: 8px !important;
  background: #F0F0F0;
  height: 16px;
  min-width: 26px;
  margin-left: 5px;
}

.tab-pane div#panl .tab-more-zhen > li .nummount,
.layui-layer-content div#panl .tab-more-zhen > li .nummount {
  color: #333;
  font-size: 12px;
}

.tab-pane div#panl .tab-more-zhen .active,
.layui-layer-content div#panl .tab-more-zhen .active {
  border-bottom: 4px solid #009FE8;
}

.tab-pane div#panl .tab-more-zhen .active .title,
.layui-layer-content div#panl .tab-more-zhen .active .title {
  color: #009FE8;
}

.tab-pane div#panl .tab-more-zhen .title .fa-times-circle,
.layui-layer-content div#panl .tab-more-zhen .title .fa-times-circle {
  color: #009FE8;
  top: 7px;
}

.tab-pane div#panl .list-filter-edit,
.layui-layer-content div#panl .list-filter-edit {
  right: 0;
  top: 0;
  width: 45px;
  z-index: 3;
  background: #fff;
  text-align: center;
  cursor: pointer;
  height: 50px;
  position: absolute;
  line-height: 55px;
  color: #009FE8;
  border-bottom: 1px #E8E8E8 solid;
  border-left: 0;
  padding-top: 0;
}

.tab-pane div#panl .list-filter-edit i.fa-edit,
.layui-layer-content div#panl .list-filter-edit i.fa-edit {
  font-size: 20px;
}

.tab-pane div#panl .list-filter-edit:hover i.fa-edit,
.layui-layer-content div#panl .list-filter-edit:hover i.fa-edit {
  color: #33B2ED;
}

.tab-pane .page-menu-list,
.layui-layer-content .page-menu-list {
  border: none;
}

.tab-pane .page-menu-list [menu="prev"],
.tab-pane .page-menu-list [menu="next"],
.layui-layer-content .page-menu-list [menu="prev"],
.layui-layer-content .page-menu-list [menu="next"] {
  float: left !important;
}

.tab-pane .list-menu-btnstyle,
.layui-layer-content .list-menu-btnstyle {
  padding: 0 0 5px 0;
}

.tab-pane .tab_title,
.layui-layer-content .tab_title {
  margin: 0;
}

.tab-pane .tab_title i#other_info,
.layui-layer-content .tab_title i#other_info {
  width: 30px;
  height: 30px;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -109px -111px !important;
  margin: 0px 0px 0px -18px;
}

.tab-pane .page-search-wrrap,
.layui-layer-content .page-search-wrrap {
  z-index: 251;
  width: 900px;
  top: 10%;
  left: 54%;
  margin-left: -500px;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3);
  position: fixed;
  background-color: white;
  height: 500px;
}

.tab-pane .page-search-wrrap .layui-layer-title,
.layui-layer-content .page-search-wrrap .layui-layer-title {
  background-color: #009FE8 !important;
  height: 42px;
  line-height: 42px;
}

.tab-pane .page-search-wrrap .page-search-btn,
.layui-layer-content .page-search-wrrap .page-search-btn {
  border-top: 1px solid #eeeeee;
  padding-top: 6px;
  text-align: right;
  pointer-events: auto;
  -webkit-user-select: none;
}

.tab-pane .page-search-wrrap .page-search-btn a,
.layui-layer-content .page-search-wrrap .page-search-btn a {
  margin: 0px 6px;
  display: inline-block;
  height: 28px;
  font-weight: 400;
  cursor: pointer;
  text-decoration: none;
}

.tab-pane .page-search-wrrap .page-search-btn .btn-active,
.layui-layer-content .page-search-wrrap .page-search-btn .btn-active {
  background: #009FE8 !important;
  border: 1px solid #009FE8 !important;
  color: white;
}

.tab-pane .page-search-wrrap .page-search-pane,
.layui-layer-content .page-search-wrrap .page-search-pane {
  max-height: 417px !important;
  width: calc(100% - 5px);
  position: relative;
  border: none;
  right: 0px;
  padding: 10px 12px;
  height: 417px;
}

.tab-pane .page-search-wrrap .page-search-pane .search-pane-current,
.layui-layer-content .page-search-wrrap .page-search-pane .search-pane-current {
  padding: 5px 0;
  padding-bottom: 10px;
}

.tab-pane .page-search-wrrap .page-search-pane .select2-choice,
.tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .tab-pane .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .select2-choice,
.layui-layer-content .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane button {
  border-radius: 4px !important;
  border: 1px solid #D6DEE9;
  font-size: 14px;
}

.tab-pane .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane button {
  color: #475166;
  outline: none;
}

.tab-pane .page-search-wrrap .page-search-pane button.filter-save,
.layui-layer-content .page-search-wrrap .page-search-pane button.filter-save {
  color: #fff;
  background: #009FE8;
  border-color: #009FE8;
}

.tab-pane .page-search-wrrap .page-search-pane button.filter-save:hover,
.layui-layer-content .page-search-wrrap .page-search-pane button.filter-save:hover {
  background: #33B2ED;
  border-color: #33B2ED;
}

.tab-pane .page-search-wrrap .page-search-pane .filteritem,
.layui-layer-content .page-search-wrrap .page-search-pane .filteritem {
  background: #fff;
  padding: 0px;
  padding-right: 18px;
}

.tab-pane .page-search-wrrap .page-search-pane .filteritem .fa-times-circle,
.layui-layer-content .page-search-wrrap .page-search-pane .filteritem .fa-times-circle {
  color: #FF313B;
  top: 4px;
  right: -5px;
}

.tab-pane .page-search-wrrap .page-search-pane .filteritem > [class*='col-'],
.layui-layer-content .page-search-wrrap .page-search-pane .filteritem > [class*='col-'] {
  padding: 0px 2px;
}

.tab-pane .page-search-wrrap .filter-close,
.layui-layer-content .page-search-wrrap .filter-close {
  position: absolute;
  right: 15px;
  top: 13px;
  width: 16px;
  height: 16px;
  display: inline-block;
  background: url("../js/skin/default/icon.png") no-repeat;
  background-position: 1px -40px;
  cursor: pointer;
}

.tab-pane .page-search-wrrap .filter-saveas,
.layui-layer-content .page-search-wrrap .filter-saveas {
  border-radius: 4px !important;
  border: 1px solid #D6DEE9;
  font-size: 14px;
  color: #475166;
  background: #fff;
}

.tab-pane .page-search-wrrap .filter-saveas:hover,
.layui-layer-content .page-search-wrrap .filter-saveas:hover {
  background: #fff;
  color: #475166;
}

.tab-pane .page-search-wrrap .newfilter,
.layui-layer-content .page-search-wrrap .newfilter {
  color: #009FE8;
}

.tab-pane .page-search-wrrap .filter-search,
.layui-layer-content .page-search-wrrap .filter-search {
  border-color: #009FE8;
}

@media (max-width: 991px) {
  .tab-pane div#panl,
  .layui-layer-content div#panl {
    margin-top: 0 !important;
  }
}

.page-search-btn .filter-saveas {
  line-height: 26px;
  font-size: 12px !important;
  padding: 0 15px !important;
}

.page-search-btn .filter-saveas:hover {
  background: #f5f5f5;
}

.page-search-btn .filter-search {
  line-height: 26px;
  margin: 0px 6px;
  padding: 0px 15px;
}

.filterpanel .filterop span {
  color: #009FE8 !important;
}

.filterpanel .filterop span.acthigh {
  margin-top: -2px;
}

.filterpanel .filteritem .fa-times-circle {
  right: 0;
  color: #009FE8;
}

.page-search-simple .filterpanel .filteritem {
  background: #fff;
  padding: 0px;
  padding-right: 18px;
}

.page-search-simple .filterpanel .filteritem .fa-times-circle {
  color: #FF313B;
  top: 4px;
  right: -5px;
}

.page-search-simple .filterpanel .filteritem > [class*='col-'] {
  padding: 0px 2px;
}

/*头部菜单样式*/

.page-header {
  background-color: #EBF1F8;
}

.page-header .navbar .top-menu .navbar-nav > li > a {
  color: #4C637B;
}

.page-header .navbar .top-menu .navbar-nav > li .dropdown-menu li a i {
  color: #4C637B !important;
}

.page-header .navbar .top-menu .navbar-nav .y-nav-button-search {
  border: none;
}

.page-header .navbar .top-menu .navbar-nav .y-nav-button-search input {
  display: none;
}

.page-header .navbar .top-menu .navbar-nav .y-nav-button-search i {
  margin-top: 3px;
  cursor: pointer;
}

.page-header .navbar .top-menu .navbar-nav i {
  color: #4C637B;
}

/*弹出框*/

.popform-actions .confirm,
.dialog-actions .confirm {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
}

.popform-actions .confirm:hover,
.dialog-actions .confirm:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

.popform-actions .confirm:hover,
.dialog-actions .confirm:hover {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
}

.popform-actions .confirm:hover:hover,
.dialog-actions .confirm:hover:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

/*页签菜单样式*/

.page-content-wrapper > .page-content > .row .tabbable-custom {
  border-top: none !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom {
  border-bottom-color: #E8E8E8;
  border-top-color: #E8E8E8;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs {
  background-color: #ffffff;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation] {
  margin-right: 3px;
  border: 0;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation] a {
  height: 41px;
  font-size: 15px;
  border-radius: 4px 4px 0px 0px !important;
  line-height: 19px;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation] i.close-tab-icon {
  top: 15px;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation]:hover {
  border: none !important;
  border-radius: 4px 4px 0px 0px !important;
  background-color: #009FE8;
  opacity: .8;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation]:hover a {
  border: none !important;
  background: #009FE8 !important;
  color: #ffffff !important;
  font-weight: 400;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li[role=presentation]:hover i {
  color: #ffffff;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop {
  border-color: #dddddd !important;
  border-radius: 4px 4px 0 0 !important;
  border-bottom: 0 !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop li.y-nav-tab-item a {
  border-radius: 0 0 0 0 !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop li.y-nav-tab-item a:hover {
  border-radius: 0 0 0 0 !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop > a.dropdown-toggle {
  background-color: #f1f4f6;
  border-radius: 4px 4px 0 0 !important;
  padding: 12px 15px;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop > a.dropdown-toggle i {
  color: #4b637b;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop > a.dropdown-toggle b {
  color: #4b637b;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.active {
  background-color: #f1f4f6;
  border: 1px #dddddd solid !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.active > a {
  background: #f1f4f6 !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.active > a i {
  color: #4b637b;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.active > a b {
  color: #4b637b;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item {
  margin-right: 0;
  background-color: #ffffff;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item a {
  background: #fff !important;
  color: #6a8299 !important;
  padding-top: 11px !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item a:hover {
  background: #efefef !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item i {
  color: #698299;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item.active a {
  background: #efefef !important;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.tabdrop.open .dropdown-menu li.y-nav-tab-item:hover {
  background-color: #ffffff;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.active {
  border: none !important;
  border-radius: 4px 4px 0px 0px !important;
  background-color: #009FE8;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.active a {
  border: none !important;
  background: #009FE8 !important;
  color: #ffffff !important;
  font-weight: 400;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.active i {
  color: #ffffff;
}

.page-content-wrapper > .page-content .page-header .tabbable-custom .nav-tabs li.active:hover {
  opacity: 1;
}

/*其他信息*/

i#other_info:hover {
  border-color: #009FE8;
}

i#other_info .otherinfo_box {
  width: 320px;
  height: 300px;
  top: 29px;
  left: 0;
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
  border-radius: 4px !important;
}

i#other_info .otherinfo_box .otherinfo_title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei-Bold';
  font-weight: bold;
  color: #333333;
  background: #fff;
  border-bottom: 1px solid #E3E3E3;
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
}

i#other_info .otherinfo_box .otherinfo_textbox {
  width: 250px;
}

i#other_info .otherinfo_box .otherinfo_textbox .otherinfo_text {
  line-height: 35px;
}

i#other_info .otherinfo_box .otherinfo_textbox .otherinfo_text:last-child {
  border-bottom: none;
}

i#other_info .otherinfo_box .otherinfo_textbox .otherinfo_text label {
  width: 70px;
  font-weight: bold;
  font-size: 14px;
}

i#other_info .otherinfo_box .otherinfo_textbox .otherinfo_text span {
  color: #333333;
}

/*个性化设置样式*/

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-title,
.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content {
  font-size: 14px;
  border: 1px solid #E3E3E3;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-title {
  background-color: #ECF0F2;
  border-bottom: none;
  height: 42px;
  padding: 12px 0px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-title > li {
  font-weight: bold;
  border-right: 1px solid #CDD9E6;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-title > li:last-child {
  border-right: none;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-title > li i {
  font-weight: bolder;
  cursor: pointer;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content {
  height: 330px;
  overflow: auto;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item {
  height: 42px;
  line-height: 42px;
  border-bottom: 1px solid #E3E3E3;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item:nth-child(2n+1) {
  background: #F8F9FD;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item:nth-child(2n+1) .ui-state-default {
  background: #F8F9FD;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item:hover {
  background: #fff6e5 !important;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item:hover .ui-state-default {
  background: #fff6e5 !important;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item:last-child {
  border-bottom: none;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item .arrow-donw,
.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item .arrow-up {
  width: 10px;
  height: 6px;
  cursor: pointer;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item .arrow-donw {
  background: url("../../include/uniform/images/arrow-down.png");
  background-repeat: no-repeat;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .y-nav-basicset-item .arrow-up {
  background: url("../../include/uniform/images/arrow-up.png");
  background-repeat: no-repeat;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .mCSB_inside > .mCSB_container {
  margin-right: 0px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .mCSB_scrollTools {
  right: -5px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .filter-wrrap,
.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .aggreatestyle-wrrap {
  width: 100px;
  height: 130px;
  background: #fff;
  z-index: 999;
  position: absolute;
  border-radius: 4px !important;
  box-shadow: 0px 0px 9px 0px #0D335D;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .filter-wrrap {
  width: 100px;
  height: 250px;
  background: #fff;
  z-index: 999;
  position: absolute;
  border-radius: 4px !important;
  box-shadow: 0px 0px 9px 0px #0D335D;
  padding: 10px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .filter-wrrap span {
  display: inline-block;
  width: 64px;
  height: 30px;
  line-height: 30px;
  border-radius: 2px !important;
  border: 1px solid #CDD9E6;
  margin-top: 20px;
  margin-right: 10px;
  cursor: pointer;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .filter-wrrap .y-searchfield {
  width: 230px;
  height: 34px;
  border: 1px solid #009FE8;
  border-radius: 4px !important;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .aggreatestyle-wrrap li {
  cursor: pointer;
  padding-left: 10px;
  line-height: 24px;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .aggreatestyle-wrrap li:hover {
  background: #fff6e5 !important;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .aggreatestyle-wrrap .disabled {
  background: #E3E3E3;
}

.tabbable-line .tab-content .y-nav-basicset .y-nav-basicset-content .aggreatestyle-wrrap .disabled:hover {
  background: #E3E3E3 !important;
}

.tabbable-line .tab-content .y-styleset .y-styleitem,
.tabbable-line .tab-content .mCustomScrollbar .y-styleitem {
  height: 42px;
  color: #333333;
  font-size: 14px;
  font-weight: bold;
  border: 1px solid #E3E3E3;
  background-color: #FAFAFA;
  margin-bottom: 5px;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem {
  background-color: #fff;
  padding: 0;
  border: none;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'],
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] {
  padding-left: 0;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] .select2-choice,
.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] button,
.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] button,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] .select2-choice,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] .tab-pane .page-search-wrrap .page-search-pane button,
.tab-pane .page-search-wrrap .page-search-pane .tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] button,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] .layui-layer-content .page-search-wrrap .page-search-pane button,
.layui-layer-content .page-search-wrrap .page-search-pane .tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] button {
  height: 34px;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] input,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] input {
  height: 34px !important;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] input[type='lookup'],
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] input[type='lookup'] {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] .ui-icon-plus,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] .ui-icon-plus {
  background-image: url("../../images/ui-icons_222222_256x240.png");
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-'] .ui-icon-trash,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-'] .ui-icon-trash {
  background-image: url("../../images/ui-icons_222222_256x240.png");
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem > [class*='col-']:last-child,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem > [class*='col-']:last-child {
  padding-left: 11px;
}

.tabbable-line .tab-content .y-styleset .stylelist .y-styleitem .color-container .sp-preview-inner,
.tabbable-line .tab-content .mCustomScrollbar .stylelist .y-styleitem .color-container .sp-preview-inner {
  border-radius: 4px !important;
}

/*个性化设置样式*/

.tabbable-line > .nav-tabs > li:hover {
  border-bottom: 2px solid #009FE8;
}

.tabbable-line > .nav-tabs > li:hover a {
  background-color: #fff;
  color: #009FE8;
}

.tabbable-line > .nav-tabs > li.active {
  border-bottom: 2px solid #009FE8;
}

.tabbable-line > .nav-tabs > li.active:hover {
  border-bottom: 2px solid #009FE8;
}

.tabbable-line > .nav-tabs > li.active a {
  background-color: #fff;
  color: #009FE8;
}

.nav-tabs > li > a {
  -webkit-border-radius: 4px 4px 0 0 !important;
  -moz-border-radius: 4px 4px 0 0 !important;
  -ms-border-radius: 4px 4px 0 0 !important;
  -o-border-radius: 4px 4px 0 0 !important;
  border-radius: 4px 4px 0 0 !important;
}

/*动态*/

.record-container {
  box-shadow: -2px 0px 10px 0px rgba(0, 0, 0, 0.18);
}

.record-container div.record-title {
  height: 50px;
  line-height: 50px;
  background: #fff;
  border-bottom: 1px solid #E3E3E3;
  color: #4C637B;
}

.record-container div.record-title > i {
  color: #4C637B;
  margin-top: 18px;
}

.record-container .record-panel .record-main > .fa-filter {
  width: 15px;
  height: 16px;
  color: #4C637B;
  position: absolute;
  right: 15px;
  padding-top: 16px;
  z-index: 55;
  cursor: pointer;
}

.record-container .record-panel .record-main .record-content {
  padding-top: 10px;
  padding-bottom: 0;
}

.record-container .record-panel .record-main .record-content .attachDonwload {
  padding-right: 5px;
}

.record-container .record-panel .record-main .record-content .attachDonwload .reminddate {
  width: 20px;
  height: 20px;
  display: inline-block;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -158px -162px !important;
  cursor: pointer;
}

.record-container .record-panel .record-main .record-content .message {
  padding: 5px 5px 5px 25px;
}

.record-container .record-panel .record-main .record-content .record {
  border-bottom: none;
  padding: 0;
  padding-bottom: 10px;
}

.record-container .record-panel .record-main .record-content .record:first-child {
  padding-top: 8px;
}

.record-container .record-panel .record-main .record-content .record .record-box {
  background: #f3f3f3;
  padding: 5px 10px;
}

.record-container .record-panel .record-main .record-content .record .record-box:hover {
  background: #f3f3f3 !important;
}

.record-container .record-panel .record-main .record-content .record .record-box i {
  width: 20px;
  height: 20px;
  display: inline-block;
  margin-right: 5px;
  float: left;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
}

.record-container .record-panel .record-main .record-content .record .record-box i.type_01 {
  background-position: -133px -162px !important;
}

.record-container .record-panel .record-main .record-content .record .record-box i.type_02 {
  background-position: -81px -162px !important;
}

.record-container .record-panel .record-main .record-content .record .record-box i.type_03 {
  background-position: -107px -162px !important;
}

.record-container .record-panel .record-main .record-content .record .record-box .detail-btn {
  border: 1px #009FE8 solid;
  padding: 3px 5px;
  border-radius: 2px !important;
  margin-left: 5px;
}

.record-container .record-panel .record-main .record-content .record .record-box .detail-btn:hover {
  text-decoration: none;
  border-color: #33B2ED;
  color: #33B2ED;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-table {
  margin-top: 5px;
}

.record-container .record-panel .record-main .record-content .record .record-box .attachDonwload .name {
  color: #999999;
}

.record-container .record-panel .record-main .record-content .record .record-box:hover {
  background: #fff;
  background: #fff;
}

.record-container .record-panel .record-main .record-content .record .record-box:hover .record-detail {
  background: #e8f8ff !important;
}

.record-container .record-panel .record-main .record-content .record .record-box:hover .datetime + div {
  background: #e8f8ff !important;
}

.record-container .record-panel .record-main .record-content .record .record-box .time-line {
  width: 20px;
  float: left;
}

.record-container .record-panel .record-main .record-content .record .record-box .time-line .time-line-arrow {
  height: 20px;
  background: url("../../images/record-arrow.png") no-repeat;
  background-size: cover;
}

.record-container .record-panel .record-main .record-content .record .record-box .time-line .time-line-border {
  width: 2px;
  background: #E3E3E3;
  margin-left: 9px;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right {
  float: left;
  width: calc(100% - 20px);
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload .datetime {
  color: #666666;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload .datetime + div {
  padding: 10px;
  background: #F8F9FD;
  border-radius: 2px !important;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload > div {
  float: none;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload > div .name {
  width: 100%;
  max-width: none;
  color: #999999;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload > .record-detail {
  padding: 10px;
  background: #F8F9FD;
  border-radius: 2px !important;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload > .record-detail > div {
  float: none;
  height: 20px;
  line-height: 20px;
}

.record-container .record-panel .record-main .record-content .record .record-box .record-right .attachDonwload > .record-detail a {
  color: #009FE8;
}

.record-container .record-panel .record-main .record-content .record-datetitle i {
  width: 20px;
  height: 20px;
  display: inline-block;
  cursor: pointer;
  margin-bottom: -5px;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -29px -162px !important;
}

.record-container .record-panel .record-main .record-content .record-datetitle i.hided {
  background-position: -55px -162px !important;
}

.record-container .record-panel .record-main .record-content .record-datetitle span {
  padding-left: 10px;
}

.record-container .record-panel .record-main .record-content .record-date-box {
  padding-left: 9px;
}

.record-container .record-panel .record-main .record-content .record-date-box .record {
  border-left: 2px #E3E3E3 solid;
  padding-left: 15px;
}

.record-container .record-panel .record-main .record-content .record-date-box .record .datetime {
  float: right;
  color: #999999;
}

.record-container .record-panel .record-main .send-record-btn-list .send-record-btn {
  color: #fff;
  background: #009FE8;
  border: 1px solid #009FE8 !important;
  border-radius: 4px !important;
}

.record-container .record-panel .record-main .send-record-btn-list .send-record-btn:hover {
  border: 1px solid #009FE8;
  background: #33B2ED;
}

.filter-icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: absolute;
  top: 65px;
  right: 23px;
  cursor: pointer;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -48px -115px !important;
}

.filter-icon .filter-group-btn {
  position: absolute;
  width: 235px;
  left: -215px;
  top: 20px;
  color: #000;
  padding: 15px;
  cursor: default;
  background-color: #fff;
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
  border-radius: 4px !important;
}

.filter-icon .filter-group-btn p {
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}

.filter-icon .filter-group-btn .checkbox-list {
  text-align: left;
  margin-top: 5px;
  margin-bottom: 10px;
  border: 1px #E3E3E3 solid;
  padding: 10px;
}

.filter-icon .filter-group-btn .checkbox-list .checkbox-inline {
  display: block;
  height: 25px;
  padding-left: 0;
  margin-left: 0;
}

.report-chart {
  margin-bottom: 10px;
  border: solid 1px #E3E3E3;
  padding: 10px;
}

.report-chart [chartid] {
  height: 400px;
}

.report-chart .title {
  width: 114px;
  height: 36px;
  text-align: center;
  font-size: 14px;
  line-height: 36px;
  background-color: #f4f6f9;
  border: 1px #e3e3e3 solid;
  border-bottom: 0;
}

.report-chart .title_border {
  border-bottom: 1px solid #E4E4E4;
  margin-bottom: 20px;
}

.report-nav {
  padding-left: 0px;
}

.report-nav ul li {
  height: 40px;
  margin-bottom: 8px;
  padding: 0 16px;
  overflow: hidden;
  font-size: 14px;
  line-height: 40px;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.report-nav ul li.active {
  background-color: #e6f7ff;
  color: #009FE8;
  border-right: 3px solid #009FE8;
}

.report-nav ul li:hover {
  color: #009FE8;
}

.time-area {
  margin: 0;
  padding: 0;
  color: #333333;
  font-size: 14px;
}

.time-area button {
  height: 32px;
  margin: 0;
  padding: 0 15px;
  color: #333;
  line-height: 30px;
  background: #fff !important;
  border: 1px solid #d9d9d9;
  border-left-width: 0;
  border-radius: 0 !important;
  cursor: pointer;
  position: relative;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.time-area button:first-child::before {
  visibility: hidden;
}

.time-area button:focus {
  color: #333;
  border-color: #d9d9d9;
}

.time-area button:active {
  color: #009FE8 !important;
  border-color: #d9d9d9 !important;
}

.time-area button:hover {
  color: #009FE8;
  border-color: #d9d9d9;
}

.time-area button.active,
.time-area button[dataarea=true] {
  border-color: #009FE8;
  color: #009FE8;
}

.time-area button.active:hover,
.time-area button[dataarea=true]:hover {
  border-color: #33B2ED;
  color: #33B2ED;
}

.time-area button.active:focus,
.time-area button[dataarea=true]:focus {
  color: #009FE8;
  border-color: #009FE8;
}

.time-area button.active:active,
.time-area button[dataarea=true]:active {
  color: #333;
  border-color: #009FE8 !important;
}

.time-area button.active::before,
.time-area button[dataarea=true]::before {
  background-color: #009FE8 !important;
  opacity: .8;
}

.time-area button.active::before,
.time-area button[dataarea=true]::before {
  position: absolute;
  top: -1px;
  left: -1px;
  display: block;
  width: 1px;
  height: calc(100% + 2px);
  background-color: #d9d9d9;
  content: '';
}

.time-area button:first-child {
  border-left-width: 1px;
  border-radius: 4px 0 0 4px !important;
}

.time-area button:last-child {
  border-radius: 0 4px 4px 0 !important;
}

.report-right {
  padding: 0;
}

.panl-content .input-group {
  float: left;
}

.group-space {
  float: left;
  cursor: default;
  border: none;
  background: none;
}

.report-right-text {
  width: 250px;
  height: 250px;
  margin: 90px auto 0 auto;
  line-height: 450px;
  font-size: 36px;
  color: #d9d9d9;
  text-align: center;
  background: url("../../images/no-data.png") no-repeat;
  background-position: 60px 60px;
}

.report-panl .panl-content {
  padding: 10px;
  background: #F4F6F9;
  border: 1px solid #e3e3e3;
  margin-bottom: 10px;
}

.box-one {
  background: #F4F6F9;
}

.body {
  min-width: 750px;
  padding: 0;
  margin: 0 auto;
  overflow: hidden;
  background-color: #fff;
}

.body .top {
  height: 45px;
  background-color: #449be0;
}

.body .left {
  float: left;
  height: 749px;
  background-color: #354960;
  width: 8%;
  min-width: 36px;
  max-width: 62px;
  min-height: 320px;
}

.body .left .item {
  position: relative;
}

.body .left .item.current {
  background: url("../../images/nav-current.png");
  background-repeat: repeat-y;
}

.body .left .item.current .item-link {
  background: none;
}

.body .left .item-link {
  display: block;
  height: 55px;
  text-align: center;
  line-height: 55px;
  font-size: 15px;
  font-weight: bold;
  color: #93A2B3;
  font-family: '微软雅黑';
  background-color: #354960;
}

.body .left .item-link:hover {
  background-color: #00BC8C;
  color: #fff;
}

.body .left .item-container {
  position: absolute;
  border: 1px solid #000;
  width: 70px;
  min-height: 25px;
  text-align: center;
  line-height: 40px;
  left: 70px;
  top: 12px;
  z-index: 10;
  cursor: pointer;
  display: none;
}

.body .left .item-container p a {
  display: block;
  width: 100%;
  height: 100%;
  text-align: center;
  line-height: 25px;
}

.body .main {
  float: left;
  height: 749px;
  min-height: 320px;
  overflow: hidden;
  min-height: 420px;
  width: 800px;
  /*根据浏览器页面进行宽度的改变*/
  width: -moz-calc(100% - 62px);
  width: -webkit-calc(100% - 62px);
  width: calc(100% - 62px);
  background-color: #9C9C9C;
}

.body .main .current {
  display: block;
}

.body .main .content {
  position: relative;
  display: none;
  height: 100%;
}

.body .main .content span {
  position: absolute;
  font-size: 24px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.top-nav {
  padding-top: 10px;
}

.top-left {
  float: left;
  width: 82px;
  height: 35px;
}

.top-center {
  height: 35px;
  float: left;
  width: 80%;
  min-width: 610px;
  list-style: none;
}

.top-center li {
  float: left;
  z-index: 99;
  width: 10%;
  cursor: pointer;
}

.top-center li.current {
  background-position: right -34px;
  margin-right: -10px;
}

.top-center li .top-inner {
  position: relative;
  display: block;
  height: 34px;
  background-color: cadetblue;
}

.top-center li .top-inner:hover {
  background-color: green;
}

.top-center li .top-inner.current {
  background-color: white;
}

.top-center li .top-inner a.top-nav-item {
  display: block;
  height: 100%;
  text-align: center;
  line-height: 34px;
}

.top-center li .top-inner a.refresh {
  position: absolute;
  width: 13px;
  height: 13px;
  right: 10px;
  bottom: 10px;
}

.top-center li .top-inner a.close {
  position: absolute;
  width: 13px;
  height: 13px;
  right: 10px;
  bottom: 10px;
}

.top-navList {
  overflow: hidden;
}

/*警告栏*/

.warm-container {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  display: none;
}

.warm-bg {
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: .5;
}

.warm-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-family: Arial,sans-serif;
  font-size: 12px;
  width: 360px;
  _width: 360px;
  background: #fff;
  border: solid 1px #99b1c4;
  color: #000;
}

.warm-message {
  height: 200px;
  line-height: 200px;
  text-align: center;
}

#act-button {
  position: absolute;
  height: 24px;
  width: 100px;
  text-align: center;
  line-height: 24px;
  background-color: yellow;
  cursor: pointer;
  right: 10px;
  bottom: 10px;
}

/*导航栏右键出现的更多功能*/

.dropdown-company #switchEnterprise {
  max-height: 350px;
  overflow-x: hidden;
  overflow-y: auto;
}

.dropdown-company #switchEnterprise li a {
  padding-right: 25px;
}

.nav-function {
  width: 150px;
  height: 180px;
  position: absolute;
  left: 0;
  top: 20px;
  z-index: 99999;
  border: 1px solid #E7E7E7;
  box-shadow: 1px 1px 2px #787878;
  background-color: white;
  display: none;
}

.nav-function-menu p {
  height: 35px;
}

.nav-function-menu p a {
  display: block;
  height: 100%;
  text-align: left;
  line-height: 35px;
  border-bottom: 1px solid #d1d4d5;
  margin: 0 10px;
}

.nav-function-menu p a:hover {
  background-color: #048fc2;
}

.nav-function-bg {
  border-color: transparent transparent #CCC transparent;
  border-style: solid;
  border-width: 10px;
  height: 0;
  width: 0;
  position: absolute;
  top: -21px;
  left: 67px;
}

/*按钮样式*/

.alp-search {
  line-height: 40px;
  /*overflow: scroll;
	overflow-y: hidden;*/
  white-space: nowrap;
}

.alp-search a {
  border-radius: 3px !important;
  background-color: #00AAEF;
  float: left;
}

.alp-search a.active {
  background-color: #45535E;
}

/*右侧列表数据*/

.alp-grid {
  padding: 0px;
}

body,
.page-content-wrapper > .page-content > .page-header.page-tabs-fixed,
.page-header.navbar.navbar-fixed-top {
  font-family: 'Microsoft YaHei' !important;
}

.container {
  height: 45px !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.page-header.navbar.navbar-fixed-top > .page-header-inner {
  height: 45px !important;
  border: 1px solid #e5e5e5;
  background-color: #fff;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu span,
.page-sidebar-menu.page-sidebar-menu-hover-submenu a {
  font-family: 'Microsoft YaHei';
}

.dropdown-company span {
  font-size: 14px;
}

.dropdown-company a i {
  -webkit-transition: .2s linear;
  -moz-transition: .2s linear;
  -ms-transition: .2s linear;
  -o-transition: .2s linear;
  transition: .2s linear;
}

.dropdown-company:hover a i {
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transition: .2s linear;
  -moz-transition: .2s linear;
  -ms-transition: .2s linear;
  -o-transition: .2s linear;
  transition: .2s linear;
}

.dropdown-company .dropdown-menu {
  margin-top: 7px !important;
}

.dropdown-company .dropdown-menu:before {
  display: none !important;
}

.dropdown-company .dropdown-menu:after {
  left: 20px;
  top: -6px;
}

/*快捷侧边栏按钮样式*/

.input-group-btn.send {
  background-color: #fff;
}

.page-quick-sidebar-chat-user-form .btn.yellow.right {
  margin-top: 10px;
  height: 26px;
  width: 50px;
  color: #fff;
  padding: 0 0 0 1px !important;
  float: right;
  background-color: #fc7900;
  border-radius: 8px !important;
  -webkit-border-radius: 8px !important;
  -moz-border-radius: 8px !important;
  -ms-border-radius: 8px !important;
  -o-border-radius: 8px !important;
}

/*头部固定*/

.clearfix.top-fixed {
  height: 0px;
}

#header_inbox_bar > a {
  height: 45px;
}

/*左侧菜单固定*/

.page-sidebar-wrapper {
  position: fixed;
  z-index: 210;
  opacity: 1;
  left: 0;
  height: 100% !important;
  background-color: #45535E;
}

.page-sidebar-menu.page-sidebar-menu-hover-submenu > .start.active > a {
  background: #222d32;
}

.page-sidebar-menu-closed .menu .click i {
  left: 4px !important;
}

/*左侧菜单鼠标经过样式*/

.page-sidebar-menu-hover-submenu li:hover > .sub-menu a {
  color: #000;
}

.page-logo {
  background: #fff;
  padding-left: 10px;
  height: 50px;
}

/*头部布局样式*/

.head-left {
  background-color: #fff;
  height: 68px;
}

.e-home.search-form > .icon-list {
  font-size: 18px;
}

.fixed-height + .row > .col-md-12 {
  padding-right: 0;
}

/*中间内容框样式*/

.page-container > .page-content-wrapper > .page-content > .row #tab-content {
  border: 2px solid #F6F6F6;
  border-top: 0;
}

.page-sidebar.navbar-collapse.collapse > ul > li > ul.sub-menu {
  padding: 0;
  border-right: 1px solid #e5e9ec;
}

.page-sidebar.navbar-collapse.collapse > ul > li > ul.sub-menu a:hover {
  color: #fff;
}

.page-sidebar.navbar-collapse.collapse > ul > li > ul.sub-menu li {
  width: 100%;
}

/*企业替换图片大小样式*/

.e-home {
  float: left;
  height: 50px;
  padding-top: 14px;
  padding-left: 10px;
}

.e-home span {
  margin-left: 10px;
  font-size: 18px;
  color: #000;
}

.e-home a i {
  font-size: 22px;
  color: #000;
}

.page-tabs-fixed {
  position: fixed;
  top: 0;
  z-index: 110;
  height: 50px;
  border: 0;
  width: 100%;
}

.fixed-height {
  height: 16px;
}

.tabbable.tabbable-custom.tabbable-noborder.tabbable-reversed {
  border-top: 1px solid #ebeef0;
}

#tab {
  border-left: 1px solid #ebeef0;
}

#tab ul {
  margin-top: 10px;
}

#tab ul .dropdown.pull-right.tabdrop a {
  padding-top: 8px !important;
}

.close-tab.glyphicon.glyphicon-remove.close-tab-icon {
  position: absolute;
  display: none;
  right: 1px;
}

.tabbable-custom > .nav-tabs > li.active .close-tab.glyphicon.glyphicon-remove.close-tab-icon {
  display: block;
}

.navbar.navbar-fixed-top:nth-child(2) {
  margin-top: 68px;
}

.page-content-wrapper > .page-content {
  /*background-color:#f6f6f6;*/
}

.nav.nav-tabs > li[role="presentation"] > a {
  height: 40px;
  line-height: 13px;
  color: #6B8299;
  border-radius: 7px 7px 0 0 !important;
}

.page-content2 .nav-tabs li {
  border: 1px #fff solid;
}

.page-content2 .nav-tabs li:hover {
  border: 1px #e4e4e4 solid;
  border-bottom: 0;
  background: #eee;
  border-radius: 7px 7px 0 0 !important;
  -webkit-transition: 0.2s linear;
  -moz-transition: 0.2s linear;
  -ms-transition: 0.2s linear;
  -o-transition: 0.2s linear;
  transition: 0.2s linear;
}

.nav-tabs .tabdrop {
  border-bottom: none !important;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown-dark .dropdown-menu .dropdown-menu-list > li a:hover,
.page-header.navbar .top-menu .navbar-nav > li.dropdown.dropdown-user.dropdown-dark li a:hover {
  background-color: #f4f4f4;
  color: #529de3;
}

@media (max-width: 991px) {
  .page-content2 .nav-tabs li:hover {
    border-radius: 0 !important;
  }

  .tabbable-custom > .nav-tabs > li.active {
    border-radius: 0 !important;
  }

  .tabbable-custom > .nav-tabs > li.active:hover {
    border-radius: 0 !important;
  }

  .nav.nav-tabs > li[role="presentation"] > a {
    border-radius: 0 !important;
  }

  .dropdown-company {
    float: right !important;
  }

  .dropdown-company a {
    line-height: 23px !important;
  }
}

.page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover,
.page-header.navbar .page-logo {
  background-color: #fff;
  border-right: 1px #ececec solid;
}

.page-sidebar {
  background-color: #222d32;
}

.page-header.navbar .top-menu .navbar-nav > li.dropdown .dropdown-toggle .badge.badge-default {
  background-color: #ed1c24;
  width: 18px;
}

.arrorbgblack {
  background-color: #36424c !important;
}

.arrorrightblack {
  border-right-color: #36424c !important;
}

.arrorleftblack {
  border-left-color: #36424c !important;
}

/*切换页面皮肤颜色样式*/

span.label.label-sm.label-icon.label-black,
.e-home,
.top-menu {
  background: #fff;
  color: #4d5b69;
}

span.label.label-sm.label-icon {
  /*黑色*/
  /*灰色*/
  /*蓝色*/
  /*白色(默认)*/
}

span.label.label-sm.label-icon .label-black {
  background-color: #000;
  color: #000;
}

span.label.label-sm.label-icon .label-default {
  color: #c6c6c6;
}

span.label.label-sm.label-icon .label-blue {
  background-color: #3399ff;
  color: #3399ff;
}

span.label.label-sm.label-icon span.label.label-sm.label-icon.label-white {
  background-color: #fff;
  color: #fff;
}

/*皮肤样式—灰色*/

/*头部企业图标和文字颜色*/

.compromise {
  color: #000 !important;
}

/*皮肤样式—黑色*/

.bgblack {
  background-color: #4d5b69 !important;
}

.activeblack {
  background-color: #000 !important;
}

.fontblack {
  color: #fff !important;
}

.hoverblack {
  background-color: #000 !important;
}

.blackborder {
  border: 1px solid #ccc !important;
}

/*皮肤样式—蓝色*/

.bgblue {
  background-color: #529de3 !important;
}

/*皮肤样式—白色*/

.siderbarwhite {
  background-color: #fff !important;
}

.fontWhite {
  color: #000 !important;
}

/*宽窄屏logo替换*/

.logochange {
  width: 33px !important;
  margin-right: 10px !important;
}

.page-sidebar-menu-closed .menu a i {
  top: 1px !important;
  left: 5px !important;
}

.blacklogochange {
  background-image: url("../../include/admin/layout3/images/logo2.png") !important;
}

.slimScrollDiv {
  overflow: inherit !important;
}

.slimScrollDiv ul li a span.details span {
  border: 1px #ccc solid;
}

.page-top {
  float: right;
  width: 100%;
}

/*单据头的可点击的基础资料的状态*/

.form-group {
  position: relative;
}

.summary_one {
  color: #529DE3;
  cursor: pointer;
}

.summary_one:hover {
  text-decoration: underline;
}

.summary_list {
  border: 1px solid #fff;
  color: #529DE3;
  cursor: pointer;
}

.summary_list:hover {
  border: 1px solid #5FCCFF;
  box-sizing: border-box;
  border-radius: 3px !important;
  color: #5FCCFF;
  text-align: center;
}

.bpm-left-title,
.bpm-right-btn {
  height: 55px;
  line-height: 55px;
  background-color: #717D98;
  color: #F4FFFD;
  text-indent: 10px;
  font-size: 16px;
}

.go-tools {
  border: solid 1px black;
  height: 420px;
}

.go-design {
  border: solid 1px black;
  height: 420px;
}

.bpm-right-btn .fa-item {
  padding: 0px;
}

.bpm-right-btn ul.bpn-btns li {
  position: relative;
  cursor: pointer;
  float: left;
  width: 50px;
  height: 55px;
  border-right: 2px solid #949494;
}

.bpm-right-btn ul.bpn-btns li i {
  position: absolute;
  left: 5px;
  top: 20px;
  font-size: 20px !important;
}

.radio-inline .pdend_pad {
  padding-bottom: 15px;
}

.radio-inline .pdend_pad .pdend-check {
  float: left;
  padding-right: 8px;
}

.radio-inline .pdend_pad .pdend-check.more {
  padding-top: 13px;
}

.bpmdes-img-uu {
  float: right;
  width: 20px;
  height: 20px;
  background-image: url("../../images/u897.png");
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: 0px;
}

.bpmdes-img-uu.more {
  height: 40px;
  background-position: 0px 13px;
}

.bpmdes-img-one {
  float: right;
  width: 75px;
  height: 20px;
  background-image: url("../../images/one_people.png");
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: 0px;
}

.bpmdes-img-more {
  float: right;
  width: 85px;
  height: 45px;
  background-image: url("../../images/more_people.png");
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: 0px;
}

.mytoken {
  border: none;
  background: #009FE8;
  min-width: 39px;
  color: #fff;
  padding: 6px 16px;
  cursor: pointer;
  display: inline-block;
  float: right;
  border-radius: 4px !important;
}

.mytoken:hover {
  background: #33B2ED;
}

button[opcode="personalsave"] {
  height: 30px;
  padding: 0px 20px;
  line-height: 28px;
  color: #fff;
  border: none;
  -webkit-transition: 0.2s linear;
  -moz-transition: 0.2s linear;
  -ms-transition: 0.2s linear;
  transition: 0.2s linear;
}

.form-horizontal .radio {
  padding: 0;
}

.materialinit-button button {
  border: 1px solid #009FE8;
  background-color: #009FE8;
  color: #fff;
  border-radius: 3px !important;
}

.materialinit-button button:hover {
  color: #fff;
  border-color: #33B2ED;
  background-color: #33B2ED;
}

.related-tabbable .tab-pane {
  padding: 10px 0 0 0 !important;
}

.portlet.box .yellow-casablanca:last-child {
  margin-bottom: 0;
}

/*侧边栏菜单样式*/

.page-sidebar {
  background-color: #34393D;
}

.page-sidebar .page-logo {
  background-color: #2D3235;
  border-bottom: 1px #141c26 solid;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module {
  border: 0;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module:hover > a,
.page-sidebar .y-nav-menu-panel .y-nav-menu-module:hover > a.click,
.page-sidebar .y-nav-menu-panel .y-nav-menu-module:active > a,
.page-sidebar .y-nav-menu-panel .y-nav-menu-module:active > a.click {
  border-left: 0;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module > a.click {
  border-left: 0;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module > a.click .title,
.page-sidebar .y-nav-menu-panel .y-nav-menu-module > a.click i {
  color: #b8c6d6;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module > a {
  color: #B8C6D6;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module .y-nav-menu-slide {
  background-color: #34393D;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module .y-nav-menu-slide .y-nav-menu-item:hover a {
  color: #009FE8;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module .y-nav-menu-slide.mCustomScrollbar {
  padding-right: 0px;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module.active a {
  background: #34393D;
}

.page-sidebar .y-nav-menu-panel .y-nav-menu-module.active i {
  color: #b8c6d6;
}

.page-sidebar .page-sidebar-bottom {
  margin-top: -20px;
}

.page-sidebar .page-sidebar-bottom > a span {
  display: inline-block;
}

.page-sidebar .page-sidebar-bottom .y-nav-button-chgskin {
  border-right: 1px solid #585858;
}

.page-sidebar .page-sidebar-bottom .y-nav-button-chgskin span {
  width: 25px;
  height: 20px;
  background: url("../../images/skin-change.png") no-repeat;
  background-size: cover;
}

.page-sidebar .page-sidebar-bottom .y-nav-button-chgskin:hover span {
  background: url("../../images/skin-change-hover.png") no-repeat;
}

.page-sidebar .page-sidebar-bottom .y-nav-button-setting span {
  width: 20px;
  height: 20px;
  background: url("../../images/setting.png") no-repeat;
  background-size: cover;
}

.page-sidebar .page-sidebar-bottom .y-nav-button-setting:hover span {
  background: url("../../images/setting-hover.png") no-repeat;
}

.page-sidebar .page-sidebar-menu .menu {
  border-top: 0;
  border-bottom: 0;
  background: none;
  background-position: 140px 20px !important;
}

.page-sidebar .page-sidebar-menu .menu:hover {
  background: #2D3235 url("../../include/admin/layout3/images/arrow-nav.png") no-repeat;
  background-size: 10px 10px;
  background-position: 140px 20px !important;
}

.page-sidebar .page-sidebar-menu .menu > a {
  min-height: 54px;
  border-left: 0;
  background: url("../../include/admin/layout3/images/arrow-nav.png") no-repeat;
  background-size: 10px 10px;
  background-position: 140px 20px !important;
}

.page-sidebar .page-sidebar-menu .menu > a:hover {
  border-left: 0;
}

.page-sidebar .page-sidebar-menu .menu > a:hover i {
  color: #fff;
}

.page-sidebar .page-sidebar-menu .menu > a i {
  top: 10px !important;
}

.page-sidebar .page-sidebar-menu .menu a.click {
  border-left: 0;
  background: #2D3235 url("../../include/admin/layout3/images/arrow-nav-active.png") no-repeat;
  background-size: 10px 10px;
  background-position: 140px 20px !important;
}

.page-sidebar .page-sidebar-menu .menu a.click span,
.page-sidebar .page-sidebar-menu .menu a.click i {
  color: #38b1eb !important;
}

.page-sidebar .page-sidebar-menu .menu .menu-panel {
  background: #2D3235;
}

.page-sidebar .page-sidebar-menu .menu .menu-panel .menu-panel-item .three-menu a:hover {
  color: #38b1eb;
}

.page-sidebar .page-sidebar-menu .start {
  background: none;
}

.page-sidebar .page-sidebar-menu .start a {
  background: none;
}

.page-sidebar .page-sidebar-menu .start:hover {
  background: #2D3235;
}

.page-sidebar .page-sidebar-menu .start:hover i {
  color: #fff;
}

.page-sidebar .page-sidebar-menu .start.active a {
  background: #34393D;
}

.page-sidebar .page-sidebar-menu .start.active a:hover {
  border-left: 0;
  background: #2D3235;
}

.page-sidebar .page-sidebar-menu-closed .menu a i {
  top: 0 !important;
}

.page-sidebar .page-sidebar-menu-closed .start a {
  padding-left: 2px !important;
  padding-top: 10px !important;
}

/*列表页面相关样式*/

.page-search-pane .filterpanel .filterop {
  padding-left: 2px;
}

.page-search-pane .filterpanel .filterop span {
  margin-right: 20px;
}

.page-search-simple {
  padding-bottom: 5px;
}

.list-quick-search .form-control {
  display: inline-block;
}

.list-quick-search .font-active {
  cursor: pointer;
}

.list-quick-search .list-selected-rows {
  display: none;
  margin-left: 10px;
  color: #999;
}

.list-quick-search .alarm {
  position: relative;
  width: 60px;
  margin: 5px;
}

.list-quick-search .divright {
  position: absolute;
  color: white;
  font-size: 10px;
  background-color: #009FE8 !important;
  width: 15px;
  height: 12px;
  line-height: 12px;
  top: -5px;
  text-align: center;
  border-radius: 24px;
}

.advanced-search-title {
  background-color: #009FE8 !important;
  color: white;
  height: 42px;
  line-height: 42px;
  font-size: 14px;
  padding-left: 20px;
  cursor: move;
}

.tree-center {
  overflow: hidden;
}

.tree-center .tree-toggler {
  width: 100%;
  margin-top: -10px;
  position: absolute;
  background-color: #79cdfb;
  background-repeat: no-repeat;
  background-position: -72px -24px;
}

.tree-center .tree-toggler.act {
  background-position: -61px -24px;
}

.form-group .input-icon.right > .input-text::-webkit-input-placeholder,
.form-group .input-icon.right > .input-text::-moz-placeholder,
.form-group .input-icon.right > .input-text:-ms-input-placeholder {
  /* WebKit browsers */
  color: #bfbfbf;
  font-size: 10px;
}

.form-control {
  border: 1px solid #D6DEE9;
  color: #333333;
}

.form-group .input-icon.right .input-text:focus {
  border-color: #009FE8 !important;
  box-shadow: #009FE8 !important;
}

.form-group .input-icon.right .input-text[readonly],
.form-group .input-icon.right .input-text[disabled] {
  cursor: text;
  background-color: #f5f5f5;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #f4f4f4 !important;
}

.form-group.has-error .fa-warning:before {
  content: '';
}

.form-group.has-error .input-icon.right > input {
  border-color: #ef2e37;
}

.form-group.has-error .input-icon.right > i.fa-warning {
  width: 12px;
  height: 12px;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -6px -58px !important;
}

.control-label .required {
  color: #ef2e37;
}

.has-error .control-label {
  color: #ef2e37;
}

.has-error .form-control,
.has-error .wmul {
  border-color: #ef2e37 !important;
}

.has-error .input-group-addon i {
  color: #ef2e37 !important;
}

.addon-droplist-box {
  box-shadow: 0px 0px 9px 0px rgba(13, 51, 93, 0.3);
}

.addon-droplist {
  color: #4c637b !important;
  line-height: 35px;
  padding: 0 10px;
}

.addon-droplist:hover {
  color: #009FE8 !important;
  background: #fff;
}

/*动态占位符控件*/

.dyncharparse .ui-widget .slick-cell {
  line-height: inherit !important;
}

.radio-list div.radio,
.radio-list div.radio span,
.radio-list div.radio input {
  width: 14px;
  height: 14px;
}

.radio-list {
  padding-top: 5px;
}

.radio-list div.radio span {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -6px -76px !important;
  -webkit-font-smoothing: antialiased;
  margin-top: 3px;
  float: left;
}

.radio-list div.radio span input {
  cursor: pointer;
}

.radio-list div.radio span.checked {
  background-position: -46px -76px !important;
}

.radio-list div.radio.disabled span {
  background-position: -26px -76px !important;
}

.radio-list div.radio.disabled span input {
  cursor: not-allowed;
}

.radio-list div.radio.disabled span.checked {
  background-position: -66px -76px !important;
}

.radio-list div.radio.disabled span.checked input {
  cursor: not-allowed;
}

.radio-list .radio-inline {
  padding-top: 0;
}

.radio-list .radio-inline .radio {
  float: left;
}

.checkbox-list div.checker,
.checkbox-list div.checker span,
.checkbox-list div.checker input {
  width: 14px;
  height: 14px;
}

.checkbox-list div.checker span,
.checkbox-inline div.checker span {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -86px -76px !important;
  -webkit-font-smoothing: antialiased;
}

.checkbox-list div.checker span.checked,
.checkbox-inline div.checker span.checked {
  background-position: -126px -76px !important;
}

.checkbox-list div.checker.disabled span,
.checkbox-inline div.checker.disabled span {
  background-position: -106px -76px !important;
}

.checkbox-list div.checker.disabled span.checked,
.checkbox-inline div.checker.disabled span.checked {
  background-position: -146px -76px !important;
}

.checkbox-inline {
  padding-left: 0;
  margin-left: 0;
  margin-right: 10px;
}

.checkbox-inline .checker {
  width: 15px !important;
  height: 15px !important;
}

.checkbox-inline .checker span {
  width: 15px !important;
  height: 15px !important;
}

.checkbox-inline .checker span input {
  width: 15px !important;
  height: 15px !important;
}

.form-group .col-md-7 .checkbox-inline,
.form-group .col-md-8 .checkbox-inline {
  padding-top: 5px !important;
}

/* checkbox开关switch样式 */

input.text-switch[type=checkbox] {
  display: none;
}

input.text-switch[type=checkbox] + label {
  display: inline-block;
  box-sizing: border-box;
  height: 16px;
  min-width: 32px;
  line-height: 18px;
  vertical-align: middle;
  border-radius: 100px;
  border: 1px solid transparent;
  background-color: #c6c6c6;
  background-image: none;
  cursor: pointer;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
  position: relative;
  margin-bottom: 0;
}

input.text-switch.edit-preclick[type=checkbox] + label {
  opacity: 0.7;
  cursor: unset;
}

input .text-switch.edit-preclick[type=checkbox]:checked + label,
input.text-switch[type=checkbox]:checked + label {
  background-color: #009fe8;
}

input.text-switch[type=checkbox]:disabled + label {
  cursor: not-allowed;
  background-color: #B2B2B2;
}

input.text-switch[type=checkbox]:checked:disabled + label {
  background-color: #009fe8;
  opacity: 0.5;
}

input.text-switch[type=checkbox] + label::before {
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 18px;
  background-color: #fff;
  cursor: pointer;
  transition: background-color 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-transition: background-color 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
  box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
}

input.text-switch[type=checkbox]:checked + label::before {
  left: 16px;
  transition: background-color 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-transition: background-color 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

input.text-switch[type=checkbox]:disabled + label::before {
  cursor: not-allowed;
  opacity: 0.4;
}

input.text-switch.edit-preclick[type=checkbox] + label::before {
  cursor: unset;
}

input.text-switch[type=checkbox] + label::after {
  content: "";
  position: absolute;
  top: 1px;
  left: 24px;
  font-size: 12px;
  color: #fff;
}

input.text-switch[type=checkbox]:checked + label::after {
  content: "";
  left: 5px;
}

.demo {
  background: black;
  opacity: 0.5;
  filter: alpha(opacity=50);
  margin: 0 auto;
  text-align: center;
  color: red;
  font-size: 20px;
  background-color: blue;
}

.grid-caption {
  border: solid 1px #E3E3E3;
  border-bottom: none;
  padding: 5px;
  font-weight: bold;
  font-size: 14px;
  color: #515151;
}

.grid-top-menu {
  border: solid 1px #E3E3E3;
  border-bottom: none;
  padding: 5px;
}

.grid-top-menu .btn-sm {
  padding: 4px 5px;
  font-size: 12px;
}

.slick-pager {
  width: 100%;
  padding-top: 7px;
  height: 34px;
  text-align: center;
  color: #333333;
  background: #ffffff;
  border: none;
  font-size: 12px;
  display: block;
}

.slick-pager .slick-pager-nav {
  float: inherit;
  padding: 0;
}

.slick-pager .slick-pager-nav .ui-icon-container {
  height: 26px;
  border-color: #CDD9E6;
  cursor: pointer;
  float: left;
}

.slick-pager .slick-pager-nav .slick-input-container {
  vertical-align: middle;
  margin: 2px 4px;
  float: left;
}

.slick-pager .slick-pager-nav .slick-input-container input {
  width: 50px;
  height: 26px;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
  text-align: center;
  margin: 0 4px;
}

.slick-pager .slick-pager-nav .slick-input-container input:focus {
  outline: none;
  border-color: #009FE8;
}

.slick-pager .slick-pager-nav .ui-icon {
  width: 24px;
  height: 24px;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  border-radius: 4px !important;
  margin-bottom: -10px;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-first {
  background-position: -107px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-first.ui-state-disabled {
  background-position: -43px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-prev {
  background-position: -139px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-prev.ui-state-disabled {
  background-position: -75px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-next {
  background-position: -171px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-next.ui-state-disabled {
  background-position: -235px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-end {
  background-position: -203px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-icon-seek-end.ui-state-disabled {
  background-position: -267px -247px !important;
}

.slick-pager .slick-pager-nav .ui-state-default .ui-state-disabled {
  cursor: not-allowed !important;
}

.slick-pager .slick-pager-nav .ui-state-disabled {
  display: block !important;
}

.slick-pager .slick-pager-settings {
  display: inline-block;
  padding: 0 3px 2px 3px;
  float: inherit;
}

.slick-pager .slick-pager-settings select {
  height: 26px;
  margin-top: 2px;
  border: 1px #D6DEE9 solid;
  border-radius: 4px !important;
  float: left;
}

.slick-pager .slick-pager-settings select:focus {
  outline: none;
  border-color: #009FE8;
}

.slick-pager .slick-pager-status tt {
  float: left;
}

table.ui-widget {
  max-width: 100% !important;
}

.ui-widget-header .ui-state-default {
  color: #333333;
}

.slick-header .slick-header-columns {
  border: none;
  background: #ECF0F2;
  color: #333333;
  font-size: 14px;
  font-family: SimSun;
  font-weight: bold;
  height: 42px;
}

.slick-header .slick-header-columns .slick-header-column {
  height: 42px;
  background: none;
  border-right: 1px solid #E3E3E3;
  line-height: 42px;
  text-align: center;
  font-family: "微软雅黑" !important;
  padding: 0;
}

.slick-header .slick-header-columns .slick-header-column .slick-sort-indicator-numbered {
  color: #009FE8;
}

.slick-header .slick-header-columns .slick-header-column.slick-header-sortable {
  cursor: pointer;
}

.slick-header .slick-header-columns .slick-header-column-sorted {
  font-style: normal;
}

.slick-header .slick-column-name {
  line-height: 42px;
  font-weight: bold;
}

.slick-header .slick-column-name input[type=checkbox] {
  opacity: 0;
  width: 14px;
  height: 14px;
  opacity: 0;
  position: absolute;
  margin-top: 13px;
  cursor: pointer;
}

.slick-header .slick-column-name input[type=checkbox] + label {
  margin-top: 13px;
  cursor: pointer;
  width: 14px;
  height: 14px;
  background-image: url("../../images/all-icon.png");
  background-repeat: no-repeat;
  background-size: auto;
  border-radius: 1px;
  background-position: -86px -76px !important;
}

.slick-header .slick-column-name input[type=checkbox]:checked + label {
  margin-top: 13px;
  width: 14px;
  height: 14px;
  background-image: url("../../images/all-icon.png");
  background-repeat: no-repeat;
  background-size: auto;
  border-radius: 1px;
  background-position: -126px -76px !important;
}

/*合并列头样式*/

.slick-preheader-panel .slick-header-columns {
  border-bottom: 1px solid #E3E3E3;
}

.slick-viewport .summary_list {
  height: 40px;
}

.slick-viewport .grid-canvas {
  box-sizing: content-box;
}

.slick-viewport .grid-canvas .slick-row {
  background: #fff;
}

.slick-viewport .grid-canvas.grid-canvas-right .slick-row {
  border-right: 1px solid #E3E3E3;
  box-sizing: content-box;
  margin-left: -1px;
}

.slick-viewport .grid-canvas div.ui-widget-content:hover {
  background-color: #DDEBFA !important;
}

.slick-viewport .grid-canvas div.ui-widget-content.active {
  background-color: #DDEBFA !important;
}

.slick-viewport .grid-canvas div.ui-widget-content.active .y-seq-cell {
  background-color: #DDEBFA !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .ui-icon {
  margin-top: 10px;
}

.slick-viewport .grid-canvas div.ui-widget-content .y-seq-cell {
  border-left: 1px #E3E3E3 solid !important;
  margin-left: -1px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell-checkboxsel {
  border-color: #E3E3E3;
  border-left: 1px #E3E3E3 solid !important;
  margin-left: -1px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell {
  line-height: 41px;
  font-size: 12px;
  font-family: ArialMT;
  padding: 0 4px;
  color: #333333;
  border: none;
  border-bottom: 1px solid #E3E3E3;
  /* checkbox开关switch样式 */
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .summary_list {
  width: fit-content;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .txt-record {
  display: inline-block;
  position: absolute;
  height: 12px;
  width: 12px;
  bottom: 1px;
  right: 1px;
  background-color: #AAA;
  border-top: 2px solid white;
  border-left: 2px solid white;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-edit-preclick-true {
  width: 14px;
  height: 14px;
  display: inline-block;
  margin-top: 12px;
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-repeat: no-repeat;
  background-size: auto;
  background-position: -126px -76px !important;
  opacity: .7;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-edit-preclick {
  width: 14px;
  height: 14px;
  display: inline-block;
  margin-top: 12px;
  border: 1px #D6DEE9 solid;
  background: #eee;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell input[type=checkbox] {
  width: 14px;
  height: 14px;
  opacity: 0;
  position: absolute;
  margin-top: 10px;
  cursor: pointer;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell input[type=checkbox]:disabled {
  cursor: not-allowed;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell input[type=checkbox] + label {
  width: 14px;
  height: 14px;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  left: 8px;
  top: 10px;
  width: 14px;
  height: 14px;
  background-image: url("../../images/all-icon.png");
  background-repeat: no-repeat;
  background-size: auto;
  border-radius: 1px;
  background-position: -86px -76px !important;
  margin-bottom: 6px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell input[type=checkbox]:checked + label {
  width: 14px;
  height: 14px;
  background-image: url("../../images/all-icon.png");
  background-repeat: no-repeat;
  background-size: auto;
  border-radius: 1px;
  background-position: -126px -76px !important;
  margin-bottom: 6px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell input[type=checkbox]:checked:disabled + label {
  cursor: not-allowed;
  background-position: -146px -76px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .ap-ctl-wrap {
  margin: 0;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.selected {
  background-color: #DDEBFA;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active {
  z-index: 100;
  background-color: #DDEBFA;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active .bd-select-icon {
  right: 5px;
  height: 31px;
  top: 5px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active .bd-select-icon i {
  margin-right: 3px;
  float: right;
  margin-top: 9px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active .bd-select-icon:hover i {
  color: #009FE8;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > .y-editor-text {
  border-radius: 4px !important;
  text-indent: 5px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > .y-editor-text[readonly] {
  border: 1px solid #bfbfbf !important;
  background: #eeeeee !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > .y-editor-text .ap-edit-icon {
  right: 0px;
  height: 94%;
  padding-left: 0;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active input.y-editor-text {
  height: 35px !important;
  border: 1px solid #009fe8;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active input.y-editor-text[readonly] {
  border: 1px solid #bfbfbf;
  background: #eeeeee;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > div {
  border-radius: 4px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > div .y-editor-text {
  border: 1px solid #009fe8 !important;
  border-radius: 4px !important;
  text-indent: 5px;
  background: #ffffff !important;
  line-height: 35px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > div .y-editor-text:focus {
  border: 1px solid #009fe8;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell.active > div .y-editor-text[readonly] {
  border: 1px solid #bfbfbf !important;
  background: #eeeeee !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .bill-img-show li img {
  margin-top: 3px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .w-quene {
  border-width: 2px;
  border: none;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .ui-icon-trash {
  margin-right: 2px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .y-sys-operate {
  cursor: pointer;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .y-sys-operate.ui-icon-plus {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -307px -75px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .y-sys-operate.ui-icon-trash {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -327px -75px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .toggle {
  height: 14px;
  width: 16px;
  display: inline-block;
  cursor: pointer;
  margin-top: 12px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .toggle.expand,
.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-group-toggle.expanded {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -206px -76px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .toggle.collapse,
.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-group-toggle.collapsed {
  background: url("../../images/all-icon.png") !important;
  background-repeat: no-repeat;
  background-position: -186px -76px !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-group-toggle.expanded,
.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .slick-group-toggle.collapsed {
  width: 14px;
  height: 14px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .uploader-start .uploader-style .queueList .w-quene .w-up.add .webuploader-pick {
  width: 45px;
  height: 45px;
  background: url("../../include/webuploader/images/add_img_normal.png");
  background-position: 0 !important;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .uploader-show .img-show li {
  margin: 0;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .uploader-show .img-show li img {
  width: auto;
  height: auto;
  max-width: 45px;
  max-height: 45px;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox] {
  display: none;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox] + label {
  display: inline-block;
  box-sizing: border-box;
  height: 16px;
  min-width: 32px;
  line-height: 18px;
  vertical-align: middle;
  border-radius: 100px;
  border: 1px solid transparent;
  background-color: #c6c6c6;
  background-image: none;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  position: relative;
  margin-bottom: 0;
  top: unset;
  left: unset;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch.edit-preclick[type=checkbox] + label {
  opacity: 0.7;
  cursor: unset;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input .text-switch.edit-preclick[type=checkbox]:checked + label,
.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:checked + label {
  background-color: #009fe8;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:disabled + label {
  cursor: not-allowed;
  background-color: #B2B2B2;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:checked:disabled + label {
  background-color: #009fe8;
  opacity: 0.5;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox] + label::before {
  content: "";
  display: block;
  width: 14px;
  height: 14px;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 18px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
  box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:checked + label::before {
  left: 16px;
  transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  -webkit-transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:disabled + label::before {
  cursor: not-allowed;
  opacity: 0.4;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch.edit-preclick[type=checkbox] + label::before {
  cursor: unset;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox] + label::after {
  content: "";
  position: absolute;
  top: 1px;
  left: 24px;
  font-size: 12px;
  color: #fff;
}

.slick-viewport .grid-canvas div.ui-widget-content .slick-cell .switch-case input.text-switch[type=checkbox]:checked + label::after {
  content: "";
  left: 5px;
}

.slick-viewport .grid-canvas div.slick-group .slick-cell {
  text-align: left !important;
  background: white;
  border-bottom: 1px solid #E3E3E3;
}

.slick-viewport .grid-canvas div.slick-group .slick-group-toggle {
  vertical-align: middle;
}

.slick-viewport .grid-canvas div.slick-group .slick-group-title {
  vertical-align: sub;
}

.slick-viewport .grid-canvas div.even {
  background: white;
}

.slick-viewport .grid-canvas div.old {
  background: #f8f9fd;
}

.ui-widget {
  display: block;
  border: 1px #E3E3E3 solid;
}

.ui-widget .slick-pane-left {
  z-index: 100;
}

.ui-widget .slick-pane-left .grid-canvas-left .slick-row {
  border-right: 1px #E3E3E3 solid;
}

.ui-widget .slick-pane .slick-header {
  border: none;
  border-bottom: 1px #E3E3E3 solid;
}

.ui-widget .slick-pane .slick-preheader-panel {
  border: 0 !important;
}

.ui-widget .slick-pane .slick-footerrow {
  background: #fff;
}

.ui-widget .slick-pane .slick-footerrow .slick-footerrow-columns {
  height: 26px !important;
}

.ui-widget .slick-pane .slick-headerrow {
  border-color: #E3E3E3;
  width: 100% !important;
  border-top: 0;
  border-right: 0;
}

.ui-widget .slick-pane .slick-headerrow .slick-headerrow-columns .slick-headerrow-column {
  border: 0;
  border-right: 1px solid #E3E3E3;
  background: #ECF0F2;
  padding: 6px;
}

.ui-widget .slick-pane .slick-headerrow .slick-headerrow-columns .slick-headerrow-column input {
  border: 1px solid #cccccc;
}

.ui-widget .slick-pane .slick-headerrow .slick-headerrow-columns .slick-headerrow-column input:focus {
  border-color: #009FE8;
}

.ui-widget .slick-pane-right {
  /*border-left: 1px $slic-border-color solid;*/
}

.page-search-pane {
  border: 1px #E3E3E3 solid;
  border-right: 0;
}

.slick-footerrow .ui-state-default {
  background: white;
}

.slick-footerrow .slick-footerrow-column.ui-state-default {
  border-right: 1px solid #E3E3E3;
}

.slick-sort-indicator-numbered {
  position: absolute;
  left: 0;
}

.slick-headerrow.ui-state-default,
.slick-header.ui-state-default {
  background: #ECF0F2;
  overflow: hidden;
}

.slick-gridmenu {
  max-height: 300px !important;
  resize: none;
  border-color: #E3E3E3;
  background: #fff;
  box-shadow: none;
  padding: 0;
}

.slick-gridmenu .slick-gridmenu-custom .title {
  font-size: 14px;
  width: 100%;
  padding-left: 20px;
  padding-bottom: 0;
  line-height: 30px;
  border-color: #E3E3E3;
  background: #fff;
  font-weight: bold;
}

.slick-gridmenu .slick-gridmenu-custom .slick-gridmenu-icon {
  margin-right: 0;
}

.slick-gridmenu .slick-gridmenu-custom .slick-gridmenu-item {
  border: 0;
}

.slick-gridmenu .slick-gridmenu-custom .slick-gridmenu-item:hover {
  border: 0;
  background: #eee;
}

.slick-gridmenu button.close {
  background-image: none !important;
  width: 15px;
  height: 15px;
  margin-top: 2px;
  margin-right: 10px;
  opacity: 1;
}

.slick-gridmenu button.close span.close {
  background-image: none !important;
  color: #707070;
  text-indent: 0;
  opacity: 1;
  font-size: 18px;
}

.slick-gridmenu-button {
  width: 18px;
  border-bottom: 1px #E3E3E3 solid;
  border-left: 1px #E3E3E3 solid;
  background-color: #fff;
  padding: 12px 2px 11.5px 2px;
  margin-top: 0;
}

.slick-gridmenu-button img {
  margin-top: -4px;
}

/* SlickGrid 样式*/

/*单元格中文本编辑器样式*/

input.y-editor-text {
  width: 100%;
  height: 100%;
  margin: 0;
  outline: 0;
  padding: 0;
}

input.y-editor-text[readonly] {
  background: #f4f4f4 !important;
}

/*表格列头和单元格样式*/

.y-head-left,
.y-cell-left {
  text-align: left !important;
}

.y-head-center,
.y-cell-center,
.y-seq-cell,
.y-toggle-cell {
  text-align: center !important;
}

.y-head-right,
.y-cell-right,
.y-operate-cell {
  text-align: right !important;
}

.y-operate-cell,
.y-toggle-cell {
  border-left: 1px #E3E3E3 solid !important;
  margin-left: -1px;
}

.y-sys-operate {
  padding: 0px 2px;
  margin-left: 1px;
  float: right;
}

/*表格序号列样式*/

/*表格复选框列样式*/

.slick-cell-checkboxsel {
  text-align: center;
}

/*表格快捷过滤样式*/

input.y-quick-text {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  outline: 0 !important;
  padding: 0px 5px !important;
}

/*表格文本框按钮样式*/

.y-field-button {
  background: #eeeeee;
}

/*单据图片样式设置*/

.slick-viewport.slick-viewport-top .uploader-style .w-quene li.w-pic img {
  width: auto;
  height: auto;
  max-width: 45px;
  max-height: 45px;
}

.slick-viewport.slick-viewport-top .uploader-style .w-quene li.w-pic {
  width: 45px;
  height: 45px;
}

.slick-viewport.slick-viewport-top .uploader-style .w-quene li.w-up {
  width: 45px;
  height: 45px;
}

.slick-viewport.slick-viewport-top .uploader-style .w-quene li.w-up .webuploader-pick {
  width: 45px;
  height: 45px;
  background-size: 45px 45px !important;
}

.bill-img-show li {
  float: left;
}

.bill-img-show li img {
  max-width: 45px;
  max-height: 45px;
}

.slick-boxshadow {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
}

.btn-entry {
  background-color: #009FE8;
  border: 0;
}

.btn-entry:hover {
  background-color: #33B2ED;
}

/*表格快捷过滤复选框样式*/

input.y-quick-checkbox {
  position: absolute;
  width: 14px;
  height: 14px;
  opacity: 0;
  margin-top: 6px;
}

.slick-headerrow .slick-headerrow-columns .slick-headerrow-column input[type="checkbox"] + label {
  width: 14px;
  height: 14px;
  display: inline-block;
  cursor: pointer;
  border-radius: 1px;
  margin-top: 6px;
  width: 14px;
  height: 14px;
  background-image: url("../../images/all-icon.png");
  background-repeat: no-repeat;
  background-size: auto;
  border-radius: 1px;
}

.slick-headerrow .slick-headerrow-columns .slick-headerrow-column .default + label {
  background-position: -166px -76px !important;
}

.slick-headerrow .slick-headerrow-columns .slick-headerrow-column .checked + label {
  background-position: -126px -76px !important;
}

.slick-headerrow .slick-headerrow-columns .slick-headerrow-column .unchecked + label {
  background-position: -86px -76px !important;
}

.uploader-start .download-all {
  position: absolute;
  right: 30px;
  top: 3px;
  color: #4C637B;
  cursor: pointer;
}

.uploader-start .download-all:hover {
  color: #009FE8;
}

.uploader-start .glyphicon {
  font-size: 12px;
}

.uploader-start .uploader-style .w-panel {
  display: none;
}

.viewer-title {
  overflow: initial;
}

.ppsign .sign-show {
  width: 100%;
  height: 80px;
  border: 1px solid #D6DEE9;
  border-radius: 4px !important;
  background: #fff;
}

.ppsign .sign-show .sign {
  padding: 5px;
  float: right;
  cursor: pointer;
  background: #009FE8;
  color: white;
  border-bottom-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
}

.ppsign .sign-show .sign:hover {
  background: #33B2ED;
}

.ppsign .sign-show img {
  width: 100px;
  height: 78px;
  max-width: 100px;
  max-height: 78px;
}

.ppsign-box .ppsign-content {
  margin-bottom: 15px;
  height: 320px;
}

.ppsign-box .ppsign-action {
  height: 35px;
  line-height: 35px;
}

.ppsign-box .ppsign-action .device {
  display: none;
  width: 220px;
}

.ppsign-box .ppsign-action .down {
  display: none;
  margin-left: 6px;
}

.ppsign-box .ppsign-action .hint {
  float: right;
  color: #999999;
}

.colorfield .color-box {
  display: none;
}

.colorfield .sp-replacer {
  padding: 0;
  border: 0;
  border-radius: 4px !important;
}

.colorfield .sp-replacer .sp-preview {
  width: 28px;
  height: 28px;
  border: 0;
  margin-right: 0;
}

.colorfield-cell-value {
  width: 28px;
  height: 28px;
  display: inline-block;
  border-radius: 4px !important;
  vertical-align: middle;
}